2025-07-29 08:56:58.845 |  | INFO     | server:lifespan:69 - RuoYi-FastAPI开始启动
2025-07-29 08:56:58.846 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-29 08:57:00.568 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-29 08:57:00.568 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-29 08:57:00.571 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-29 08:57:01.207 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-29 08:57:01.778 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-29 08:57:01.779 |  | INFO     | server:lifespan:76 - RuoYi-FastAPI启动成功
2025-07-29 08:57:05.299 | fbf332212554480c806af86111646657 | INFO     | module_admin.controller.captcha_controller:get_captcha_image:34 - 编号为cf32776b-eb3b-49fa-b52d-8243e7068917的会话获取图片验证码成功
2025-07-29 08:57:09.465 | 5a63cb8a7f5a4ea9a5f19f3872bd7d20 | INFO     | module_admin.controller.login_controller:login:71 - 登录成功
2025-07-29 08:57:09.713 | 80b0556991464811aa5ff5ae550a97ac | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-29 08:57:10.233 | ff23ac514bc048b8ba912394f36634a1 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-29 08:57:10.730 | d708812c1d3e442cade7353374deb5d1 | INFO     | module_bill.service.bill_service:get_user_membership_info:116 - 用户1的VIP等级: 1, 会员状态: VIP1会员
2025-07-29 08:57:10.793 | d708812c1d3e442cade7353374deb5d1 | INFO     | module_bill.service.bill_service:get_user_membership_info:150 - 用户订单套餐: 企业版, 到期时间: 2025-07-19 10:30:00
2025-07-29 08:57:10.793 | d708812c1d3e442cade7353374deb5d1 | INFO     | module_opinion.controller.dashboard_controller:get_user_dashboard_info:240 - 获取用户Dashboard信息成功，用户ID: 1
2025-07-29 08:57:11.053 | c7ec44c251ff41d0a6f6be4140d0f3ab | INFO     | module_opinion.dao.dashboard_dao:get_recent_analysis_records:110 - 成功获取最新3条分析记录（从opinion_task表），用户ID: 1
2025-07-29 08:57:11.084 | c7ec44c251ff41d0a6f6be4140d0f3ab | INFO     | module_opinion.dao.dashboard_dao:get_analysis_records_count:152 - 分析记录总数（opinion_task表），用户ID: 1: 25
2025-07-29 08:57:11.084 | c7ec44c251ff41d0a6f6be4140d0f3ab | INFO     | module_opinion.service.dashboard_service:get_recent_analysis_records_services:51 - 成功获取最新3条分析记录，用户ID: 1，总记录数: 25
2025-07-29 08:57:11.085 | c7ec44c251ff41d0a6f6be4140d0f3ab | INFO     | module_opinion.controller.dashboard_controller:get_recent_analysis_records:43 - 获取最新分析记录成功，用户ID: 1，返回3条记录
2025-07-29 08:57:11.195 | 32bfdac921d9441e8a164ad6eb0f174b | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:69 - === Dashboard统计API被调用，用户ID: 1 ===
2025-07-29 08:57:11.195 | 32bfdac921d9441e8a164ad6eb0f174b | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:72 - === Dashboard Service: 开始获取统计数据 ===
2025-07-29 08:57:11.195 | 32bfdac921d9441e8a164ad6eb0f174b | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:169 - === Dashboard DAO: 开始获取统计数据 ===
2025-07-29 08:57:11.196 | 32bfdac921d9441e8a164ad6eb0f174b | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:179 - === Dashboard DAO: 时间范围 - 今日: 2025-07-29 00:00:00 到 2025-07-29 23:59:59.999999 ===
2025-07-29 08:57:11.196 | 32bfdac921d9441e8a164ad6eb0f174b | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:180 - === Dashboard DAO: 时间范围 - 昨日: 2025-07-28 00:00:00 到 2025-07-28 23:59:59.999999 ===
2025-07-29 08:57:11.198 | 32bfdac921d9441e8a164ad6eb0f174b | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:196 - === Dashboard DAO: 今日任务查询SQL: SELECT count(opinion_task.id) AS count_1 
FROM opinion_task 
WHERE opinion_task.create_time >= :create_time_1 AND opinion_task.create_time <= :create_time_2 AND opinion_task.is_enabled = :is_enabled_1 AND opinion_task.user_id = :user_id_1 ===
2025-07-29 08:57:11.232 | 32bfdac921d9441e8a164ad6eb0f174b | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:200 - === Dashboard DAO: 今日任务数量: 0 ===
2025-07-29 08:57:11.399 | 32bfdac921d9441e8a164ad6eb0f174b | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:406 - === 开始获取用户分析统计数据，用户ID: 1 ===
2025-07-29 08:57:11.693 | 32bfdac921d9441e8a164ad6eb0f174b | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:445 - === 用户1的opinion_requirement记录总数: 67 ===
2025-07-29 08:57:11.694 | 32bfdac921d9441e8a164ad6eb0f174b | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:446 - === 套餐有效期信息: {'has_valid_package': True, 'start_time': datetime.datetime(2025, 7, 17, 10, 30), 'end_time': datetime.datetime(2025, 7, 29, 8, 57, 11, 659345), 'package_name': '企业版', 'is_expired': True} ===
2025-07-29 08:57:11.731 | 32bfdac921d9441e8a164ad6eb0f174b | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:462 - === 用户1今日的opinion_requirement记录数: 0 ===
2025-07-29 08:57:11.732 | 32bfdac921d9441e8a164ad6eb0f174b | INFO     | module_opinion.dao.dashboard_dao:get_user_package_info:526 - === 开始获取用户套餐信息，用户ID: 1 ===
2025-07-29 08:57:11.766 | 32bfdac921d9441e8a164ad6eb0f174b | INFO     | config.package_config:get_package_by_vip_level:64 - VIP等级 1 对应套餐: {'name': 'VIP1', 'limit': 100, 'description': 'VIP1会员套餐', 'membership_status': 'VIP1会员'}
2025-07-29 08:57:11.766 | 32bfdac921d9441e8a164ad6eb0f174b | INFO     | module_opinion.dao.dashboard_dao:_get_package_from_vip_level:703 - === 根据VIP等级 1 确定套餐: {'name': 'VIP1', 'limit': 100, 'description': 'VIP1会员套餐', 'membership_status': 'VIP1会员'} ===
2025-07-29 08:57:11.767 | 32bfdac921d9441e8a164ad6eb0f174b | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:494 - === 用户分析统计数据: {'total_analysis': 67, 'today_analysis': 0, 'remaining_count': 33, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 67.0} ===
2025-07-29 08:57:11.767 | 32bfdac921d9441e8a164ad6eb0f174b | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:361 - === Dashboard DAO: 最终统计数据: {'today_tasks': 0, 'tasks_growth': 0.0, 'negative_count': 0, 'negative_change': 0.0, 'completed_count': 12, 'completed_change': 100.0, 'pending_count': 13, 'pending_change': 100.0, 'total_count': 25, 'running_count': 0, 'failed_count': 0, 'total_analysis': 67, 'today_analysis': 0, 'remaining_count': 33, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 67.0} ===
2025-07-29 08:57:11.767 | 32bfdac921d9441e8a164ad6eb0f174b | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:362 - === Dashboard DAO: 成功获取Dashboard统计数据 ===
2025-07-29 08:57:11.768 | 32bfdac921d9441e8a164ad6eb0f174b | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:76 - === Dashboard Service: 从DAO获取的原始数据: {'today_tasks': 0, 'tasks_growth': 0.0, 'negative_count': 0, 'negative_change': 0.0, 'completed_count': 12, 'completed_change': 100.0, 'pending_count': 13, 'pending_change': 100.0, 'total_count': 25, 'running_count': 0, 'failed_count': 0, 'total_analysis': 67, 'today_analysis': 0, 'remaining_count': 33, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 67.0} ===
2025-07-29 08:57:11.768 | 32bfdac921d9441e8a164ad6eb0f174b | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:80 - === Dashboard Service: 转换后的模型数据: today_tasks=0 tasks_growth=0.0 negative_count=0 negative_change=0.0 completed_count=12 completed_change=100.0 pending_count=13 pending_change=100.0 total_count=25 running_count=0 failed_count=0 total_analysis=67 today_analysis=0 remaining_count=33 today_remaining=3 package_name='VIP1' package_limit=100 usage_percentage=67.0 ===
2025-07-29 08:57:11.769 | 32bfdac921d9441e8a164ad6eb0f174b | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:82 - === Dashboard Service: 成功获取Dashboard统计数据 ===
2025-07-29 08:57:11.769 | 32bfdac921d9441e8a164ad6eb0f174b | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:74 - === 从Service获取的统计数据: today_tasks=0 tasks_growth=0.0 negative_count=0 negative_change=0.0 completed_count=12 completed_change=100.0 pending_count=13 pending_change=100.0 total_count=25 running_count=0 failed_count=0 total_analysis=67 today_analysis=0 remaining_count=33 today_remaining=3 package_name='VIP1' package_limit=100 usage_percentage=67.0 ===
2025-07-29 08:57:11.769 | 32bfdac921d9441e8a164ad6eb0f174b | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:75 - === 统计数据类型: <class 'module_opinion.entity.vo.dashboard_vo.DashboardStatisticsModel'> ===
2025-07-29 08:57:11.770 | 32bfdac921d9441e8a164ad6eb0f174b | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:79 - === 转换后的字典数据: {'today_tasks': 0, 'tasks_growth': 0.0, 'negative_count': 0, 'negative_change': 0.0, 'completed_count': 12, 'completed_change': 100.0, 'pending_count': 13, 'pending_change': 100.0, 'total_count': 25, 'running_count': 0, 'failed_count': 0, 'total_analysis': 67, 'today_analysis': 0, 'remaining_count': 33, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 67.0} ===
2025-07-29 08:57:11.770 | 32bfdac921d9441e8a164ad6eb0f174b | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:81 - === Dashboard统计数据获取成功 ===
2025-07-29 08:57:11.771 | 32bfdac921d9441e8a164ad6eb0f174b | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:84 - === 最终响应数据: <starlette.responses.JSONResponse object at 0x0000019F294158C0> ===
2025-07-29 08:57:14.422 | e1b8af191121496588692c69763e8b41 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:478 - [用户认证] 当前用户对象: permissions=['*:*:*'] roles=['admin'] user=UserInfoModel(user_id=1, dept_id=103, user_name='admin', nick_name='超级管理员', user_type='00', email='<EMAIL>', phonenumber='***********', sex='1', avatar='', password='$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', status='0', del_flag='0', login_ip='127.0.0.1', login_date=datetime.datetime(2025, 7, 29, 8, 57, 9), create_by='admin', create_time=datetime.datetime(2025, 7, 18, 9, 44, 47), update_by='', update_time=None, remark='管理员', admin=True, vip_level=1, account_balance=0.0, total_spent=0.0, last_payment_time=None, post_ids='1', role_ids='1', dept=DeptModel(dept_id=103, parent_id=101, ancestors='0,100,101', dept_name='研发部门', order_num=1, leader='年糕', phone='***********', email='<EMAIL>', status='0', del_flag='0', create_by='admin', create_time=datetime.datetime(2025, 5, 9, 9, 44, 46), update_by='', update_time=None), role=[RoleModel(role_id=1, role_name='超级管理员', role_key='admin', role_sort=1, data_scope='1', menu_check_strictly=True, dept_check_strictly=True, status='0', del_flag='0', create_by='admin', create_time=datetime.datetime(2025, 5, 9, 9, 44, 47), update_by='', update_time=None, remark='超级管理员', admin=True)])
2025-07-29 08:57:14.422 | e1b8af191121496588692c69763e8b41 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:479 - [用户认证] 用户ID: 1 (类型: <class 'int'>)
2025-07-29 08:57:14.423 | e1b8af191121496588692c69763e8b41 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:480 - [用户认证] 用户名: admin
2025-07-29 08:57:14.423 | e1b8af191121496588692c69763e8b41 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:492 - [查询参数] 原始查询对象: page_num=1 page_size=10 requirement_id=None user_id=None task_name=None task_type=None task_description=None frequency=None status=None is_enabled=None create_time_start=None create_time_end=None
2025-07-29 08:57:14.424 | e1b8af191121496588692c69763e8b41 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:493 - [查询参数] 原始user_id: None
2025-07-29 08:57:14.424 | e1b8af191121496588692c69763e8b41 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:500 - [查询参数] 安全查询对象user_id: 1
2025-07-29 08:57:14.425 | e1b8af191121496588692c69763e8b41 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:501 - [查询参数] 确认用户数据隔离 - 只查询用户ID为1的数据
2025-07-29 08:57:14.425 | e1b8af191121496588692c69763e8b41 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:32 - [DAO查询] 开始构建舆情任务查询条件
2025-07-29 08:57:14.426 | e1b8af191121496588692c69763e8b41 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:33 - [DAO查询] 查询对象: page_num=1 page_size=10 requirement_id=None user_id=1 task_name=None task_type=None task_description=None frequency=None status=None is_enabled=None create_time_start=None create_time_end=None
2025-07-29 08:57:14.426 | e1b8af191121496588692c69763e8b41 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:34 - [DAO查询] 用户ID: 1 (类型: <class 'int'>)
2025-07-29 08:57:14.426 | e1b8af191121496588692c69763e8b41 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:35 - [DAO查询] 使用物理删除，无需软删除过滤条件
2025-07-29 08:57:14.426 | e1b8af191121496588692c69763e8b41 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:44 - [DAO查询] 添加用户ID过滤条件: 1 - 这是用户数据隔离的关键
2025-07-29 08:57:14.427 | e1b8af191121496588692c69763e8b41 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:107 - 🔍 未应用任何筛选条件，将返回全部数据
2025-07-29 08:57:14.430 | e1b8af191121496588692c69763e8b41 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:113 - [DAO查询] 最终查询SQL: SELECT opinion_task.id, opinion_task.requirement_id, opinion_task.user_id, opinion_task.task_name, opinion_task.task_description, opinion_task.task_type, opinion_task.frequency, opinion_task.execute_time, opinion_task.cron_expression, opinion_task.status, opinion_task.push_url, opinion_task.push_config, opinion_task.last_execute_time, opinion_task.next_execute_time, opinion_task.execute_count, opinion_task.success_count, opinion_task.fail_count, opinion_task.is_enabled, opinion_task.create_time, opinion_task.update_time, opinion_task.create_by, opinion_task.update_by, opinion_task.remark, opinion_task.report_oss_url, opinion_task.positive_count, opinion_task.negative_count, opinion_task.neutral_count 
FROM opinion_task 
WHERE opinion_task.user_id = :user_id_1 ORDER BY opinion_task.create_time DESC
2025-07-29 08:57:14.430 | e1b8af191121496588692c69763e8b41 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:117 - [DAO查询] 执行分页查询 - 页码: 1, 页大小: 10
2025-07-29 08:57:14.497 | e1b8af191121496588692c69763e8b41 | INFO     | utils.page_util:paginate:89 - 🔍 PageUtil.paginate 返回结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-29 08:57:14.497 | e1b8af191121496588692c69763e8b41 | INFO     | utils.page_util:paginate:90 - 🔍 records数量: 10
2025-07-29 08:57:14.498 | e1b8af191121496588692c69763e8b41 | INFO     | utils.page_util:paginate:91 - 🔍 total: 35
2025-07-29 08:57:14.498 | e1b8af191121496588692c69763e8b41 | INFO     | module_opinion.dao.opinion_task_dao:get_opinion_task_list:119 - [DAO查询] 分页查询结果 - 总记录数: 35, 当前页记录数: 10
2025-07-29 08:57:14.499 | e1b8af191121496588692c69763e8b41 | INFO     | module_opinion.controller.opinion_analysis_controller:get_task_list:509 - [查询结果] 用户ID为1的舆情任务列表查询成功，共35条记录
2025-07-29 08:57:16.629 | 7edb6b9b20154ec58599d07892c07b81 | INFO     | module_opinion.controller.opinion_analysis_controller:get_public_keyword_categories:123 - 获取关键词分类列表成功
2025-07-29 08:57:20.034 | d141ed6b50164713bea87a0af15e1159 | INFO     | module_opinion.controller.opinion_template_controller:get_templates_for_selection:47 - 获取模板选择列表成功
2025-07-29 08:57:21.441 | 8766252488224758a6514f8a87aea14b | INFO     | module_opinion.controller.opinion_template_controller:update_template_usage:155 - 更新模板使用次数成功: 1
2025-07-29 08:57:26.076 | 1a5f4d9b5a124586a00392b9e99f6403 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:69 - === Dashboard统计API被调用，用户ID: 1 ===
2025-07-29 08:57:26.077 | 1a5f4d9b5a124586a00392b9e99f6403 | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:72 - === Dashboard Service: 开始获取统计数据 ===
2025-07-29 08:57:26.077 | 1a5f4d9b5a124586a00392b9e99f6403 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:169 - === Dashboard DAO: 开始获取统计数据 ===
2025-07-29 08:57:26.077 | 1a5f4d9b5a124586a00392b9e99f6403 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:179 - === Dashboard DAO: 时间范围 - 今日: 2025-07-29 00:00:00 到 2025-07-29 23:59:59.999999 ===
2025-07-29 08:57:26.077 | 1a5f4d9b5a124586a00392b9e99f6403 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:180 - === Dashboard DAO: 时间范围 - 昨日: 2025-07-28 00:00:00 到 2025-07-28 23:59:59.999999 ===
2025-07-29 08:57:26.079 | 1a5f4d9b5a124586a00392b9e99f6403 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:196 - === Dashboard DAO: 今日任务查询SQL: SELECT count(opinion_task.id) AS count_1 
FROM opinion_task 
WHERE opinion_task.create_time >= :create_time_1 AND opinion_task.create_time <= :create_time_2 AND opinion_task.is_enabled = :is_enabled_1 AND opinion_task.user_id = :user_id_1 ===
2025-07-29 08:57:26.109 | 1a5f4d9b5a124586a00392b9e99f6403 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:200 - === Dashboard DAO: 今日任务数量: 0 ===
2025-07-29 08:57:26.258 | 1a5f4d9b5a124586a00392b9e99f6403 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:406 - === 开始获取用户分析统计数据，用户ID: 1 ===
2025-07-29 08:57:26.325 | 1a5f4d9b5a124586a00392b9e99f6403 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:445 - === 用户1的opinion_requirement记录总数: 67 ===
2025-07-29 08:57:26.326 | 1a5f4d9b5a124586a00392b9e99f6403 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:446 - === 套餐有效期信息: {'has_valid_package': True, 'start_time': datetime.datetime(2025, 7, 17, 10, 30), 'end_time': datetime.datetime(2025, 7, 29, 8, 57, 26, 297001), 'package_name': '企业版', 'is_expired': True} ===
2025-07-29 08:57:26.354 | 1a5f4d9b5a124586a00392b9e99f6403 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:462 - === 用户1今日的opinion_requirement记录数: 0 ===
2025-07-29 08:57:26.355 | 1a5f4d9b5a124586a00392b9e99f6403 | INFO     | module_opinion.dao.dashboard_dao:get_user_package_info:526 - === 开始获取用户套餐信息，用户ID: 1 ===
2025-07-29 08:57:26.386 | 1a5f4d9b5a124586a00392b9e99f6403 | INFO     | config.package_config:get_package_by_vip_level:64 - VIP等级 1 对应套餐: {'name': 'VIP1', 'limit': 100, 'description': 'VIP1会员套餐', 'membership_status': 'VIP1会员'}
2025-07-29 08:57:26.387 | 1a5f4d9b5a124586a00392b9e99f6403 | INFO     | module_opinion.dao.dashboard_dao:_get_package_from_vip_level:703 - === 根据VIP等级 1 确定套餐: {'name': 'VIP1', 'limit': 100, 'description': 'VIP1会员套餐', 'membership_status': 'VIP1会员'} ===
2025-07-29 08:57:26.387 | 1a5f4d9b5a124586a00392b9e99f6403 | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:494 - === 用户分析统计数据: {'total_analysis': 67, 'today_analysis': 0, 'remaining_count': 33, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 67.0} ===
2025-07-29 08:57:26.388 | 1a5f4d9b5a124586a00392b9e99f6403 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:361 - === Dashboard DAO: 最终统计数据: {'today_tasks': 0, 'tasks_growth': 0.0, 'negative_count': 0, 'negative_change': 0.0, 'completed_count': 12, 'completed_change': 100.0, 'pending_count': 13, 'pending_change': 100.0, 'total_count': 25, 'running_count': 0, 'failed_count': 0, 'total_analysis': 67, 'today_analysis': 0, 'remaining_count': 33, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 67.0} ===
2025-07-29 08:57:26.389 | 1a5f4d9b5a124586a00392b9e99f6403 | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:362 - === Dashboard DAO: 成功获取Dashboard统计数据 ===
2025-07-29 08:57:26.389 | 1a5f4d9b5a124586a00392b9e99f6403 | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:76 - === Dashboard Service: 从DAO获取的原始数据: {'today_tasks': 0, 'tasks_growth': 0.0, 'negative_count': 0, 'negative_change': 0.0, 'completed_count': 12, 'completed_change': 100.0, 'pending_count': 13, 'pending_change': 100.0, 'total_count': 25, 'running_count': 0, 'failed_count': 0, 'total_analysis': 67, 'today_analysis': 0, 'remaining_count': 33, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 67.0} ===
2025-07-29 08:57:26.389 | 1a5f4d9b5a124586a00392b9e99f6403 | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:80 - === Dashboard Service: 转换后的模型数据: today_tasks=0 tasks_growth=0.0 negative_count=0 negative_change=0.0 completed_count=12 completed_change=100.0 pending_count=13 pending_change=100.0 total_count=25 running_count=0 failed_count=0 total_analysis=67 today_analysis=0 remaining_count=33 today_remaining=3 package_name='VIP1' package_limit=100 usage_percentage=67.0 ===
2025-07-29 08:57:26.390 | 1a5f4d9b5a124586a00392b9e99f6403 | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:82 - === Dashboard Service: 成功获取Dashboard统计数据 ===
2025-07-29 08:57:26.390 | 1a5f4d9b5a124586a00392b9e99f6403 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:74 - === 从Service获取的统计数据: today_tasks=0 tasks_growth=0.0 negative_count=0 negative_change=0.0 completed_count=12 completed_change=100.0 pending_count=13 pending_change=100.0 total_count=25 running_count=0 failed_count=0 total_analysis=67 today_analysis=0 remaining_count=33 today_remaining=3 package_name='VIP1' package_limit=100 usage_percentage=67.0 ===
2025-07-29 08:57:26.390 | 1a5f4d9b5a124586a00392b9e99f6403 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:75 - === 统计数据类型: <class 'module_opinion.entity.vo.dashboard_vo.DashboardStatisticsModel'> ===
2025-07-29 08:57:26.391 | 1a5f4d9b5a124586a00392b9e99f6403 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:79 - === 转换后的字典数据: {'today_tasks': 0, 'tasks_growth': 0.0, 'negative_count': 0, 'negative_change': 0.0, 'completed_count': 12, 'completed_change': 100.0, 'pending_count': 13, 'pending_change': 100.0, 'total_count': 25, 'running_count': 0, 'failed_count': 0, 'total_analysis': 67, 'today_analysis': 0, 'remaining_count': 33, 'today_remaining': 3, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 67.0} ===
2025-07-29 08:57:26.391 | 1a5f4d9b5a124586a00392b9e99f6403 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:81 - === Dashboard统计数据获取成功 ===
2025-07-29 08:57:26.392 | 1a5f4d9b5a124586a00392b9e99f6403 | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:84 - === 最终响应数据: <starlette.responses.JSONResponse object at 0x0000019F2A792C60> ===
2025-07-29 08:57:26.782 | 55612d8bd29b44e69ba8a4d22faa31a1 | INFO     | module_opinion.controller.opinion_analysis_controller:create_public_requirement:145 - 创建舆情需求成功
2025-07-29 08:57:27.257 | e378eb4169a04c1487c34a385d742516 | INFO     | module_opinion.controller.related_keywords_controller:generate_related_keywords:55 - 用户 admin 请求生成关联词，需求内容长度: 60
2025-07-29 08:57:27.257 | e378eb4169a04c1487c34a385d742516 | INFO     | module_opinion.service.related_keywords_service:generate_related_keywords_services:67 - 开始生成关联词，需求内容长度: 60, 最大数量: 20, 用户ID: 1
2025-07-29 08:57:41.097 | e378eb4169a04c1487c34a385d742516 | INFO     | module_opinion.service.external_api_service:_call_ark_api:532 - 豆包AI API调用成功
2025-07-29 08:57:41.098 | e378eb4169a04c1487c34a385d742516 | INFO     | module_opinion.service.related_keywords_service:_generate_keywords_by_ai:119 - AI返回的原始内容: ["品牌口碑", "产品质量", "用户反馈", "好评分析", "投诉监测", "服务态度", "性价比", "消费体验", "负面评价", "退换货", "用户满意度", "客服响应", "价格投诉", "产品缺陷", "售后服务", "差评追踪", "促销活动", "质量检测", "定价策略", "服务时效"]...
2025-07-29 08:57:41.098 | e378eb4169a04c1487c34a385d742516 | INFO     | module_opinion.service.related_keywords_service:_parse_ai_keywords_response:161 - 成功解析AI生成的关联词: 20 个
2025-07-29 08:57:41.099 | e378eb4169a04c1487c34a385d742516 | INFO     | module_opinion.service.related_keywords_service:_deduct_package_usage_for_keywords:254 - 开始为用户1的关键词生成功能扣减套餐次数
2025-07-29 08:57:41.216 | e378eb4169a04c1487c34a385d742516 | INFO     | module_opinion.service.related_keywords_service:_deduct_package_usage_for_keywords:274 - 成功为用户1创建关键词生成记录，ID: 268
2025-07-29 08:57:41.217 | e378eb4169a04c1487c34a385d742516 | INFO     | module_opinion.service.related_keywords_service:generate_related_keywords_services:89 - 关联词生成成功，共生成 20 个关联词
2025-07-29 08:57:41.217 | e378eb4169a04c1487c34a385d742516 | INFO     | module_opinion.controller.related_keywords_controller:generate_related_keywords:75 - 关联词生成成功，共生成 20 个关联词
2025-07-29 08:58:23.745 | b773c3eafe21439e85e98f1233f21c69 | INFO     | utils.page_util:paginate:89 - 🔍 PageUtil.paginate 返回结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-29 08:58:23.745 | b773c3eafe21439e85e98f1233f21c69 | INFO     | utils.page_util:paginate:90 - 🔍 records数量: 2
2025-07-29 08:58:23.745 | b773c3eafe21439e85e98f1233f21c69 | INFO     | utils.page_util:paginate:91 - 🔍 total: 2
2025-07-29 08:58:23.746 | b773c3eafe21439e85e98f1233f21c69 | INFO     | module_opinion.controller.opinion_analysis_controller:get_public_data_source_list:1119 - 获取需求数据源列表成功（公开接口）
2025-07-29 08:58:25.642 | 86daee90cd0045f4aebeb1d2ed7d24cc | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:69 - === Dashboard统计API被调用，用户ID: 1 ===
2025-07-29 08:58:25.642 | 86daee90cd0045f4aebeb1d2ed7d24cc | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:72 - === Dashboard Service: 开始获取统计数据 ===
2025-07-29 08:58:25.643 | 86daee90cd0045f4aebeb1d2ed7d24cc | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:169 - === Dashboard DAO: 开始获取统计数据 ===
2025-07-29 08:58:25.643 | 86daee90cd0045f4aebeb1d2ed7d24cc | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:179 - === Dashboard DAO: 时间范围 - 今日: 2025-07-29 00:00:00 到 2025-07-29 23:59:59.999999 ===
2025-07-29 08:58:25.643 | 86daee90cd0045f4aebeb1d2ed7d24cc | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:180 - === Dashboard DAO: 时间范围 - 昨日: 2025-07-28 00:00:00 到 2025-07-28 23:59:59.999999 ===
2025-07-29 08:58:25.644 | 86daee90cd0045f4aebeb1d2ed7d24cc | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:196 - === Dashboard DAO: 今日任务查询SQL: SELECT count(opinion_task.id) AS count_1 
FROM opinion_task 
WHERE opinion_task.create_time >= :create_time_1 AND opinion_task.create_time <= :create_time_2 AND opinion_task.is_enabled = :is_enabled_1 AND opinion_task.user_id = :user_id_1 ===
2025-07-29 08:58:25.677 | 86daee90cd0045f4aebeb1d2ed7d24cc | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:200 - === Dashboard DAO: 今日任务数量: 0 ===
2025-07-29 08:58:25.841 | 86daee90cd0045f4aebeb1d2ed7d24cc | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:406 - === 开始获取用户分析统计数据，用户ID: 1 ===
2025-07-29 08:58:25.922 | 86daee90cd0045f4aebeb1d2ed7d24cc | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:445 - === 用户1的opinion_requirement记录总数: 69 ===
2025-07-29 08:58:25.923 | 86daee90cd0045f4aebeb1d2ed7d24cc | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:446 - === 套餐有效期信息: {'has_valid_package': True, 'start_time': datetime.datetime(2025, 7, 17, 10, 30), 'end_time': datetime.datetime(2025, 7, 29, 8, 58, 25, 888964), 'package_name': '企业版', 'is_expired': True} ===
2025-07-29 08:58:25.957 | 86daee90cd0045f4aebeb1d2ed7d24cc | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:462 - === 用户1今日的opinion_requirement记录数: 2 ===
2025-07-29 08:58:25.958 | 86daee90cd0045f4aebeb1d2ed7d24cc | INFO     | module_opinion.dao.dashboard_dao:get_user_package_info:526 - === 开始获取用户套餐信息，用户ID: 1 ===
2025-07-29 08:58:25.993 | 86daee90cd0045f4aebeb1d2ed7d24cc | INFO     | config.package_config:get_package_by_vip_level:64 - VIP等级 1 对应套餐: {'name': 'VIP1', 'limit': 100, 'description': 'VIP1会员套餐', 'membership_status': 'VIP1会员'}
2025-07-29 08:58:25.993 | 86daee90cd0045f4aebeb1d2ed7d24cc | INFO     | module_opinion.dao.dashboard_dao:_get_package_from_vip_level:703 - === 根据VIP等级 1 确定套餐: {'name': 'VIP1', 'limit': 100, 'description': 'VIP1会员套餐', 'membership_status': 'VIP1会员'} ===
2025-07-29 08:58:25.994 | 86daee90cd0045f4aebeb1d2ed7d24cc | INFO     | module_opinion.dao.dashboard_dao:get_user_analysis_statistics:494 - === 用户分析统计数据: {'total_analysis': 69, 'today_analysis': 2, 'remaining_count': 31, 'today_remaining': 1, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 69.0} ===
2025-07-29 08:58:25.994 | 86daee90cd0045f4aebeb1d2ed7d24cc | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:361 - === Dashboard DAO: 最终统计数据: {'today_tasks': 0, 'tasks_growth': 0.0, 'negative_count': 0, 'negative_change': 0.0, 'completed_count': 12, 'completed_change': 100.0, 'pending_count': 13, 'pending_change': 100.0, 'total_count': 25, 'running_count': 0, 'failed_count': 0, 'total_analysis': 69, 'today_analysis': 2, 'remaining_count': 31, 'today_remaining': 1, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 69.0} ===
2025-07-29 08:58:25.994 | 86daee90cd0045f4aebeb1d2ed7d24cc | INFO     | module_opinion.dao.dashboard_dao:get_dashboard_statistics:362 - === Dashboard DAO: 成功获取Dashboard统计数据 ===
2025-07-29 08:58:25.994 | 86daee90cd0045f4aebeb1d2ed7d24cc | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:76 - === Dashboard Service: 从DAO获取的原始数据: {'today_tasks': 0, 'tasks_growth': 0.0, 'negative_count': 0, 'negative_change': 0.0, 'completed_count': 12, 'completed_change': 100.0, 'pending_count': 13, 'pending_change': 100.0, 'total_count': 25, 'running_count': 0, 'failed_count': 0, 'total_analysis': 69, 'today_analysis': 2, 'remaining_count': 31, 'today_remaining': 1, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 69.0} ===
2025-07-29 08:58:25.994 | 86daee90cd0045f4aebeb1d2ed7d24cc | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:80 - === Dashboard Service: 转换后的模型数据: today_tasks=0 tasks_growth=0.0 negative_count=0 negative_change=0.0 completed_count=12 completed_change=100.0 pending_count=13 pending_change=100.0 total_count=25 running_count=0 failed_count=0 total_analysis=69 today_analysis=2 remaining_count=31 today_remaining=1 package_name='VIP1' package_limit=100 usage_percentage=69.0 ===
2025-07-29 08:58:25.995 | 86daee90cd0045f4aebeb1d2ed7d24cc | INFO     | module_opinion.service.dashboard_service:get_dashboard_statistics_services:82 - === Dashboard Service: 成功获取Dashboard统计数据 ===
2025-07-29 08:58:25.995 | 86daee90cd0045f4aebeb1d2ed7d24cc | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:74 - === 从Service获取的统计数据: today_tasks=0 tasks_growth=0.0 negative_count=0 negative_change=0.0 completed_count=12 completed_change=100.0 pending_count=13 pending_change=100.0 total_count=25 running_count=0 failed_count=0 total_analysis=69 today_analysis=2 remaining_count=31 today_remaining=1 package_name='VIP1' package_limit=100 usage_percentage=69.0 ===
2025-07-29 08:58:25.995 | 86daee90cd0045f4aebeb1d2ed7d24cc | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:75 - === 统计数据类型: <class 'module_opinion.entity.vo.dashboard_vo.DashboardStatisticsModel'> ===
2025-07-29 08:58:25.995 | 86daee90cd0045f4aebeb1d2ed7d24cc | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:79 - === 转换后的字典数据: {'today_tasks': 0, 'tasks_growth': 0.0, 'negative_count': 0, 'negative_change': 0.0, 'completed_count': 12, 'completed_change': 100.0, 'pending_count': 13, 'pending_change': 100.0, 'total_count': 25, 'running_count': 0, 'failed_count': 0, 'total_analysis': 69, 'today_analysis': 2, 'remaining_count': 31, 'today_remaining': 1, 'package_name': 'VIP1', 'package_limit': 100, 'usage_percentage': 69.0} ===
2025-07-29 08:58:25.996 | 86daee90cd0045f4aebeb1d2ed7d24cc | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:81 - === Dashboard统计数据获取成功 ===
2025-07-29 08:58:25.996 | 86daee90cd0045f4aebeb1d2ed7d24cc | INFO     | module_opinion.controller.dashboard_controller:get_dashboard_statistics:84 - === 最终响应数据: <starlette.responses.JSONResponse object at 0x0000019F2A8D2ED0> ===
2025-07-29 08:58:26.358 | a8b0d8f31b3f472595d3940897f03917 | INFO     | module_opinion.dao.analysis_progress_dao:create_analysis_task:52 - 创建分析任务成功，任务ID: analysis_a991911f7a3644db
2025-07-29 08:58:26.482 | a8b0d8f31b3f472595d3940897f03917 | DEBUG    | module_opinion.dao.analysis_progress_dao:add_progress_log:169 - 添加进度日志成功: analysis_a991911f7a3644db - 分析任务已创建
2025-07-29 08:58:26.483 | a8b0d8f31b3f472595d3940897f03917 | INFO     | module_opinion.service.analysis_progress_service:create_analysis_task:60 - 创建分析任务成功: analysis_a991911f7a3644db
2025-07-29 08:58:26.483 | a8b0d8f31b3f472595d3940897f03917 | INFO     | module_opinion.controller.opinion_analysis_controller:create_analysis_task:1355 - 创建分析任务成功，任务ID: analysis_a991911f7a3644db
2025-07-29 08:58:27.028 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.controller.opinion_analysis_controller:start_analysis:998 - 开始分析，需求ID: 267
2025-07-29 08:58:27.028 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.controller.opinion_analysis_controller:start_analysis:1004 - 执行联网搜索...
2025-07-29 08:58:27.028 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:perform_online_search:37 - 开始执行联网搜索，实体关键词: 方太
2025-07-29 08:58:27.029 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:perform_online_search:40 - 第一阶段：开始模型能力验证
2025-07-29 08:58:27.029 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_verify_model_capabilities:88 - 开始验证AI模型的URL提供能力
2025-07-29 08:58:27.095 | a472987dfffc4b0c942707ea97214208 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_a991911f7a3644db
2025-07-29 08:58:28.905 | bfd58169ca2642b1af0d131541e7a6fd | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_a991911f7a3644db
2025-07-29 08:58:31.019 | 0a77451f06e34357ac507cc774670b2b | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_a991911f7a3644db
2025-07-29 08:58:33.131 | b0fdaea70b874a96aac98bc78d806dce | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_a991911f7a3644db
2025-07-29 08:58:35.220 | 5f29bd09e4c6454b966f5937836e09e7 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_a991911f7a3644db
2025-07-29 08:58:37.763 | 873998fe1ec448a1975d228955e39207 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_a991911f7a3644db
2025-07-29 08:58:39.809 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_call_ark_api:532 - 豆包AI API调用成功
2025-07-29 08:58:39.810 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_analyze_capability_response:135 - AI能力测试回复: 

https://content-static.cctvnews.cctv.com/snow-book/index.html?item_id=12818008410318019725&t=1695360009044...
2025-07-29 08:58:39.810 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_analyze_capability_response:146 - AI返回了URL: ['https://content-static.cctvnews.cctv.com/snow-book/index.html?item_id=12818008410318019725&t=1695360009044']
2025-07-29 08:58:39.811 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_verify_model_capabilities:108 - 模型能力验证完成: {'can_provide_urls': True, 'confidence_level': 'high', 'test_result': 'passed', 'sample_urls': ['https://content-static.cctvnews.cctv.com/snow-book/index.html?item_id=12818008410318019725&t=1695360009044'], 'url_count': 1}
2025-07-29 08:58:39.811 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:perform_online_search:42 - 模型能力验证结果: {'can_provide_urls': True, 'confidence_level': 'high', 'test_result': 'passed', 'sample_urls': ['https://content-static.cctvnews.cctv.com/snow-book/index.html?item_id=12818008410318019725&t=1695360009044'], 'url_count': 1}
2025-07-29 08:58:39.812 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:perform_online_search:45 - 第二阶段：开始正式搜索
2025-07-29 08:58:39.812 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_build_adaptive_search_query:232 - 使用高能力搜索模式，强调URL真实性
2025-07-29 08:58:40.794 | e993c8f5ebfa4d6c88d1cc060e10cb91 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_a991911f7a3644db
2025-07-29 08:58:42.997 | 6b1eab4e353048cd9da29de8b89e4d05 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_a991911f7a3644db
2025-07-29 08:58:45.778 | 77831f7064e64b258f19d2d27d078eb2 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_a991911f7a3644db
2025-07-29 08:58:48.785 | 7e74749813764571a1373b150abfc0b4 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_a991911f7a3644db
2025-07-29 08:58:51.791 | 34fd201dc56c4dcda73c77715fab223e | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_a991911f7a3644db
2025-07-29 08:58:54.069 | 9c3af8a9765a4ff185a514a8d71f9288 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_a991911f7a3644db
2025-07-29 08:58:56.799 | fe19345c29374163b34dce399ed84c16 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_a991911f7a3644db
2025-07-29 08:58:58.931 | e289c1638dc04521b287d3a3e141c19d | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_a991911f7a3644db
2025-07-29 08:59:01.060 | f3aa850095d143eb99874b95be85ba51 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_a991911f7a3644db
2025-07-29 08:59:03.195 | 9ce2920f867d4521b3790ac3cf51678d | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_a991911f7a3644db
2025-07-29 08:59:05.310 | 4e46256f01e94218b9af51653bc4377a | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_a991911f7a3644db
2025-07-29 08:59:07.431 | 63244948a18949dcb93cf134d99307ac | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_a991911f7a3644db
2025-07-29 08:59:09.556 | bd5926567e114131ac79fb244337d4a4 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_a991911f7a3644db
2025-07-29 08:59:11.792 | 882ef9728de5435a9018e507fcb5511a | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_a991911f7a3644db
2025-07-29 08:59:14.788 | 4f5d0e4eca394ae880a337c72ccc42a3 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_a991911f7a3644db
2025-07-29 08:59:17.769 | f80830ce94b641b59d4f0214e62e5215 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_a991911f7a3644db
2025-07-29 08:59:20.787 | 71b3422f58454692b7f9a647cd066cd0 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_a991911f7a3644db
2025-07-29 08:59:23.768 | e4eaec20812b416798834771e34aac9f | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_a991911f7a3644db
2025-07-29 08:59:26.779 | 797e9f36a3904f7b936e49e2392b281f | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_a991911f7a3644db
2025-07-29 08:59:28.772 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_call_ark_api:532 - 豆包AI API调用成功
2025-07-29 08:59:28.778 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:700 - 尝试使用Firecrawl MCP增强的文章提取
2025-07-29 08:59:29.806 | 9a90dacc255b404496656ff75ce41b6a | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_a991911f7a3644db
2025-07-29 08:59:32.767 | c3fd41d4ccfa4697a384f7b7d3cbb8b5 | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_a991911f7a3644db
2025-07-29 09:00:08.409 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_call_ark_api_with_mcp_support:605 - 豆包AI API（MCP支持）调用成功
2025-07-29 09:00:08.411 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:710 - AI返回的原始内容: ```json
[
  {
    "title": "方太洗碗机故障，京东退货推诿，物流打包还添乱",
    "content": "消费者购机后未使用即出现故障，经历多次无效沟通后通过12315介入达成退货，但物流环节仍因包装材料缺失导致纠纷升级。投诉涉及方太2025年新款洗碗机（型号VP10等）的质量缺陷，包括喷淋臂故障、电机电流异常导致的F7错误代码等问题，售后服务响应迟缓且维修流程不规...
2025-07-29 09:00:20.798 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_call_ark_api_with_mcp_support:605 - 豆包AI API（MCP支持）调用成功
2025-07-29 09:00:29.177 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_call_ark_api_with_mcp_support:605 - 豆包AI API（MCP支持）调用成功
2025-07-29 09:00:29.178 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:1294 - 使用本地情感分析结果: negative
2025-07-29 09:00:29.178 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:746 - 使用AI返回的原始URL: https://tousu.sina.com.cn/complaint/view/17385355475
2025-07-29 09:00:37.185 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_call_ark_api_with_mcp_support:605 - 豆包AI API（MCP支持）调用成功
2025-07-29 09:00:48.889 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_call_ark_api_with_mcp_support:605 - 豆包AI API（MCP支持）调用成功
2025-07-29 09:00:48.890 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:1294 - 使用本地情感分析结果: negative
2025-07-29 09:00:48.890 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:746 - 使用AI返回的原始URL: http://www.cqn.com.cn/ms/content/2024-07/25/content_9123456.htm
2025-07-29 09:01:00.040 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_call_ark_api_with_mcp_support:605 - 豆包AI API（MCP支持）调用成功
2025-07-29 09:01:10.998 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_call_ark_api_with_mcp_support:605 - 豆包AI API（MCP支持）调用成功
2025-07-29 09:01:10.999 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:1294 - 使用本地情感分析结果: negative
2025-07-29 09:01:10.999 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:746 - 使用AI返回的原始URL: https://weibo.com/1234567890/ABCdefGHIJ
2025-07-29 09:01:35.178 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_call_ark_api_with_mcp_support:605 - 豆包AI API（MCP支持）调用成功
2025-07-29 09:01:45.800 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_call_ark_api_with_mcp_support:605 - 豆包AI API（MCP支持）调用成功
2025-07-29 09:01:45.802 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:1294 - 使用本地情感分析结果: negative
2025-07-29 09:01:45.802 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:746 - 使用AI返回的原始URL: http://www.cheaa.com/news/2024/0620/692438.shtml
2025-07-29 09:01:51.560 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_call_ark_api_with_mcp_support:605 - 豆包AI API（MCP支持）调用成功
2025-07-29 09:02:03.872 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_call_ark_api_with_mcp_support:605 - 豆包AI API（MCP支持）调用成功
2025-07-29 09:02:03.873 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_analyze_text_sentiment:1294 - 使用本地情感分析结果: neutral
2025-07-29 09:02:03.873 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:746 - 使用AI返回的原始URL: http://www.ccreports.org.cn/article/20240617/3589.html
2025-07-29 09:02:03.873 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_ai_extract_articles:759 - 成功提取 5 篇文章
2025-07-29 09:02:03.873 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_extract_articles_from_content:676 - AI成功提取到 5 篇文章
2025-07-29 09:02:03.873 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_calculate_sentiment_statistics:1334 - 情感分析统计: {'positive': 0, 'neutral': 20, 'negative': 80} (总文章数: 5)
2025-07-29 09:02:03.874 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_evaluate_search_quality:402 - 搜索质量评估完成: 总分 60.24, 等级 good
2025-07-29 09:02:03.874 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:perform_online_search:58 - 搜索结果质量评估: {'overall_quality': 'good', 'overall_score': 60.24, 'url_coverage': {'score': 100.0, 'stats': {'total': 5, 'with_url': 5}}, 'content_quality': {'score': 60.24, 'stats': {'avg_title_length': 21.0, 'avg_content_length': 102.4, 'with_source': 5, 'with_time': 5}}, 'recommendations': ['搜索结果质量良好']}
2025-07-29 09:02:03.874 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1476 - 开始保存搜索结果到数据库
2025-07-29 09:02:03.874 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1477 - 实体关键词: 方太
2025-07-29 09:02:03.874 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1478 - 选中关键词: ['品牌口碑', '产品质量', '用户反馈', '好评分析', '投诉监测']
2025-07-29 09:02:03.874 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1479 - 搜索结果文章数量: 5
2025-07-29 09:02:03.875 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1489 - 合并后的关键词字符串: 方太,品牌口碑,产品质量,用户反馈,好评分析,投诉监测
2025-07-29 09:02:03.875 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1493 - 正在保存第 1 条文章: 方太洗碗机故障，京东退货推诿，物流打包还添乱
2025-07-29 09:02:03.875 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1501 - 保存AI返回的原始URL: https://tousu.sina.com.cn/complaint/view/17385355475
2025-07-29 09:02:03.875 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1521 - 准备保存数据: title=方太洗碗机故障，京东退货推诿，物流打包还添乱..., url=https://tousu.sina.com.cn/complaint/view/17385355475, sentiment=negative
2025-07-29 09:02:03.910 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1527 - 成功保存第 1 条文章，ID: 2229
2025-07-29 09:02:03.910 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1493 - 正在保存第 2 条文章: 方太售后遭集体投诉：大品牌为何放任'傲慢服务'?
2025-07-29 09:02:03.910 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1501 - 保存AI返回的原始URL: http://www.cqn.com.cn/ms/content/2024-07/25/content_9123456.htm
2025-07-29 09:02:03.911 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1521 - 准备保存数据: title=方太售后遭集体投诉：大品牌为何放任'傲慢服务'?..., url=http://www.cqn.com.cn/ms/content/2024-07/25/content_9123456.htm, sentiment=negative
2025-07-29 09:02:03.943 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1527 - 成功保存第 2 条文章，ID: 2230
2025-07-29 09:02:03.944 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1493 - 正在保存第 3 条文章: 微博话题#方太售后傲慢#引发127万次阅读讨论
2025-07-29 09:02:03.944 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1501 - 保存AI返回的原始URL: https://weibo.com/1234567890/ABCdefGHIJ
2025-07-29 09:02:03.944 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1521 - 准备保存数据: title=微博话题#方太售后傲慢#引发127万次阅读讨论..., url=https://weibo.com/1234567890/ABCdefGHIJ, sentiment=negative
2025-07-29 09:02:03.976 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1527 - 成功保存第 3 条文章，ID: 2231
2025-07-29 09:02:03.977 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1493 - 正在保存第 4 条文章: 方太洗碗机VP10系列遭集中质量投诉
2025-07-29 09:02:03.977 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1501 - 保存AI返回的原始URL: http://www.cheaa.com/news/2024/0620/692438.shtml
2025-07-29 09:02:03.977 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1521 - 准备保存数据: title=方太洗碗机VP10系列遭集中质量投诉..., url=http://www.cheaa.com/news/2024/0620/692438.shtml, sentiment=negative
2025-07-29 09:02:04.009 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1527 - 成功保存第 4 条文章，ID: 2232
2025-07-29 09:02:04.009 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1493 - 正在保存第 5 条文章: 消费者维权组织发布家电售后服务排行榜
2025-07-29 09:02:04.010 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1501 - 保存AI返回的原始URL: http://www.ccreports.org.cn/article/20240617/3589.html
2025-07-29 09:02:04.010 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1521 - 准备保存数据: title=消费者维权组织发布家电售后服务排行榜..., url=http://www.ccreports.org.cn/article/20240617/3589.html, sentiment=neutral
2025-07-29 09:02:04.043 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1527 - 成功保存第 5 条文章，ID: 2233
2025-07-29 09:02:04.108 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:_save_search_results_to_db:1538 - 成功保存 5 条搜索结果到数据库
2025-07-29 09:02:04.108 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.service.external_api_service:perform_online_search:65 - 联网搜索完成，获取到 5 条结果，已保存 5 条到数据库
2025-07-29 09:02:04.108 | c7ca7da049b04caeb9fda6a97b9bb9cc | INFO     | module_opinion.controller.opinion_analysis_controller:start_analysis:1040 - 分析执行成功
2025-07-29 09:02:04.216 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-29 09:02:04.216 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
2025-07-29 09:02:08.179 |  | INFO     | server:lifespan:69 - RuoYi-FastAPI开始启动
2025-07-29 09:02:08.179 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-29 09:02:09.598 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-29 09:02:09.598 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-29 09:02:09.599 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-29 09:02:10.029 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-29 09:02:10.600 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-29 09:02:10.600 |  | INFO     | server:lifespan:76 - RuoYi-FastAPI启动成功
2025-07-29 09:02:10.805 | 13633db2c6114446ad22e0b6fd4ce2ed | INFO     | module_opinion.controller.opinion_analysis_controller:get_analysis_progress:1395 - 获取分析进度成功: analysis_a991911f7a3644db
2025-07-29 09:02:10.958 | d05fe45f051345aabf3ce2360a77521a | INFO     | module_opinion.dao.analysis_progress_dao:update_task_status:129 - 更新任务状态成功: analysis_a991911f7a3644db -> completed
2025-07-29 09:02:11.136 | d05fe45f051345aabf3ce2360a77521a | DEBUG    | module_opinion.dao.analysis_progress_dao:add_progress_log:169 - 添加进度日志成功: analysis_a991911f7a3644db - 分析任务已完成
2025-07-29 09:02:11.136 | d05fe45f051345aabf3ce2360a77521a | DEBUG    | module_opinion.service.websocket_manager:broadcast_to_task:120 - 任务没有活跃连接: analysis_a991911f7a3644db
2025-07-29 09:02:11.136 | d05fe45f051345aabf3ce2360a77521a | INFO     | module_opinion.service.analysis_progress_service:complete_analysis_task:232 - 完成分析任务成功: analysis_a991911f7a3644db
2025-07-29 09:02:11.136 | d05fe45f051345aabf3ce2360a77521a | INFO     | module_opinion.controller.opinion_analysis_controller:complete_analysis_task:1417 - 完成分析任务成功: analysis_a991911f7a3644db
2025-07-29 09:02:11.321 | 6474eebdb485432eb36a0cb549b1cd52 | INFO     | module_opinion.controller.opinion_analysis_controller:generate_and_upload_report:1457 - 开始生成报告并上传到OSS，需求ID: 267
2025-07-29 09:02:11.322 | 6474eebdb485432eb36a0cb549b1cd52 | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:80 - 生成HTML - report_data keys: ['totalArticles', 'totalKeywords', 'dataSources', 'sentiment', 'onlineSearchCount', 'customSourceCounts']
2025-07-29 09:02:11.322 | 6474eebdb485432eb36a0cb549b1cd52 | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:81 - 生成HTML - analysis_results keys: ['online_search']
2025-07-29 09:02:11.322 | 6474eebdb485432eb36a0cb549b1cd52 | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:82 - 生成HTML - selectedKeywords: []
2025-07-29 09:02:11.323 | 6474eebdb485432eb36a0cb549b1cd52 | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:107 - 生成HTML - 提取到 0 篇文章
2025-07-29 09:02:11.323 | 6474eebdb485432eb36a0cb549b1cd52 | INFO     | module_opinion.service.report_html_generator_service:_generate_html_content:111 - 生成HTML - 提取到 0 个关键词: []
2025-07-29 09:02:11.811 | 6474eebdb485432eb36a0cb549b1cd52 | INFO     | utils.oss_util:_get_client:47 - OSS客户端初始化成功
2025-07-29 09:02:11.990 | 6474eebdb485432eb36a0cb549b1cd52 | INFO     | utils.oss_util:_generate_signed_domain_url:204 - 生成可直接查看的OSS URL: opinion-reports/report_ae4e13757baa_1753750931.html
2025-07-29 09:02:11.991 | 6474eebdb485432eb36a0cb549b1cd52 | INFO     | utils.oss_util:_upload_html_standard:110 - HTML文件上传OSS成功: opinion-reports/report_ae4e13757baa_1753750931.html -> https://oss.jingangai.cn/opinion-reports/report_ae4e13757baa_1753750931.html?OSSAccessKeyId=LTAIHVOdjxgTfzUK&Expires=1756342931&Signature=6ah4ZRGgH8w0UG4bE7/jwBquIhw%3D
2025-07-29 09:02:11.991 | 6474eebdb485432eb36a0cb549b1cd52 | INFO     | utils.oss_util:_upload_html_standard:111 - OSS上传结果 - 状态码: 200, 请求ID: 68881D95A0BBC430316F5330
2025-07-29 09:02:11.992 | 6474eebdb485432eb36a0cb549b1cd52 | INFO     | module_opinion.service.report_html_generator_service:generate_report_html:45 - 报告HTML页面生成并上传OSS成功: report_ae4e13757baa_1753750931 -> https://oss.jingangai.cn/opinion-reports/report_ae4e13757baa_1753750931.html?OSSAccessKeyId=LTAIHVOdjxgTfzUK&Expires=1756342931&Signature=6ah4ZRGgH8w0UG4bE7/jwBquIhw%3D
2025-07-29 09:02:12.017 | 6474eebdb485432eb36a0cb549b1cd52 | INFO     | module_opinion.controller.opinion_analysis_controller:generate_and_upload_report:1493 - 报告生成并上传OSS成功: report_ae4e13757baa_1753750931 -> https://oss.jingangai.cn/opinion-reports/report_ae4e13757baa_1753750931.html?OSSAccessKeyId=LTAIHVOdjxgTfzUK&Expires=1756342931&Signature=6ah4ZRGgH8w0UG4bE7/jwBquIhw%3D
2025-07-29 09:02:12.670 | 4f145f9cb1074003ae3401359638b16d | INFO     | module_opinion.controller.opinion_analysis_controller:create_task:582 - 创建舆情任务成功
2025-07-29 09:02:17.448 | 899dd24619a04941a91e4a976408c8f2 | ERROR    | module_opinion.controller.opinion_analysis_controller:create_task:585 - 创建舆情任务失败: 任务名称"品牌舆情监测_20250729085726_azbpw9 - 分析报告"在该需求下已存在
2025-07-29 09:02:44.824 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-29 09:02:44.825 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
