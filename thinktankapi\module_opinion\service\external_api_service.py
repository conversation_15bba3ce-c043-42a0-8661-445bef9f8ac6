"""
外部API服务层
用于调用外部API进行数据采集和分析
"""
import asyncio
import aiohttp
import json
from typing import Dict, List, Optional, Any
from sqlalchemy.ext.asyncio import AsyncSession
from utils.log_util import logger
from exceptions.exception import ServiceException
from module_admin.dao.keyword_data_dao import KeywordDataDao
from module_admin.entity.vo.keyword_data_vo import KeywordDataModel


class ExternalApiService:
    """
    外部API服务类
    """

    # 豆包AI API配置 - 使用联网搜索接口
    ARK_API_BASE_URL = "https://ark.cn-beijing.volces.com/api/v3/bots/chat/completions"
    ARK_API_KEY = "580f264a-fb64-41a0-a1eb-3246d87f5835"
    ARK_MODEL = "bot-20250707132111-g57fr"  # 切换为联网搜索模型
    
    @classmethod
    async def perform_online_search(cls, query_db: AsyncSession, entity_keyword: str, specific_requirement: str, selected_keywords: List[str]) -> Dict[str, Any]:
        """
        执行联网搜索（采用两阶段验证策略）

        :param entity_keyword: 实体关键词
        :param specific_requirement: 具体需求
        :param selected_keywords: 选中的关键词列表
        :return: 搜索结果
        """
        try:
            logger.info(f"开始执行联网搜索，实体关键词: {entity_keyword}")

            # 第一阶段：模型能力验证
            logger.info("第一阶段：开始模型能力验证")
            model_capabilities = await cls._verify_model_capabilities()
            logger.info(f"模型能力验证结果: {model_capabilities}")

            # 第二阶段：基于验证结果执行搜索
            logger.info("第二阶段：开始正式搜索")
            search_query = cls._build_adaptive_search_query(
                entity_keyword, specific_requirement, selected_keywords, model_capabilities
            )

            # 调用豆包AI API
            search_results = await cls._call_ark_api(search_query)

            # 处理搜索结果
            processed_results = await cls._process_search_results(search_results, entity_keyword)

            # 评估搜索结果质量
            quality_assessment = cls._evaluate_search_quality(processed_results, model_capabilities)
            logger.info(f"搜索结果质量评估: {quality_assessment}")

            # 保存搜索结果到数据库
            saved_count = await cls._save_search_results_to_db(
                query_db, processed_results, entity_keyword, selected_keywords
            )

            logger.info(f"联网搜索完成，获取到 {len(processed_results.get('articles', []))} 条结果，已保存 {saved_count} 条到数据库")

            return {
                'success': True,
                'data': processed_results,
                'message': f'联网搜索完成，已保存 {saved_count} 条数据到数据库',
                'saved_count': saved_count,
                'model_capabilities': model_capabilities,
                'quality_assessment': quality_assessment
            }
            
        except Exception as e:
            logger.error(f"联网搜索失败: {str(e)}")
            raise ServiceException(message=f"联网搜索失败: {str(e)}")

    @classmethod
    async def _verify_model_capabilities(cls) -> Dict[str, Any]:
        """
        第一阶段：验证AI模型的URL提供能力

        :return: 模型能力评估结果
        """
        try:
            logger.info("开始验证AI模型的URL提供能力")

            # 能力测试问题
            capability_test_query = """
你能否搜索并返回真实的外部网页URL地址？

请提供一个具体的新闻网站链接作为示例，要求：
1. 必须是真实可访问的URL地址
2. 指向具体的新闻文章页面
3. 不要使用示例或占位符URL

请直接返回一个完整的URL地址。
"""

            # 调用AI进行能力测试
            test_response = await cls._call_ark_api(capability_test_query)

            # 分析AI回复
            capabilities = cls._analyze_capability_response(test_response)

            logger.info(f"模型能力验证完成: {capabilities}")
            return capabilities

        except Exception as e:
            logger.error(f"模型能力验证失败: {str(e)}")
            # 返回保守的能力评估
            return {
                'can_provide_urls': False,
                'confidence_level': 'low',
                'test_result': 'failed',
                'error': str(e)
            }

    @classmethod
    def _analyze_capability_response(cls, response: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析AI能力测试的响应结果

        :param response: AI API响应
        :return: 能力评估结果
        """
        try:
            # 提取AI返回的内容
            content = ""
            if "choices" in response and len(response["choices"]) > 0:
                content = response["choices"][0].get("message", {}).get("content", "")

            logger.info(f"AI能力测试回复: {content[:200]}...")

            # 分析回复内容
            import re

            # 检查是否包含URL（简化检查，不验证格式）
            url_pattern = r'https?://[^\s<>"\']+\.[a-zA-Z]{2,}[^\s<>"\']*'
            urls = re.findall(url_pattern, content)

            # 评估能力（简化评估，直接信任AI返回的URL）
            if urls:
                logger.info(f"AI返回了URL: {urls}")
                return {
                    'can_provide_urls': True,
                    'confidence_level': 'high',
                    'test_result': 'passed',
                    'sample_urls': urls[:3],  # 最多保存3个示例
                    'url_count': len(urls)
                }

            # 检查是否明确表示无法提供URL
            negative_indicators = ['无法', '不能', '无法提供', '不支持', '无法访问', '无法搜索']
            if any(indicator in content for indicator in negative_indicators):
                logger.info("AI明确表示无法提供URL")
                return {
                    'can_provide_urls': False,
                    'confidence_level': 'high',
                    'test_result': 'clear_limitation',
                    'ai_response': content[:500]
                }

            # 模糊回复
            logger.warning("AI回复模糊，无法确定URL提供能力")
            return {
                'can_provide_urls': False,
                'confidence_level': 'medium',
                'test_result': 'unclear',
                'ai_response': content[:500]
            }

        except Exception as e:
            logger.error(f"分析能力响应失败: {str(e)}")
            return {
                'can_provide_urls': False,
                'confidence_level': 'low',
                'test_result': 'analysis_failed',
                'error': str(e)
            }

    # 注释掉URL格式验证方法，完全信任AI返回的URL
    # @classmethod
    # def _is_valid_url_format(cls, url: str) -> bool:
    #     """
    #     基本URL格式验证（已废弃，完全信任AI返回的URL）
    #
    #     :param url: URL地址
    #     :return: 是否为有效格式
    #     """
    #     try:
    #         import re
    #         # 基本URL格式检查
    #         url_pattern = r'^https?://[^\s<>"\']+\.[a-zA-Z]{2,}[^\s<>"\']*$'
    #         if not re.match(url_pattern, url):
    #             return False
    #
    #         # 排除明显的示例URL
    #         invalid_patterns = [
    #             'example.com', 'test.com', 'placeholder', 'sample',
    #             'demo.com', 'xxx.com', 'yyy.com'
    #         ]
    #
    #         url_lower = url.lower()
    #         for pattern in invalid_patterns:
    #             if pattern in url_lower:
    #                 return False
    #
    #         return True
    #
    #     except Exception:
    #         return False

    @classmethod
    def _build_adaptive_search_query(cls, entity_keyword: str, specific_requirement: str, selected_keywords: List[str], capabilities: Dict[str, Any]) -> str:
        """
        基于模型能力动态构建搜索查询

        :param entity_keyword: 实体关键词
        :param specific_requirement: 具体需求
        :param selected_keywords: 选中的关键词列表
        :param capabilities: 模型能力评估结果
        :return: 搜索查询语句
        """
        keywords_str = "、".join(selected_keywords)

        # 根据模型能力调整搜索策略
        if capabilities.get('can_provide_urls', False) and capabilities.get('confidence_level') == 'high':
            # 高能力模式：强调URL真实性
            logger.info("使用高能力搜索模式，强调URL真实性")
            query = f"""
请帮我搜索关于"{entity_keyword}"的最新网络舆情信息。

具体需求：{specific_requirement}

关注的关键词：{keywords_str}

请搜索并分析以下内容：
1. 与"{entity_keyword}"相关的最新新闻报道
2. 社交媒体上的讨论和评论
3. 公众对"{entity_keyword}"的情感倾向
4. 相关话题的传播趋势

**重要要求（基于您的URL提供能力）**：
- 请直接提供文章的真实原始URL地址
- 每篇文章都必须包含完整的可访问网络链接
- URL应该指向具体的新闻文章页面
- 确保提供的链接是真实有效的

请提供详细的搜索结果，包括：
- 文章的真实URL链接
- 信息来源网站名称
- 发布时间
- 内容摘要
- 情感分析
"""
        else:
            # 低能力模式：专注内容质量
            logger.info("使用低能力搜索模式，专注内容质量")
            query = f"""
请帮我搜索关于"{entity_keyword}"的最新网络舆情信息。

具体需求：{specific_requirement}

关注的关键词：{keywords_str}

请搜索并分析以下内容：
1. 与"{entity_keyword}"相关的最新新闻报道
2. 社交媒体上的讨论和评论
3. 公众对"{entity_keyword}"的情感倾向
4. 相关话题的传播趋势

**重要要求**：
- 请提供高质量的内容分析
- 重点关注信息的准确性和时效性
- 如果有URL链接请提供，如无法提供请留空
- 确保内容摘要详细且有价值

请提供详细的搜索结果，包括：
- 信息来源网站名称
- 发布时间
- 详细的内容摘要
- 情感分析
- URL链接（如果可以提供）
"""

        return query.strip()

    @classmethod
    def _evaluate_search_quality(cls, search_results: Dict[str, Any], capabilities: Dict[str, Any]) -> Dict[str, Any]:
        """
        评估搜索结果质量

        :param search_results: 搜索结果
        :param capabilities: 模型能力评估
        :return: 质量评估结果
        """
        try:
            articles = search_results.get('articles', [])
            total_articles = len(articles)

            if total_articles == 0:
                return {
                    'overall_quality': 'poor',
                    'url_quality': 'no_data',
                    'content_quality': 'no_data',
                    'recommendations': ['重新尝试搜索', '调整搜索关键词']
                }

            # URL统计（简化统计，不进行质量验证）
            url_stats = {
                'total': total_articles,
                'with_url': 0
            }

            for article in articles:
                url = article.get('url', '')
                if url and url.strip():
                    url_stats['with_url'] += 1

            # 内容质量评估
            content_stats = {
                'avg_title_length': 0,
                'avg_content_length': 0,
                'with_source': 0,
                'with_time': 0
            }

            total_title_len = 0
            total_content_len = 0

            for article in articles:
                title = article.get('title', '')
                content = article.get('content', '')
                source = article.get('source', '')
                publish_time = article.get('publish_time', '')

                total_title_len += len(title)
                total_content_len += len(content)

                if source and source.strip():
                    content_stats['with_source'] += 1
                if publish_time and publish_time.strip():
                    content_stats['with_time'] += 1

            if total_articles > 0:
                content_stats['avg_title_length'] = total_title_len / total_articles
                content_stats['avg_content_length'] = total_content_len / total_articles

            # 简化质量评估（专注内容质量，不评估URL质量）
            url_coverage_score = 0
            if url_stats['total'] > 0:
                url_coverage_score = (url_stats['with_url'] / url_stats['total']) * 100

            content_quality_score = 0
            if total_articles > 0:
                content_quality_score = (
                    (content_stats['with_source'] / total_articles) * 30 +
                    (content_stats['with_time'] / total_articles) * 20 +
                    min(content_stats['avg_content_length'] / 500, 1) * 50
                )

            # 确定整体质量等级（主要基于内容质量）
            overall_score = content_quality_score

            if overall_score >= 80:
                overall_quality = 'excellent'
            elif overall_score >= 60:
                overall_quality = 'good'
            elif overall_score >= 40:
                overall_quality = 'fair'
            else:
                overall_quality = 'poor'

            # 生成建议（简化建议，不基于URL质量）
            recommendations = []
            if url_coverage_score < 50:
                recommendations.append('部分文章缺少URL链接')

            if content_quality_score < 60:
                recommendations.append('内容质量需要改进，建议调整搜索策略')

            if not recommendations:
                recommendations.append('搜索结果质量良好')

            quality_assessment = {
                'overall_quality': overall_quality,
                'overall_score': round(overall_score, 2),
                'url_coverage': {
                    'score': round(url_coverage_score, 2),
                    'stats': url_stats
                },
                'content_quality': {
                    'score': round(content_quality_score, 2),
                    'stats': content_stats
                },
                'recommendations': recommendations
            }

            logger.info(f"搜索质量评估完成: 总分 {overall_score:.2f}, 等级 {overall_quality}")
            return quality_assessment

        except Exception as e:
            logger.error(f"搜索质量评估失败: {str(e)}")
            return {
                'overall_quality': 'unknown',
                'error': str(e),
                'recommendations': ['评估失败，建议检查系统状态']
            }

    # 注释掉URL真实性判断方法，完全信任AI返回的URL
    # @classmethod
    # def _is_likely_real_url(cls, url: str) -> bool:
    #     """
    #     判断URL是否可能是真实的（已废弃，完全信任AI返回的URL）
    #
    #     :param url: URL地址
    #     :return: 是否可能真实
    #     """
    #     try:
    #         # 检查是否包含真实新闻网站域名
    #         real_news_domains = [
    #             'sina.com', 'qq.com', '163.com', 'sohu.com', 'ifeng.com',
    #             'people.com', 'xinhua', 'chinanews.com', 'cctv.com',
    #             'chinadaily.com', 'caixin.com', 'thepaper.cn', 'yicai.com'
    #         ]
    #
    #         url_lower = url.lower()
    #         for domain in real_news_domains:
    #             if domain in url_lower:
    #                 return True
    #
    #         # 检查URL结构是否合理
    #         if '/article/' in url_lower or '/news/' in url_lower or '/a/' in url_lower:
    #             return True
    #
    #         # 排除明显的搜索引擎URL
    #         search_patterns = [
    #             'baidu.com/s?', 'google.com/search', 'bing.com/search',
    #             'so.com/s?', 'sogou.com/web'
    #         ]
    #
    #         for pattern in search_patterns:
    #             if pattern in url_lower:
    #                 return False
    #
    #         return True
    #
    #     except Exception:
    #         return False

    @classmethod
    def _build_search_query(cls, entity_keyword: str, specific_requirement: str, selected_keywords: List[str]) -> str:
        """
        构建搜索查询语句

        :param entity_keyword: 实体关键词
        :param specific_requirement: 具体需求
        :param selected_keywords: 选中的关键词列表
        :return: 搜索查询语句
        """
        keywords_str = "、".join(selected_keywords)

        query = f"""
请帮我搜索关于"{entity_keyword}"的最新网络舆情信息。

具体需求：{specific_requirement}

关注的关键词：{keywords_str}

请搜索并分析以下内容：
1. 与"{entity_keyword}"相关的最新新闻报道
2. 社交媒体上的讨论和评论
3. 公众对"{entity_keyword}"的情感倾向
4. 相关话题的传播趋势

**重要要求**：
- 请直接提供文章的原始URL地址
- 每篇文章都必须包含完整的原始网络链接地址
- 请返回文章的真实原始URL，不要进行任何修改或处理
- 如果无法获取URL，请返回空字符串

请提供详细的搜索结果，包括：
- 文章的原始URL链接
- 信息来源网站名称
- 发布时间
- 内容摘要
- 情感分析
"""
        return query.strip()
    
    @classmethod
    async def _call_ark_api(cls, query: str) -> Dict[str, Any]:
        """
        调用豆包AI API
        
        :param query: 查询内容
        :return: API响应结果
        """
        headers = {
            "Authorization": f"Bearer {cls.ARK_API_KEY}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": cls.ARK_MODEL,
            "stream": False,  # 不使用流式响应，便于处理
            "messages": [
                {
                    "role": "system",
                    "content": "你是一个专业的舆情分析助手，能够搜索和分析网络上的相关信息。请提供准确、及时的舆情分析结果。\n\n重要提醒：\n1. 必须提供真实可访问的新闻文章URL链接\n2. 不要使用示例URL、占位符URL或虚构的链接\n3. 确保每个URL都指向真实存在的网页内容\n4. 优先提供权威媒体和官方信息源的链接\n5. 所有URL必须是完整的、可直接访问的网络地址"
                },
                {
                    "role": "user",
                    "content": query
                }
            ]
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    cls.ARK_API_BASE_URL,
                    headers=headers,
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=180)  # 增加到3分钟
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        logger.info("豆包AI API调用成功")
                        return result
                    else:
                        error_text = await response.text()
                        logger.error(f"豆包AI API调用失败，状态码: {response.status}, 错误信息: {error_text}")
                        raise ServiceException(message=f"API调用失败，状态码: {response.status}")
                        
        except asyncio.TimeoutError:
            logger.error("豆包AI API调用超时")
            raise ServiceException(message="API调用超时，请稍后重试")
        except Exception as e:
            logger.error(f"豆包AI API调用异常: {str(e)}")
            raise ServiceException(message=f"API调用异常: {str(e)}")
    
    @classmethod
    async def _process_search_results(cls, api_response: Dict[str, Any], keyword: str = '') -> Dict[str, Any]:
        """
        处理搜索结果

        :param api_response: API响应结果
        :param keyword: 搜索关键词
        :return: 处理后的结果
        """
        try:
            # 提取AI返回的内容
            content = ""
            if "choices" in api_response and len(api_response["choices"]) > 0:
                content = api_response["choices"][0].get("message", {}).get("content", "")
            
            # 模拟解析结果为结构化数据
            # 在实际应用中，可能需要更复杂的解析逻辑
            articles = await cls._extract_articles_from_content(content, keyword)

            # 计算整体情感分析统计
            sentiment_analysis = cls._calculate_sentiment_statistics(articles)

            processed_data = {
                'articles': articles,
                'sentiment_analysis': sentiment_analysis,
                # 注释掉硬编码的趋势分析，后续可以集成AI趋势分析
                # 'trend_analysis': cls._extract_trends_from_content(content),
                'raw_content': content,
                'search_time': cls._get_current_timestamp()
            }
            
            return processed_data
            
        except Exception as e:
            logger.error(f"处理搜索结果失败: {str(e)}")
            return {
                'articles': [],
                'sentiment_analysis': {'positive': 0, 'neutral': 0, 'negative': 0},
                # 注释掉硬编码的趋势分析数据
                # 'trend_analysis': [],
                'raw_content': str(api_response),
                'search_time': cls._get_current_timestamp()
            }
    
    @classmethod
    async def _extract_articles_from_content(cls, content: str, keyword: str = '') -> List[Dict[str, Any]]:
        """
        使用AI从内容中提取文章信息（完全基于AI，无硬编码）

        :param content: AI返回的内容
        :param keyword: 搜索关键词，用于生成备用URL
        """
        try:
            # 使用AI进行文章提取
            articles = await cls._ai_extract_articles(content, keyword)

            if articles:
                logger.info(f"AI成功提取到 {len(articles)} 篇文章")
                return articles
            else:
                logger.warning("AI未能提取到文章，使用降级策略")
                return await cls._fallback_article_extraction(content, keyword)

        except Exception as e:
            logger.error(f"AI文章提取失败: {str(e)}")
            return await cls._fallback_article_extraction(content, keyword)

    @classmethod
    async def _ai_extract_articles(cls, content: str, keyword: str = '') -> List[Dict[str, Any]]:
        """
        使用AI提取文章信息
        """
        try:
            from module_opinion.config.sentiment_config import SentimentConfig

            # 构建AI文章提取的prompt
            extraction_prompt = SentimentConfig.get_article_extraction_prompt(content)

            # 首先尝试使用MCP增强的API调用
            api_response = None
            try:
                logger.info("尝试使用Firecrawl MCP增强的文章提取")
                api_response = await cls._call_ark_api_with_mcp_support(extraction_prompt)
            except Exception as mcp_error:
                logger.warning(f"MCP增强调用失败，回退到基础方法: {str(mcp_error)}")
                api_response = await cls._call_ark_api(extraction_prompt)

            # 解析AI返回的结果
            if "choices" in api_response and len(api_response["choices"]) > 0:
                ai_content = api_response["choices"][0].get("message", {}).get("content", "").strip()

                logger.info(f"AI返回的原始内容: {ai_content[:200]}...")

                # 预处理AI返回的内容，确保JSON格式
                cleaned_content = cls._clean_ai_json_response(ai_content)

                if not cleaned_content:
                    logger.warning("AI返回内容为空或无法清理为有效JSON")
                    return []

                # 尝试解析JSON格式的文章列表
                import json
                try:
                    extracted_articles = json.loads(cleaned_content)

                    if not isinstance(extracted_articles, list):
                        if isinstance(extracted_articles, dict):
                            extracted_articles = [extracted_articles]
                        else:
                            logger.warning(f"AI返回的不是数组或对象格式: {type(extracted_articles)}")
                            return []

                    # 处理每篇文章，添加情感分析
                    articles = []
                    for i, article in enumerate(extracted_articles):
                        if isinstance(article, dict):
                            # 验证必要字段
                            if not article.get('title') and not article.get('content'):
                                logger.warning(f"跳过无效文章 {i}: 缺少标题和内容")
                                continue

                            # 使用AI进行情感分析
                            article_content = article.get('content', article.get('title', ''))
                            sentiment = await cls._analyze_text_sentiment(article_content)

                            # 直接使用AI返回的原始URL，不进行任何验证或替换
                            article_url = article.get('url', '')
                            logger.info(f"使用AI返回的原始URL: {article_url}")

                            # 确保必要字段存在
                            processed_article = {
                                'title': article.get('title', f'AI提取文章-{len(articles)+1}'),
                                'content': article_content,
                                'url': article_url,  # 直接使用AI返回的原始URL
                                'source': article.get('source', '豆包AI搜索'),
                                'publish_time': article.get('publish_time', cls._get_current_timestamp()),
                                'sentiment': sentiment
                            }
                            articles.append(processed_article)

                    logger.info(f"成功提取 {len(articles)} 篇文章")
                    return articles

                except json.JSONDecodeError as e:
                    logger.error(f"JSON解析失败: {str(e)}")
                    logger.error(f"清理后的内容: {cleaned_content[:500]}...")
                    # 尝试从非JSON内容中提取信息
                    return await cls._extract_from_non_json_content(ai_content, keyword)

            logger.warning("AI API返回格式异常")
            return []

        except Exception as e:
            logger.error(f"AI文章提取过程出错: {str(e)}")
            return []

    @classmethod
    def _clean_ai_json_response(cls, ai_content: str) -> str:
        """
        清理AI返回的内容，确保是有效的JSON格式
        """
        try:
            # 移除可能的markdown标记
            ai_content = ai_content.replace('```json', '').replace('```', '').strip()

            # 查找JSON数组的开始和结束
            start_idx = ai_content.find('[')
            end_idx = ai_content.rfind(']')

            if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
                json_content = ai_content[start_idx:end_idx+1]

                # 验证是否为有效JSON
                import json
                json.loads(json_content)  # 测试解析
                return json_content

            # 如果没有找到数组，尝试查找对象
            start_idx = ai_content.find('{')
            end_idx = ai_content.rfind('}')

            if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
                json_content = ai_content[start_idx:end_idx+1]

                # 验证是否为有效JSON
                json.loads(json_content)  # 测试解析
                return f'[{json_content}]'  # 包装成数组

            return ""

        except Exception as e:
            logger.warning(f"清理JSON内容失败: {str(e)}")
            return ""

    @classmethod
    async def _extract_from_non_json_content(cls, ai_content: str, keyword: str = '') -> List[Dict[str, Any]]:
        """
        从非JSON格式的AI返回内容中提取文章信息
        """
        try:
            # 简单的文本解析策略
            lines = [line.strip() for line in ai_content.split('\n') if line.strip()]

            if not lines:
                return []

            # 将所有内容作为一篇文章处理
            title = await cls._ai_generate_title(ai_content)
            sentiment = await cls._analyze_text_sentiment(ai_content)

            article = {
                'title': title,
                'content': ai_content[:1000] + '...' if len(ai_content) > 1000 else ai_content,
                'url': '',  # 降级策略不生成URL，保持为空
                'source': '豆包AI搜索',
                'publish_time': cls._get_current_timestamp(),
                'sentiment': sentiment
            }

            return [article]

        except Exception as e:
            logger.error(f"从非JSON内容提取文章失败: {str(e)}")
            return []

    @classmethod
    async def _fallback_article_extraction(cls, content: str, keyword: str = '') -> List[Dict[str, Any]]:
        """
        降级策略：当AI提取失败时的简单处理
        """
        try:
            # 简单的降级策略：将整个内容作为一篇文章
            sentiment = await cls._analyze_text_sentiment(content)

            # 使用AI生成标题
            title = await cls._ai_generate_title(content)

            article = {
                'title': title,
                'content': content[:1000] + '...' if len(content) > 1000 else content,
                'url': '',  # 降级策略不生成URL，保持为空
                'source': '豆包AI搜索',
                'publish_time': cls._get_current_timestamp(),
                'sentiment': sentiment
            }

            return [article]

        except Exception as e:
            logger.error(f"降级文章提取也失败: {str(e)}")
            return []

    @classmethod
    async def _ai_generate_title(cls, content: str) -> str:
        """
        使用AI生成文章标题
        """
        try:
            title_prompt = f"""
请为以下内容生成一个简洁、准确的标题，不超过30个字。

你可以使用firecrawl_search工具搜索相关信息来生成更准确的标题：
- 如果内容涉及特定事件，可以搜索最新发展
- 如果涉及特定人物或组织，可以搜索相关背景
- 结合搜索结果生成更有新闻价值的标题

内容：
{content[:500]}

请先考虑是否需要使用firecrawl_search工具获取更多信息，然后生成标题。
最后只返回标题，不要包含其他内容。
"""

            # 首先尝试使用MCP增强的API调用
            api_response = None
            try:
                logger.info("尝试使用Firecrawl MCP增强的标题生成")
                api_response = await cls._call_ark_api_with_mcp_support(title_prompt)
            except Exception as mcp_error:
                logger.warning(f"MCP增强标题生成失败，回退到基础方法: {str(mcp_error)}")
                # 使用基础prompt
                basic_title_prompt = f"""
请为以下内容生成一个简洁、准确的标题，不超过30个字：

内容：
{content[:500]}

请只返回标题，不要包含其他内容。
"""
                api_response = await cls._call_ark_api(basic_title_prompt)

            if "choices" in api_response and len(api_response["choices"]) > 0:
                title = api_response["choices"][0].get("message", {}).get("content", "").strip()
                return title if title else "AI生成标题"

            return "AI生成标题"

        except Exception as e:
            logger.warning(f"AI标题生成失败: {str(e)}")
            return "AI生成标题"

    # 注释掉硬编码的标题提取方法，现在使用AI生成
    # 所有硬编码的文章解析方法已被AI方法替代

    # 注释掉硬编码的文本清理方法，AI提取不需要预处理
    # @classmethod
    # def _clean_text_for_title(cls, text: str) -> str:
    #     """
    #     清理文本用于标题提取（已废弃，AI提取不需要预处理）
    #
    #     :param text: 原始文本
    #     :return: 清理后的文本
    #     """
    #     import re
    #
    #     # 移除HTML标签
    #     text = re.sub(r'<[^>]+>', '', text)
    #
    #     # 移除多余的空白字符
    #     text = re.sub(r'\s+', ' ', text)
    #
    #     # 移除特殊字符和符号
    #     text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9。！？，、；：""''（）【】\s]', '', text)
    #
    #     # 移除开头的无用词汇
    #     useless_starts = [
    #         '根据', '据悉', '据了解', '据报道', '消息称', '有消息', '最新消息',
    #         '今日', '昨日', '近日', '日前', '目前', '现在', '当前',
    #         '记者', '本报', '新华社', '中新网'
    #     ]
    #
    #     for start in useless_starts:
    #         if text.startswith(start):
    #             text = text[len(start):].lstrip('，、：')
    #             break
    #
    #     return text.strip()

    # 注释掉硬编码的URL提取方法，AI提取会直接包含URL信息
    # @classmethod
    # def _extract_real_urls_from_content(cls, content: str) -> List[str]:
    #     """
    #     从内容中提取真实的新闻URL（已废弃，AI提取会直接包含URL信息）
    #
    #     :param content: 文章内容
    #     :return: 提取的URL列表
    #     """
    #     import re
    #
    #     urls = []
    #
    #     try:
    #         # URL正则模式 - 匹配常见新闻网站
    #         url_patterns = [
    #             # 完整的HTTP/HTTPS URL
    #             r'https?://[^\s<>"\']+',
    #             # 新闻网站特定模式
    #             r'(?:https?://)?(?:www\.)?(?:news\.|finance\.|tech\.|sports\.)?[a-zA-Z0-9-]+\.(?:com|cn|net|org|gov|edu)/[^\s<>"\']*',
    #         ]
    #
    #         for pattern in url_patterns:
    #             matches = re.findall(pattern, content, re.IGNORECASE)
    #             for match in matches:
    #                 clean_url = cls._validate_and_clean_url(match)
    #                 if clean_url and clean_url not in urls:
    #                     urls.append(clean_url)
    #
    #         # 过滤掉搜索引擎和无关URL
    #         filtered_urls = []
    #         exclude_domains = [
    #             'search.doubao.com',
    #             'google.com',
    #             'baidu.com',
    #             'bing.com',
    #             'yahoo.com',
    #             'sogou.com',
    #             '360.cn'
    #         ]
    #
    #         for url in urls:
    #             is_excluded = False
    #             for domain in exclude_domains:
    #                 if domain in url.lower():
    #                     is_excluded = True
    #                     break
    #
    #             if not is_excluded and cls._is_news_url(url):
    #                 filtered_urls.append(url)
    #
    #         return filtered_urls[:5]  # 最多返回5个URL
    #
    #     except Exception as e:
    #         logger.warning(f"提取URL时出错: {str(e)}")
    #         return []

    # 注释掉URL验证和清理方法，完全信任AI返回的URL
    # @classmethod
    # def _validate_and_clean_url(cls, url: str) -> str:
    #     """
    #     验证和清理URL（已废弃，完全信任AI返回的URL）
    #
    #     :param url: 原始URL
    #     :return: 清理后的URL，如果无效则返回空字符串
    #     """
    #     try:
    #         if not url or not url.strip():
    #             return ''
    #
    #         # 清理URL
    #         url = url.strip().rstrip('.,;!?')
    #
    #         # 过滤掉明显的搜索引擎和无效URL
    #         invalid_patterns = [
    #             'search.doubao.com',
    #             'baidu.com/s?',
    #             'google.com/search',
    #             'bing.com/search',
    #             'so.com/s?',
    #             'sogou.com/web',
    #             'example.com',
    #             'test.com',
    #             'placeholder'
    #         ]
    #
    #         url_lower = url.lower()
    #         for pattern in invalid_patterns:
    #             if pattern in url_lower:
    #                 logger.warning(f"过滤掉搜索引擎或无效URL: {url}")
    #                 return ''
    #
    #         # 添加协议前缀
    #         if not url.startswith(('http://', 'https://')):
    #             if url.startswith('www.'):
    #                 url = 'https://' + url
    #             elif '.' in url and not url.startswith('//'):
    #                 url = 'https://' + url
    #             else:
    #                 return ''
    #
    #         # 基本URL格式验证
    #         import re
    #         url_pattern = r'^https?://[^\s<>"\']+\.[a-zA-Z]{2,}[^\s<>"\']*$'
    #         if re.match(url_pattern, url):
    #             logger.info(f"URL验证通过: {url}")
    #             return url
    #
    #         logger.warning(f"URL格式验证失败: {url}")
    #         return ''
    #     except Exception as e:
    #         logger.error(f"URL验证异常: {str(e)}")
    #         return ''

    # 注释掉新闻URL判断方法，完全信任AI返回的URL
    # @classmethod
    # def _is_news_url(cls, url: str) -> bool:
    #     """
    #     判断是否为新闻URL（已废弃，完全信任AI返回的URL）
    #
    #     :param url: URL地址
    #     :return: 是否为新闻URL
    #     """
    #     try:
    #         if not url:
    #             return False
    #
    #         # 扩展的新闻网站域名关键词
    #         news_keywords = [
    #             'news', 'finance', 'tech', 'sports', 'business', 'economy',
    #             'xinhua', 'people', 'china', 'sina', 'sohu', 'netease',
    #             'tencent', 'ifeng', 'caixin', 'ftchinese', 'eastmoney',
    #             'cnstock', 'securities', 'wallstreetcn', 'jiemian',
    #             'thepaper', 'yicai', 'cls', 'gelonghui', 'qq.com',
    #             '163.com', 'chinanews', 'cctv', 'cri.cn', 'gmw.cn',
    #             'ce.cn', 'chinadaily', 'huanqiu', 'cankaoxiaoxi'
    #         ]
    #
    #         url_lower = url.lower()
    #
    #         # 检查域名是否包含新闻关键词
    #         for keyword in news_keywords:
    #             if keyword in url_lower:
    #                 logger.info(f"识别为新闻URL (域名匹配): {url}")
    #                 return True
    #
    #         # 检查路径是否包含新闻相关词汇
    #         path_keywords = [
    #             '/news/', '/article/', '/story/', '/report/', '/finance/',
    #             '/tech/', '/business/', '/economy/', '/politics/', '/sports/',
    #             '/a/', '/c/', '/n/', '/p/', '/detail/', '/content/'
    #         ]
    #
    #         for keyword in path_keywords:
    #             if keyword in url_lower:
    #                 logger.info(f"识别为新闻URL (路径匹配): {url}")
    #                 return True
    #
    #         # 检查文件扩展名，排除非新闻内容
    #         non_news_extensions = ['.jpg', '.png', '.gif', '.pdf', '.doc', '.zip']
    #         for ext in non_news_extensions:
    #             if url_lower.endswith(ext):
    #                 return False
    #
    #         return False
    #
    #     except Exception as e:
    #         logger.error(f"判断新闻URL时出错: {str(e)}")
    #         return False

    @classmethod
    def _generate_smart_news_url(cls, keyword: str, index: int = 0) -> str:
        """
        生成智能新闻网站搜索URL，优先使用真实新闻网站

        :param keyword: 搜索关键词
        :param index: 索引
        :return: 新闻搜索URL
        """
        try:
            import urllib.parse

            # URL编码关键词
            encoded_keyword = urllib.parse.quote(keyword)

            # 优先使用真实新闻网站的搜索功能
            news_sites = [
                f'https://news.sina.com.cn/search/?q={encoded_keyword}',
                f'https://news.163.com/search?keyword={encoded_keyword}',
                f'https://news.qq.com/search.htm?query={encoded_keyword}',
                f'https://www.chinanews.com.cn/search/search.jsp?q={encoded_keyword}',
                f'https://search.people.com.cn/search?keyword={encoded_keyword}',
                f'https://sou.chinadaily.com.cn/search.html?query={encoded_keyword}'
            ]

            if index < len(news_sites):
                selected_url = news_sites[index % len(news_sites)]
                logger.info(f"生成智能新闻搜索URL: {selected_url}")
                return selected_url
            else:
                # 如果索引超出范围，使用第一个
                logger.info(f"使用默认新闻搜索URL: {news_sites[0]}")
                return news_sites[0]

        except Exception as e:
            logger.error(f"生成智能新闻URL失败: {str(e)}")
            # 降级到通用搜索
            return cls._generate_search_url(keyword, index)

    @classmethod
    def _generate_search_url(cls, keyword: str, index: int = None) -> str:
        """
        生成通用搜索URL（降级使用）

        :param keyword: 搜索关键词
        :param index: 索引
        :return: 搜索URL
        """
        try:
            import urllib.parse

            # URL编码关键词
            encoded_keyword = urllib.parse.quote(keyword)

            # 生成多个搜索引擎的URL
            search_engines = [
                f'https://www.baidu.com/s?wd={encoded_keyword}',
                f'https://search.sina.com.cn/?q={encoded_keyword}',
                f'https://news.google.com/search?q={encoded_keyword}',
                f'https://www.so.com/s?q={encoded_keyword}'
            ]

            if index is not None and index < len(search_engines):
                return search_engines[index % len(search_engines)]
            else:
                return search_engines[0]  # 默认使用百度

        except Exception:
            return 'https://www.baidu.com/s?wd=热点新闻'

    # 注释掉硬编码的智能URL获取方法，AI提取会直接提供URL
    # @classmethod
    # def _get_smart_url(cls, item: dict, real_urls: List[str], index: int, keyword: str = '') -> str:
    #     """
    #     智能获取文章URL（已废弃，AI提取会直接提供URL）
    #
    #     :param item: 文章项目字典
    #     :param real_urls: 从内容中提取的真实URL列表
    #     :param index: 文章索引
    #     :param keyword: 搜索关键词
    #     :return: 最佳URL
    #     """
    #     # 1. 优先使用item中的URL
    #     item_url = item.get('url', item.get('link', ''))
    #     if item_url and cls._validate_and_clean_url(item_url):
    #         clean_url = cls._validate_and_clean_url(item_url)
    #         if clean_url and cls._is_news_url(clean_url):
    #             return clean_url
    #
    #     # 2. 使用从内容中提取的真实URL
    #     if real_urls and index < len(real_urls):
    #         return real_urls[index]
    #     elif real_urls:
    #         return real_urls[0]  # 如果索引超出范围，使用第一个
    #
    #     # 3. 生成有意义的搜索URL
    #     if keyword:
    #         return cls._generate_search_url(keyword, index)
    #     else:
    #         return cls._generate_search_url('热点新闻', index)

    # 注释掉硬编码的情感分析方法，现在使用AI智能分析
    # @classmethod
    # def _extract_sentiment_from_content(cls, content: str) -> Dict[str, int]:
    #     """
    #     从内容中提取情感分析（已废弃，使用AI智能分析）
    #     """
    #     # 简化实现，返回模拟数据
    #     return {
    #         'positive': 60,
    #         'neutral': 30,
    #         'negative': 10
    #     }

    # 注释掉硬编码的趋势分析方法，后续可以集成AI趋势分析
    # @classmethod
    # def _extract_trends_from_content(cls, content: str) -> List[Dict[str, Any]]:
    #     """
    #     从内容中提取趋势分析（已废弃，后续集成AI分析）
    #     """
    #     # 简化实现，返回模拟数据
    #     return [
    #         {
    #             'keyword': '热度上升',
    #             'trend': 'up',
    #             'score': 85
    #         }
    #     ]
    
    @classmethod
    def _get_current_timestamp(cls) -> str:
        """
        获取当前时间戳
        """
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    @classmethod
    async def _analyze_text_sentiment(cls, text: str) -> str:
        """
        智能文本情感分析（优先使用AI，降级到本地分析）
        """
        from module_opinion.service.sentiment_cache_service import SentimentCacheService

        # 首先检查缓存
        cached_result = SentimentCacheService.get_cached_sentiment(text)
        if cached_result:
            logger.info(f"使用缓存的情感分析结果: {cached_result}")
            return cached_result

        sentiment_result = 'neutral'

        try:
            # 优先使用MCP增强的AI情感分析
            ai_sentiment = await cls._ai_sentiment_analysis_with_mcp(text)
            if ai_sentiment:
                sentiment_result = ai_sentiment
                logger.info(f"使用MCP增强AI情感分析结果: {ai_sentiment}")
            else:
                # 降级到基础AI分析
                ai_sentiment = await cls._ai_sentiment_analysis(text)
                if ai_sentiment:
                    sentiment_result = ai_sentiment
                    logger.info(f"使用基础AI情感分析结果: {ai_sentiment}")
                else:
                    # 最终降级到本地关键词分析
                    sentiment_result = cls._local_sentiment_analysis(text)
                    logger.info(f"使用本地情感分析结果: {sentiment_result}")
        except Exception as e:
            logger.warning(f"AI情感分析失败，降级到本地分析: {str(e)}")
            # 降级到本地关键词分析
            sentiment_result = cls._local_sentiment_analysis(text)

        # 将结果存入缓存
        SentimentCacheService.set_cached_sentiment(text, sentiment_result)

        return sentiment_result

    @classmethod
    def _calculate_sentiment_statistics(cls, articles: List[Dict[str, Any]]) -> Dict[str, int]:
        """
        计算文章列表的情感分析统计

        :param articles: 文章列表
        :return: 情感分析统计结果（百分比）
        """
        if not articles:
            return {'positive': 0, 'neutral': 0, 'negative': 0}

        # 统计各种情感的数量
        sentiment_counts = {'positive': 0, 'neutral': 0, 'negative': 0}

        for article in articles:
            sentiment = article.get('sentiment', 'neutral')
            if sentiment in sentiment_counts:
                sentiment_counts[sentiment] += 1
            else:
                sentiment_counts['neutral'] += 1

        total_articles = len(articles)

        # 计算百分比
        sentiment_percentages = {}
        for sentiment, count in sentiment_counts.items():
            percentage = round((count / total_articles) * 100) if total_articles > 0 else 0
            sentiment_percentages[sentiment] = percentage

        logger.info(f"情感分析统计: {sentiment_percentages} (总文章数: {total_articles})")
        return sentiment_percentages

    @classmethod
    async def _ai_sentiment_analysis_with_mcp(cls, text: str) -> str:
        """
        使用豆包AI进行情感分析（MCP增强版）
        """
        try:
            from module_opinion.config.sentiment_config import SentimentConfig

            # 检查是否启用AI分析
            if not SentimentConfig.is_ai_enabled():
                return None

            # 使用配置文件中的prompt模板
            sentiment_prompt = SentimentConfig.get_ai_prompt(text)

            # 调用豆包AI API（使用MCP支持）
            api_response = await cls._call_ark_api_with_mcp_support(sentiment_prompt)

            if api_response.get('success') and 'choices' in api_response:
                ai_result = api_response['choices'][0]['message']['content'].strip().lower()

                # 解析AI返回的情感结果
                if 'positive' in ai_result or '积极' in ai_result:
                    return 'positive'
                elif 'negative' in ai_result or '消极' in ai_result:
                    return 'negative'
                elif 'neutral' in ai_result or '中性' in ai_result:
                    return 'neutral'
                else:
                    # 如果AI返回的格式不标准，尝试从内容中提取
                    if any(word in ai_result for word in ['好', '积极', '正面', '支持', '赞']):
                        return 'positive'
                    elif any(word in ai_result for word in ['坏', '消极', '负面', '批评', '反对']):
                        return 'negative'
                    else:
                        return 'neutral'

            return None

        except Exception as e:
            logger.error(f"MCP增强AI情感分析调用失败: {str(e)}")
            return None

    @classmethod
    async def _ai_sentiment_analysis(cls, text: str) -> str:
        """
        使用豆包AI进行情感分析
        """
        try:
            from module_opinion.config.sentiment_config import SentimentConfig

            # 检查是否启用AI分析
            if not SentimentConfig.is_ai_enabled():
                return None

            # 使用配置文件中的prompt模板
            sentiment_prompt = SentimentConfig.get_ai_prompt(text)

            # 调用豆包AI API（使用MCP支持）
            api_response = await cls._call_ark_api_with_mcp_support(sentiment_prompt)

            if api_response.get('success') and 'choices' in api_response:
                ai_result = api_response['choices'][0]['message']['content'].strip().lower()

                # 解析AI返回的情感结果
                if 'positive' in ai_result or '积极' in ai_result:
                    return 'positive'
                elif 'negative' in ai_result or '消极' in ai_result:
                    return 'negative'
                elif 'neutral' in ai_result or '中性' in ai_result:
                    return 'neutral'
                else:
                    # 如果AI返回的格式不标准，尝试从内容中提取
                    if any(word in ai_result for word in ['好', '积极', '正面', '支持', '赞']):
                        return 'positive'
                    elif any(word in ai_result for word in ['坏', '消极', '负面', '批评', '反对']):
                        return 'negative'
                    else:
                        return 'neutral'

            return None

        except Exception as e:
            logger.error(f"AI情感分析调用失败: {str(e)}")
            return None

    @classmethod
    def _local_sentiment_analysis(cls, text: str) -> str:
        """
        本地关键词情感分析（备选方案）
        """
        from module_opinion.config.sentiment_config import SentimentConfig

        # 使用配置文件中的关键词列表
        positive_words = SentimentConfig.POSITIVE_KEYWORDS
        negative_words = SentimentConfig.NEGATIVE_KEYWORDS
        neutral_words = SentimentConfig.NEUTRAL_KEYWORDS

        text_lower = text.lower()
        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)
        neutral_count = sum(1 for word in neutral_words if word in text_lower)

        # 改进的权重计算，考虑中性词汇的影响
        total_sentiment_words = positive_count + negative_count + neutral_count

        if total_sentiment_words == 0:
            return 'neutral'

        # 计算情感倾向比例
        positive_ratio = positive_count / total_sentiment_words
        negative_ratio = negative_count / total_sentiment_words

        # 使用配置的置信度阈值
        threshold = SentimentConfig.CONFIDENCE_THRESHOLD

        if positive_ratio > threshold and positive_count > negative_count:
            return 'positive'
        elif negative_ratio > threshold and negative_count > positive_count:
            return 'negative'
        else:
            return 'neutral'

    @classmethod
    async def _save_search_results_to_db(cls, query_db: AsyncSession, processed_results: Dict[str, Any],
                                       entity_keyword: str, selected_keywords: List[str]) -> int:
        """
        将搜索结果保存到keyword_data表

        :param query_db: 数据库会话
        :param processed_results: 处理后的搜索结果
        :param entity_keyword: 实体关键词
        :param selected_keywords: 选中的关键词列表
        :return: 保存的记录数量
        """
        try:
            saved_count = 0
            articles = processed_results.get('articles', [])

            logger.info(f"开始保存搜索结果到数据库")
            logger.info(f"实体关键词: {entity_keyword}")
            logger.info(f"选中关键词: {selected_keywords}")
            logger.info(f"搜索结果文章数量: {len(articles)}")

            if not articles:
                logger.warning("没有文章数据需要保存")
                return 0

            # 将所有关键词合并为一个字符串
            all_keywords = [entity_keyword] + selected_keywords
            keywords_str = ','.join(all_keywords)

            logger.info(f"合并后的关键词字符串: {keywords_str}")

            for i, article in enumerate(articles):
                try:
                    logger.info(f"正在保存第 {i+1} 条文章: {article.get('title', '无标题')}")

                    # 数据验证和清理
                    title = article.get('title', f'联网搜索结果 {i+1}')[:255]  # 限制长度
                    content = article.get('content', '')[:10000]  # 限制长度

                    # 直接使用AI返回的原始URL，不进行任何验证或替换
                    url = article.get('url', '')[:255]  # 限制长度，但不验证或替换
                    logger.info(f"保存AI返回的原始URL: {url}")

                    web = article.get('source', '豆包AI搜索')[:255]  # 限制长度
                    sentiment = article.get('sentiment', 'neutral')

                    # 确保sentiment值有效
                    if sentiment not in ['positive', 'negative', 'neutral']:
                        sentiment = 'neutral'

                    # 创建KeywordDataModel对象
                    keyword_data = KeywordDataModel(
                        title=title,
                        content=content,
                        url=url,
                        keyword=keywords_str,
                        type='online-search',  # 标记为联网搜索来源
                        web=web,
                        sentiment=sentiment
                    )

                    logger.info(f"准备保存数据: title={title[:50]}..., url={url}, sentiment={sentiment}")

                    # 保存到数据库
                    result = await KeywordDataDao.add_keyword_data_dao(query_db, keyword_data)
                    if result:
                        saved_count += 1
                        logger.info(f"成功保存第 {i+1} 条文章，ID: {result.id}")
                    else:
                        logger.error(f"保存第 {i+1} 条文章失败：数据库操作返回空结果")

                except Exception as e:
                    logger.error(f"保存第 {i+1} 条搜索结果失败: {str(e)}")
                    logger.error(f"文章数据: {article}")
                    continue

            # 提交事务
            await query_db.commit()
            logger.info(f"成功保存 {saved_count} 条搜索结果到数据库")
            return saved_count

        except Exception as e:
            logger.error(f"保存搜索结果到数据库失败: {str(e)}")
            await query_db.rollback()
            return 0
