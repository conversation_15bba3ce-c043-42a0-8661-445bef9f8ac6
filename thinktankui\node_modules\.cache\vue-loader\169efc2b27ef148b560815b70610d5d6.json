{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue?vue&type=style&index=0&id=040a21b8&lang=scss&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue", "mtime": 1753695251423}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1753065270777}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1753065272975}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1753065271542}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1753065270150}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753065270161}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753065272328}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAwjKA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/opinion-analysis", "sourcesContent": ["<template>\n  <div class=\"opinion-analysis\">\n    <!-- 步骤指示器 -->\n    <div class=\"steps-container\">\n      <!-- 左侧占位区域，保持布局平衡 -->\n      <div class=\"left-placeholder\"></div>\n\n      <!-- 步骤指示器 -->\n      <div class=\"steps-wrapper\">\n        <div class=\"step-item\" :class=\"{ active: currentStep === 1 }\">\n          <span class=\"step-number\">1</span>\n          <span class=\"step-text\">舆情分析来源</span>\n        </div>\n        <div class=\"step-item\" :class=\"{ active: currentStep === 2 }\">\n          <span class=\"step-number\">2</span>\n          <span class=\"step-text\">数据概览</span>\n        </div>\n        <div class=\"step-item\" :class=\"{ active: currentStep === 3 }\">\n          <span class=\"step-number\">3</span>\n          <span class=\"step-text\">分析进度</span>\n        </div>\n        <div class=\"step-item\" :class=\"{ active: currentStep === 4 }\">\n          <span class=\"step-number\">4</span>\n          <span class=\"step-text\">报告预览</span>\n        </div>\n      </div>\n\n      <!-- 右侧按钮区域 -->\n      <div class=\"right-actions\">\n        \n\n        <el-button\n          class=\"analyze-record-btn\"\n          type=\"info\"\n          size=\"small\"\n          @click=\"goToAnalyzeRecord\"\n        >\n          分析记录\n        </el-button>\n        <el-button\n          class=\"timed-push-btn\"\n          type=\"primary\"\n          size=\"small\"\n          @click=\"handleTimedPush\"\n        >\n          定时推送\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 主要内容区域 -->\n    <div class=\"main-content\">\n      <!-- 第一步：分析需求 -->\n      <div v-if=\"currentStep === 1\" class=\"analysis-source\">\n        <div class=\"section-header\">\n          <h2 class=\"section-title\">分析需求</h2>\n          <el-button\n            type=\"primary\"\n            size=\"small\"\n            @click=\"handleTemplateClick\"\n            class=\"template-btn\"\n          >\n            模板\n          </el-button>\n        </div>\n<div class=\"input-section\">\n          <div class=\"input-label\">\n            需求名称\n            <span class=\"required\">*</span>\n          </div>\n          <el-input\n            v-model=\"requirementName\"\n            placeholder=\"请输入需求名称\"\n            class=\"entity-input\"\n            :class=\"{ 'error': !requirementName.trim() && showValidation }\"\n          />\n        </div>\n        <!-- 实体关键词区域 -->\n        <div class=\"input-section\">\n          <div class=\"input-label\">\n            实体关键词\n            <span class=\"required\">*</span>\n          </div>\n          <el-input\n            v-model=\"entityKeyword\"\n            placeholder=\"请输入1个本次舆情分析最关注的实体词，例如你关注的品牌名称、产品名、人物名字等\"\n            class=\"entity-input\"\n            :class=\"{ 'error': !entityKeyword.trim() && showValidation }\"\n          />\n        </div>\n\n        <!-- 具体需求区域 -->\n        <div class=\"input-section\">\n          <div class=\"input-label\">\n            具体需求\n            <span class=\"required\">*</span>\n          </div>\n          <el-input\n            v-model=\"specificRequirement\"\n            type=\"textarea\"\n            :rows=\"4\"\n            placeholder=\"请描述你在本次分析中具体关注哪些条件，例如，分析社交媒体中与拍子空间相关的消费主题及情感倾向（正面/中性/负面）\"\n            class=\"requirement-textarea\"\n            :class=\"{ 'error': !specificRequirement.trim() && showValidation }\"\n          />\n        </div>\n\n        <!-- 选择关联词区域 -->\n        <div class=\"related-words-section\">\n          <div class=\"section-header\">\n            <div class=\"header-left\">\n              <span class=\"section-label\">选择关联词</span>\n              <span class=\"word-count\" :class=\"{ 'max-reached': selectedKeywords.length >= maxKeywords }\">\n                ({{ selectedKeywords.length }}/{{ maxKeywords }})\n              </span>\n            </div>\n            <el-button\n              v-if=\"generatedKeywords.length > 0\"\n              class=\"regenerate-btn\"\n              size=\"mini\"\n              type=\"text\"\n              @click=\"regenerateKeywords\"\n            >\n              <i class=\"el-icon-refresh\"></i>\n              重新生成\n            </el-button>\n          </div>\n\n          <div class=\"keywords-textbox-wrapper\">\n            <!-- 显示生成的关键词 -->\n            <div v-if=\"generatedKeywords.length > 0\" class=\"generated-keywords-display\">\n              <div v-for=\"(category, categoryName) in groupedKeywords\" :key=\"categoryName\" class=\"keyword-category\">\n                <el-button\n                  class=\"category-button\"\n                  size=\"small\"\n                  type=\"primary\"\n                  plain\n                  @click=\"toggleCategorySelection(categoryName, category)\"\n                >\n                  {{ categoryName }}\n                </el-button>\n                <div class=\"keyword-tags\">\n                  <el-tag\n                    v-for=\"(keyword, index) in category\"\n                    :key=\"index\"\n                    :class=\"['keyword-tag', { selected: isKeywordSelected(keyword) }]\"\n                    @click=\"toggleKeyword(keyword)\"\n                  >\n                    {{ keyword }}\n                  </el-tag>\n                </div>\n              </div>\n            </div>\n\n            <!-- 生成关联词按钮区域 -->\n            <div v-if=\"generatedKeywords.length === 0\" class=\"words-container\">\n              <div class=\"generate-word-btn\" @click=\"generateRelatedWords\">\n                <i class=\"el-icon-magic-stick\"></i>\n                <span>生成关联词</span>\n              </div>\n              <div class=\"word-description\">\n                根据你填写的需求和关键词生成关联词\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 第二步：数据概览 -->\n      <div v-if=\"currentStep === 2\" class=\"data-overview\">\n        <h2 class=\"section-title\">选择数据来源</h2>\n\n        <!-- 数据来源选项 -->\n        <div class=\"data-source-section\">\n          <!-- 联网搜索选项 -->\n          <div class=\"source-option\" @click=\"toggleOnlineSearch\">\n            <el-checkbox\n              v-model=\"enableOnlineSearch\"\n              class=\"source-checkbox\"\n            ></el-checkbox>\n            <div class=\"source-icon\">\n              <i class=\"el-icon-search\"></i>\n            </div>\n            <div class=\"source-content\">\n              <h3>联网搜索</h3>\n              <p class=\"source-description\">使用AI搜索引擎获取最新网络信息</p>\n            </div>\n          </div>\n\n          <!-- 自定义数据源搜索选项 -->\n          <div class=\"source-option\" @click=\"toggleCustomDataSource\">\n            <el-checkbox\n              v-model=\"enableCustomDataSource\"\n              class=\"source-checkbox\"\n            ></el-checkbox>\n            <div class=\"source-icon\">\n              <i class=\"el-icon-link\"></i>\n            </div>\n            <div class=\"source-content\">\n              <h3>自定义数据源搜索 <span style=\"color: #909399; font-size: 12px;\">(可选)</span></h3>\n              <p class=\"source-description\">\n                从已配置的数据源网站抓取相关信息，可与联网搜索配合使用\n                <span v-if=\"customDataSources.length > 0\" class=\"source-count\">\n                  ({{ customDataSources.length }}个数据源)\n                </span>\n              </p>\n            </div>\n          </div>\n\n          <!-- 数据源列表区域 -->\n          <div v-if=\"enableCustomDataSource\" class=\"data-source-list-section\">\n            <div class=\"list-header\">\n              <h4>数据源列表</h4>\n              <div class=\"header-controls\">\n                <el-button\n                  size=\"small\"\n                  type=\"primary\"\n                  @click=\"showAddSourceForm\"\n                >\n                  <i class=\"el-icon-plus\"></i>\n                  新增数据源\n                </el-button>\n                <el-button\n                  size=\"small\"\n                  type=\"success\"\n                  @click=\"refreshDataSourceList\"\n                >\n                  <i class=\"el-icon-refresh\"></i>\n                  刷新\n                </el-button>\n              </div>\n            </div>\n\n            <!-- 数据源表格 -->\n            <div class=\"data-source-table\">\n              <el-table\n                :data=\"paginatedDataSources\"\n                v-loading=\"dataSourceListState.loading\"\n                style=\"width: 100%\"\n                empty-text=\"暂无数据源\"\n                size=\"small\"\n                @selection-change=\"handleDataSourceSelectionChange\"\n              >\n                <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n                <el-table-column prop=\"sourceUrl\" label=\"数据源URL\">\n                  <template slot-scope=\"scope\">\n                    <div class=\"url-cell\">\n                      <i class=\"el-icon-link\"></i>\n                      <span class=\"url-text\">{{ scope.row.sourceUrl }}</span>\n                    </div>\n                  </template>\n                </el-table-column>\n                <el-table-column label=\"操作\" width=\"80\" fixed=\"right\">\n                  <template slot-scope=\"scope\">\n                    <el-button\n                      type=\"text\"\n                      size=\"mini\"\n                      @click=\"deleteDataSource(scope.row)\"\n                      title=\"删除\"\n                      style=\"color: #f56c6c;\"\n                    >\n                      <i class=\"el-icon-delete\"></i>\n                    </el-button>\n                  </template>\n                </el-table-column>\n              </el-table>\n\n              <!-- 分页 -->\n              <div class=\"pagination-wrapper\" v-if=\"dataSourceList.length > 0\">\n                <el-pagination\n                  @current-change=\"handleDataSourcePageChange\"\n                  @size-change=\"handleDataSourceSizeChange\"\n                  :current-page=\"dataSourceListState.current_page\"\n                  :page-sizes=\"[10, 20, 50]\"\n                  :page-size=\"dataSourceListState.page_size\"\n                  :total=\"dataSourceList.length\"\n                  layout=\"total, sizes, prev, pager, next\"\n                  small\n                />\n              </div>\n            </div>\n          </div>\n\n          <!-- 新增数据源表单 -->\n          <div v-if=\"showAddSourceInput\" class=\"add-source-form\">\n            <div class=\"form-header\">\n              <h3>新增数据源</h3>\n              <i class=\"el-icon-close\" @click=\"hideAddSourceForm\"></i>\n            </div>\n            <div class=\"form-item\">\n              <label class=\"form-label\">\n                数据源网址\n                <span class=\"required\">*</span>\n              </label>\n              <div class=\"input-group\">\n                <el-input\n                  v-model=\"newSourceUrl\"\n                  placeholder=\"请输入网址，例如：https://www.example.com\"\n                  class=\"source-url-input\"\n                  @keyup.enter=\"confirmAddSource\"\n                />\n                <el-button type=\"primary\" @click=\"confirmAddSource\">确定</el-button>\n              </div>\n            </div>\n          </div>\n\n          <!-- 新增来源按钮 -->\n          <div v-if=\"!showAddSourceInput\" class=\"add-source-btn\" @click=\"showAddSourceForm\">\n            <i class=\"el-icon-plus\"></i>\n            <span>新增来源</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- 第三步：分析进度 -->\n      <div v-if=\"currentStep === 3\" class=\"analysis-progress\">\n        <h2 class=\"section-title\">分析进度</h2>\n\n        <!-- 分析状态概览 -->\n        <div class=\"progress-overview\">\n          <div class=\"status-card\">\n            <div class=\"status-header\">\n              <h3>当前状态</h3>\n              <div class=\"status-indicator\" :class=\"analysisStatus\">\n                <span class=\"status-dot\"></span>\n                <span class=\"status-text\">{{ getAnalysisStatusText() }}</span>\n              </div>\n            </div>\n            <div class=\"progress-bar-container\">\n              <div class=\"progress-bar\">\n                <div class=\"progress-fill\" :style=\"{ width: analysisProgress + '%' }\"></div>\n              </div>\n              <span class=\"progress-text\">{{ analysisProgress }}%</span>\n            </div>\n          </div>\n        </div>\n\n        <!-- 实时日志显示 -->\n        <div class=\"real-time-logs\">\n          <div class=\"logs-header\">\n            <h3>分析日志</h3>\n            <div class=\"logs-controls\">\n              <el-button size=\"mini\" @click=\"clearLogs\" type=\"text\">清空日志</el-button>\n              <el-button size=\"mini\" @click=\"toggleAutoScroll\" type=\"text\">\n                {{ autoScroll ? '停止滚动' : '自动滚动' }}\n              </el-button>\n            </div>\n          </div>\n          <div class=\"logs-container\" ref=\"logsContainer\">\n            <div v-if=\"analysisLogs.length === 0\" class=\"no-logs\">\n              <i class=\"el-icon-loading\"></i>\n              <span>等待分析开始...</span>\n            </div>\n            <div v-else class=\"logs-list\">\n              <div\n                v-for=\"(log, index) in analysisLogs\"\n                :key=\"index\"\n                class=\"log-item\"\n                :class=\"log.level\"\n              >\n                <span class=\"log-time\">{{ formatLogTime(log.timestamp) }}</span>\n                <span class=\"log-level\">{{ (log.level || 'info').toUpperCase() }}</span>\n                <span class=\"log-message\">{{ log.message }}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 分析完成后的操作 -->\n        <div v-if=\"analysisStatus === 'completed'\" class=\"analysis-completed\">\n          <div class=\"completion-message\">\n            <i class=\"el-icon-success\"></i>\n            <span>分析已完成！</span>\n          </div>\n          <el-button type=\"primary\" size=\"large\" @click=\"goToReportPreview\">\n            查看分析报告\n          </el-button>\n        </div>\n\n        <!-- 分析失败时的操作 -->\n        <div v-if=\"analysisStatus === 'failed'\" class=\"analysis-failed\">\n          <div class=\"failure-message\">\n            <i class=\"el-icon-error\"></i>\n            <span>分析失败，请重试</span>\n          </div>\n          <el-button type=\"danger\" size=\"large\" @click=\"retryAnalysis\">\n            重新分析\n          </el-button>\n        </div>\n      </div>\n\n      <!-- 第四步：报告预览 -->\n      <div v-if=\"currentStep === 4\" class=\"report-preview\">\n        <h2 class=\"section-title\">分析报告预览</h2>\n\n        <!-- 报告概览 -->\n        <div class=\"report-overview\">\n          <div class=\"overview-card\">\n            <div class=\"card-header\">\n              <h3>分析概览</h3>\n              <span class=\"analysis-time\">{{ formatAnalysisTime(new Date()) }}</span>\n            </div>\n            <div class=\"overview-stats\">\n              <div class=\"stat-item\">\n                <div class=\"stat-number\">{{ reportData.totalArticles || 0 }}</div>\n                <div class=\"stat-label\">相关文章</div>\n              </div>\n              <div class=\"stat-item\">\n                <div class=\"stat-number\">{{ reportData.totalKeywords || selectedKeywords.length }}</div>\n                <div class=\"stat-label\">关键词</div>\n              </div>\n              <div class=\"stat-item\">\n                <div class=\"stat-number\">{{ reportData.dataSources || (enableOnlineSearch ? 1 : 0) + customDataSources.length }}</div>\n                <div class=\"stat-label\">数据源</div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 情感分析结果 -->\n        <div class=\"sentiment-analysis\">\n          <div class=\"analysis-card\">\n            <h3>情感倾向分析</h3>\n            <div class=\"sentiment-chart\">\n              <div class=\"sentiment-item positive\">\n                <div class=\"sentiment-bar\">\n                  <div class=\"bar-fill\" :style=\"{ width: (reportData.sentiment?.positive || 0) + '%' }\"></div>\n                </div>\n                <div class=\"sentiment-info\">\n                  <span class=\"sentiment-label\">正面</span>\n                  <span class=\"sentiment-value\">{{ reportData.sentiment?.positive || 0 }}%</span>\n                </div>\n              </div>\n              <div class=\"sentiment-item neutral\">\n                <div class=\"sentiment-bar\">\n                  <div class=\"bar-fill\" :style=\"{ width: (reportData.sentiment?.neutral || 0) + '%' }\"></div>\n                </div>\n                <div class=\"sentiment-info\">\n                  <span class=\"sentiment-label\">中性</span>\n                  <span class=\"sentiment-value\">{{ reportData.sentiment?.neutral || 0 }}%</span>\n                </div>\n              </div>\n              <div class=\"sentiment-item negative\">\n                <div class=\"sentiment-bar\">\n                  <div class=\"bar-fill\" :style=\"{ width: (reportData.sentiment?.negative || 0) + '%' }\"></div>\n                </div>\n                <div class=\"sentiment-info\">\n                  <span class=\"sentiment-label\">负面</span>\n                  <span class=\"sentiment-value\">{{ reportData.sentiment?.negative || 0 }}%</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 关键词分析 -->\n        <div class=\"keyword-analysis\">\n          <div class=\"analysis-card\">\n            <h3>关键词分析</h3>\n            <div class=\"selected-keywords-display\">\n              <div class=\"keyword-list\">\n                <div class=\"keyword-item\" v-for=\"(keyword, index) in selectedKeywords\" :key=\"index\">\n                  <span class=\"keyword-text\">{{ keyword }}</span>\n                  <span class=\"keyword-frequency\">{{ getKeywordFrequency(keyword) }}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 数据来源统计 -->\n        <div class=\"data-source-stats\">\n          <div class=\"analysis-card\">\n            <h3>数据来源统计</h3>\n            <div class=\"source-list\">\n              <div v-if=\"enableOnlineSearch\" class=\"source-item\">\n                <div class=\"source-icon online\">\n                  <i class=\"el-icon-search\"></i>\n                </div>\n                <div class=\"source-info\">\n                  <div class=\"source-name\">联网搜索</div>\n                  <div class=\"source-desc\">AI搜索引擎数据</div>\n                </div>\n                <div class=\"source-count\">{{ reportData.onlineSearchCount || 0 }} 条</div>\n              </div>\n              <div v-for=\"(source, index) in customDataSources\" :key=\"index\" class=\"source-item\">\n                <div class=\"source-icon custom\">\n                  <i class=\"el-icon-link\"></i>\n                </div>\n                <div class=\"source-info\">\n                  <div class=\"source-name\">{{ extractDomainName(source) }}</div>\n                  <div class=\"source-desc\">自定义数据源</div>\n                </div>\n                <div class=\"source-count\">{{ getSourceCount(source) }} 条</div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 详细数据 -->\n        <div class=\"detailed-data\" v-if=\"allArticles.length > 0\">\n          <div class=\"analysis-card\">\n            <div class=\"card-header-with-controls\">\n              <h3>详细数据</h3>\n              <div class=\"data-controls\">\n                <el-input\n                  v-model=\"articleListState.searchKeyword\"\n                  placeholder=\"搜索文章标题或内容\"\n                  prefix-icon=\"el-icon-search\"\n                  size=\"small\"\n                  style=\"width: 200px; margin-right: 12px;\"\n                  clearable\n                />\n                <el-select\n                  v-model=\"articleListState.selectedSource\"\n                  placeholder=\"筛选来源\"\n                  size=\"small\"\n                  style=\"width: 120px; margin-right: 12px;\"\n                  clearable\n                >\n                  <el-option label=\"全部来源\" value=\"\"></el-option>\n                  <el-option\n                    v-for=\"source in uniqueSources\"\n                    :key=\"source\"\n                    :label=\"source\"\n                    :value=\"source\"\n                  ></el-option>\n                </el-select>\n                <el-select\n                  v-model=\"articleListState.selectedSentiment\"\n                  placeholder=\"筛选情感\"\n                  size=\"small\"\n                  style=\"width: 100px;\"\n                  clearable\n                >\n                  <el-option label=\"全部情感\" value=\"\"></el-option>\n                  <el-option label=\"正面\" value=\"positive\"></el-option>\n                  <el-option label=\"中性\" value=\"neutral\"></el-option>\n                  <el-option label=\"负面\" value=\"negative\"></el-option>\n                </el-select>\n              </div>\n            </div>\n\n            <div class=\"article-list\">\n              <div\n                v-for=\"(article, index) in paginatedArticles\"\n                :key=\"`article-${index}`\"\n                class=\"article-item\"\n              >\n                <div class=\"article-header\">\n                  <div class=\"article-title-row\">\n                    <h4 class=\"article-title\" @click=\"toggleArticleExpand(index)\">\n                      {{ article.title }}\n                      <i :class=\"isArticleExpanded(index) ? 'el-icon-arrow-up' : 'el-icon-arrow-down'\"></i>\n                    </h4>\n                    <div class=\"article-meta\">\n                      <span class=\"article-source\">{{ article.source }}</span>\n                      <span :class=\"['sentiment-tag', article.sentiment]\">\n                        {{ getSentimentLabel(article.sentiment) }}\n                      </span>\n                    </div>\n                  </div>\n                  <div class=\"article-info\">\n                    <span class=\"publish-time\" v-if=\"article.publish_time\">\n                      {{ formatPublishTime(article.publish_time) }}\n                    </span>\n                    <div class=\"article-actions\">\n                      <el-button\n                        type=\"text\"\n                        size=\"mini\"\n                        icon=\"el-icon-copy-document\"\n                        @click=\"copyArticleContent(article)\"\n                      >\n                        复制\n                      </el-button>\n                      <el-button\n                        v-if=\"article.url && article.url.trim()\"\n                        type=\"text\"\n                        size=\"mini\"\n                        icon=\"el-icon-link\"\n                        @click=\"openArticleUrl(article.url)\"\n                        :title=\"getUrlTooltip(article.url)\"\n                      >\n                        原文链接\n                      </el-button>\n                    </div>\n                  </div>\n                </div>\n\n                <div class=\"article-content\" v-if=\"!isArticleExpanded(index)\">\n                  <p class=\"content-summary\">{{ getContentSummary(article.content) }}</p>\n                </div>\n\n                <div class=\"article-content expanded\" v-if=\"isArticleExpanded(index)\">\n                  <p class=\"content-full\">{{ article.content }}</p>\n                </div>\n              </div>\n            </div>\n\n            <!-- 分页 -->\n            <div class=\"pagination-wrapper\" v-if=\"filteredArticles.length > 0\">\n              <el-pagination\n                @current-change=\"handlePageChange\"\n                :current-page=\"articleListState.currentPage\"\n                :page-size=\"articleListState.pageSize\"\n                :total=\"filteredArticles.length\"\n                layout=\"prev, pager, next, total\"\n                small\n              />\n            </div>\n          </div>\n        </div>\n\n        <!-- 报告操作 -->\n        <div class=\"report-actions\">\n          <div class=\"action-buttons\">\n            <el-button size=\"large\" icon=\"el-icon-download\">导出报告</el-button>\n            <el-button size=\"large\" icon=\"el-icon-share\">分享报告</el-button>\n            <el-button type=\"primary\" size=\"large\" icon=\"el-icon-s-promotion\">生成完整报告</el-button>\n          </div>\n        </div>\n      </div>\n\n      <!-- 底部按钮区域 -->\n      <div class=\"bottom-actions\">\n        <el-button v-if=\"currentStep === 2 || currentStep === 4\" @click=\"goToPreviousStep\" size=\"large\">上一步</el-button>\n        <el-button\n          v-if=\"currentStep === 1\"\n          @click=\"goToNextStep\"\n          type=\"primary\"\n          size=\"large\"\n          :disabled=\"!canGoToNextStep\"\n        >下一步</el-button>\n        <el-button v-if=\"currentStep === 2\" @click=\"startAnalysis\" type=\"primary\" size=\"large\">开始分析</el-button>\n        <el-button v-if=\"currentStep === 3 && analysisStatus === 'running'\" @click=\"cancelAnalysis\" size=\"large\">取消分析</el-button>\n        <el-button v-if=\"currentStep === 4\" @click=\"showPushDialog\" type=\"primary\" size=\"large\">\n          <i class=\"el-icon-s-promotion\"></i>\n          推送报告\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 定时任务抽屉 -->\n    <el-drawer\n      title=\"定时任务\"\n      :visible.sync=\"timedTaskDialogVisible\"\n      direction=\"rtl\"\n      size=\"600px\"\n      :before-close=\"closeTimedTaskDialog\"\n      custom-class=\"timed-task-drawer\"\n    >\n      <!-- 抽屉头部右侧按钮 -->\n      <div slot=\"title\" class=\"drawer-header\">\n        <span class=\"drawer-title\">定时任务</span>\n        <el-button\n          type=\"primary\"\n          size=\"mini\"\n          icon=\"el-icon-plus\"\n          @click=\"handleAddTimedTask\"\n          class=\"add-task-btn\"\n        >\n          定时任务\n        </el-button>\n      </div>\n\n      <!-- 抽屉内容 -->\n      <div class=\"drawer-content\">\n        <!-- 空状态 -->\n        <div v-if=\"timedTaskList.length === 0\" class=\"empty-state\">\n          <div class=\"empty-content\">\n            <!-- 空状态图标 -->\n            <div class=\"empty-icon\">\n              <svg width=\"120\" height=\"120\" viewBox=\"0 0 120 120\" fill=\"none\">\n                <!-- 文件夹图标 -->\n                <path d=\"M20 30h25l5-10h50v70H20V30z\" fill=\"#f0f0f0\" stroke=\"#d0d0d0\" stroke-width=\"2\"/>\n                <path d=\"M25 35h70v50H25V35z\" fill=\"#fafafa\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n                <!-- 文档图标 -->\n                <rect x=\"35\" y=\"45\" width=\"30\" height=\"25\" fill=\"#ffffff\" stroke=\"#d0d0d0\" stroke-width=\"1\" rx=\"2\"/>\n                <rect x=\"70\" y=\"50\" width=\"20\" height=\"15\" fill=\"#ffffff\" stroke=\"#d0d0d0\" stroke-width=\"1\" rx=\"2\"/>\n                <!-- 装饰线条 -->\n                <line x1=\"40\" y1=\"52\" x2=\"60\" y2=\"52\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n                <line x1=\"40\" y1=\"57\" x2=\"55\" y2=\"57\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n                <line x1=\"40\" y1=\"62\" x2=\"58\" y2=\"62\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n                <line x1=\"75\" y1=\"55\" x2=\"85\" y2=\"55\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n                <line x1=\"75\" y1=\"60\" x2=\"82\" y2=\"60\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n              </svg>\n            </div>\n            <p class=\"empty-text\">暂无定时任务</p>\n            <el-button type=\"primary\" @click=\"handleCreateTimedTask\" class=\"create-btn\">\n              去创建\n            </el-button>\n          </div>\n        </div>\n\n        <!-- 任务列表 -->\n        <div v-else class=\"task-list\">\n          <div v-if=\"timedTaskList.length === 0\" class=\"empty-task-list\">\n            <div class=\"empty-icon\">📅</div>\n            <div class=\"empty-text\">暂无定时任务</div>\n            <el-button type=\"primary\" size=\"small\" @click=\"handleAddTimedTask\" class=\"add-task-btn\">\n              添加任务\n            </el-button>\n          </div>\n          <div v-else class=\"task-items\">\n            <div v-for=\"(task, index) in timedTaskList\" :key=\"index\" class=\"task-item\">\n              <div class=\"task-info\">\n                <div class=\"task-header\">\n                  <div class=\"task-name\">{{ task.name }}</div>\n                  <div class=\"task-status\" :class=\"{ 'status-running': task.status === 'running', 'status-pending': task.status === 'pending' }\">\n                    {{ task.status === 'running' ? '运行中' : '待运行' }}\n                  </div>\n                </div>\n                <!-- 任务描述已隐藏 -->\n                <div class=\"task-schedule\">\n                  <i class=\"el-icon-time\"></i>\n                  <span>{{ getTaskScheduleText(task) }}</span>\n                </div>\n              </div>\n              <div class=\"task-actions\">\n                <el-button type=\"text\" size=\"mini\" @click=\"previewTask(index)\" title=\"预览任务详情\">\n                  <i class=\"el-icon-view\"></i>\n                </el-button>\n                <el-button type=\"text\" size=\"mini\" @click=\"toggleTaskStatus(index)\" :title=\"task.status === 'running' ? '暂停任务' : '启动任务'\">\n                  <i :class=\"task.status === 'running' ? 'el-icon-video-pause' : 'el-icon-video-play'\"></i>\n                </el-button>\n                <el-button type=\"text\" size=\"mini\" @click=\"editTask(index)\" title=\"编辑任务\">\n                  <i class=\"el-icon-edit\"></i>\n                </el-button>\n                <el-button type=\"text\" size=\"mini\" @click=\"deleteTask(index)\" title=\"删除任务\">\n                  <i class=\"el-icon-delete\"></i>\n                </el-button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 创建/编辑任务弹窗 -->\n      <el-dialog\n        :title=\"editingTaskIndex === -1 ? '创建定时任务' : '编辑定时任务'\"\n        :visible.sync=\"createTaskDialogVisible\"\n        width=\"500px\"\n        :before-close=\"closeCreateTaskDialog\"\n        :append-to-body=\"true\"\n        class=\"create-task-dialog\"\n      >\n        <div class=\"task-form\">\n          <!-- 任务需求 -->\n          <div class=\"task-requirement-section\">\n            <div class=\"section-label\">\n              任务需求\n              <span class=\"required\">*</span>\n            </div>\n                  <div class=\"form-group\">\n              <div class=\"input-label\">需求名称</div>\n              <el-select\n                v-model=\"taskForm.requirementId\"\n                placeholder=\"请选择需求\"\n                class=\"task-name-input\"\n                style=\"width: 100%\"\n              >\n                <el-option\n                  v-for=\"requirement in requirementList\"\n                  :key=\"requirement.id\"\n                  :label=\"requirement.requirementName\"\n                  :value=\"requirement.id\"\n                />\n              </el-select>\n            </div>\n            <div class=\"form-group\">\n              <div class=\"input-label\">任务名称</div>\n              <el-input\n                v-model=\"taskForm.name\"\n                placeholder=\"请输入任务名称\"\n                class=\"task-name-input\"\n              />\n            </div>\n            <div class=\"form-group\">\n              <div class=\"input-label\">任务描述</div>\n              <el-input\n                v-model=\"taskForm.description\"\n                type=\"textarea\"\n                :rows=\"3\"\n                placeholder=\"请描述具体的任务需求，例如：基于当前分析需求自动生成AI新闻总结\"\n                class=\"task-description-input\"\n              />\n                    <div class=\"form-group\">\n              <div class=\"input-label\">推送地址</div>\n              <el-input\n                v-model=\"taskForm.pushUrl\"\n                placeholder=\"例如：https://www.baidu.com\"\n                class=\"task-name-input\"\n              />\n            </div>\n            </div>\n          </div>\n\n          <!-- 执行时间 -->\n          <div class=\"execute-time-section\">\n            <div class=\"section-label\">执行时间</div>\n            <div class=\"time-selector\">\n              <el-select v-model=\"taskForm.frequency\" placeholder=\"选择频率\" class=\"frequency-select\">\n                <el-option label=\"仅一次\" value=\"once\"></el-option>\n                <el-option label=\"每天\" value=\"daily\"></el-option>\n                <el-option label=\"每周\" value=\"weekly\"></el-option>\n                <el-option label=\"每月\" value=\"monthly\"></el-option>\n              </el-select>\n              <!-- 一次性任务：选择具体日期时间 -->\n              <el-date-picker\n                v-if=\"taskForm.frequency === 'once'\"\n                v-model=\"taskForm.executeDateTime\"\n                type=\"datetime\"\n                placeholder=\"选择执行日期和时间\"\n                format=\"yyyy-MM-dd HH:mm\"\n                value-format=\"yyyy-MM-dd HH:mm\"\n                class=\"datetime-picker\"\n                :picker-options=\"{\n                  disabledDate(time) {\n                    return time.getTime() < Date.now() - 8.64e7\n                  }\n                }\"\n              />\n              <!-- 周期性任务：选择时间 -->\n              <el-time-picker\n                v-else\n                v-model=\"taskForm.executeTime\"\n                format=\"HH:mm\"\n                value-format=\"HH:mm\"\n                placeholder=\"选择时间\"\n                class=\"time-picker\"\n              />\n            </div>\n          </div>\n        </div>\n\n        <!-- 底部按钮 -->\n        <div slot=\"footer\" class=\"dialog-footer\">\n          <el-button @click=\"modifyPlan\" class=\"modify-btn\">修改计划</el-button>\n          <el-button type=\"primary\" @click=\"saveAndRunTask\" class=\"run-btn\">保存并运行</el-button>\n          <el-button type=\"success\" @click=\"saveTaskPlan\" class=\"save-btn\">保存计划</el-button>\n        </div>\n      </el-dialog>\n    </el-drawer>\n\n    <!-- 推送报告弹窗 -->\n    <el-dialog\n      title=\"推送报告\"\n      :visible.sync=\"pushReportDialog.visible\"\n      width=\"500px\"\n      center\n      :close-on-click-modal=\"false\"\n      :close-on-press-escape=\"false\"\n      @close=\"hidePushDialog\"\n    >\n      <el-form :model=\"pushReportDialog\" label-width=\"80px\">\n        <el-form-item label=\"目标URL\" required>\n          <el-input\n            v-model=\"pushReportDialog.url\"\n            placeholder=\"请输入推送目标URL地址\"\n            clearable\n            :disabled=\"pushReportDialog.loading\"\n          >\n            <template slot=\"prepend\">https://</template>\n          </el-input>\n          <div class=\"form-tip\">\n            <i class=\"el-icon-info\"></i>\n            将推送包含报告页面链接的消息，接收方可点击链接查看完整报告<br>\n            <strong>支持所有地址格式：</strong><br>\n            • 钉钉机器人：https://oapi.dingtalk.com/robot/send?access_token=xxx<br>\n            • 企业微信：https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=xxx<br>\n            • 飞书机器人：https://open.feishu.cn/open-apis/bot/v2/hook/xxx<br>\n            • 普通HTTP接口：https://your-domain.com/api/webhook<br>\n            • 本地地址：localhost:3000/webhook 或 127.0.0.1:8080/api<br>\n            • 测试地址：httpbin.org/post\n          </div>\n        </el-form-item>\n      </el-form>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"hidePushDialog\" :disabled=\"pushReportDialog.loading\">\n          取消\n        </el-button>\n        <el-button\n          type=\"default\"\n          @click=\"savePushPlan\"\n          :disabled=\"pushReportDialog.loading\"\n        >\n          保存计划\n        </el-button>\n        <el-button\n          type=\"primary\"\n          @click=\"directPushReport\"\n          :loading=\"pushReportDialog.loading\"\n          :disabled=\"!pushReportDialog.url.trim()\"\n        >\n          直接推送\n        </el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 任务预览弹窗 -->\n    <el-dialog\n      title=\"任务详情预览\"\n      :visible.sync=\"taskPreviewDialog.visible\"\n      width=\"600px\"\n      center\n      :close-on-click-modal=\"false\"\n      :close-on-press-escape=\"false\"\n      @close=\"hideTaskPreviewDialog\"\n      class=\"task-preview-dialog\"\n    >\n      <div v-if=\"taskPreviewDialog.taskData\" class=\"task-preview-content\">\n        <!-- 基本信息 -->\n        <div class=\"preview-section\">\n          <h3 class=\"section-title\">\n            <i class=\"el-icon-info\"></i>\n            基本信息\n          </h3>\n          <div class=\"info-grid\">\n            <div class=\"info-item\">\n              <label>任务名称：</label>\n              <span>{{ taskPreviewDialog.taskData.name }}</span>\n            </div>\n            <div class=\"info-item\">\n              <label>任务状态：</label>\n              <el-tag :type=\"taskPreviewDialog.taskData.status === 'running' ? 'success' : 'info'\">\n                {{ taskPreviewDialog.taskData.status === 'running' ? '运行中' : '待运行' }}\n              </el-tag>\n            </div>\n            <div class=\"info-item\">\n              <label>需求ID：</label>\n              <span>{{ taskPreviewDialog.taskData.requirementId || '未关联' }}</span>\n            </div>\n          </div>\n        </div>\n\n        <!-- 任务描述 -->\n        <div class=\"preview-section\" v-if=\"taskPreviewDialog.taskData.description\">\n          <h3 class=\"section-title\">\n            <i class=\"el-icon-document\"></i>\n            任务描述\n          </h3>\n          <div class=\"description-content\">\n            {{ taskPreviewDialog.taskData.description }}\n          </div>\n        </div>\n\n        <!-- 执行计划 -->\n        <div class=\"preview-section\">\n          <h3 class=\"section-title\">\n            <i class=\"el-icon-time\"></i>\n            执行计划\n          </h3>\n          <div class=\"info-grid\">\n            <div class=\"info-item\">\n              <label>执行频率：</label>\n              <span>{{ getFrequencyText(taskPreviewDialog.taskData.frequency) }}</span>\n            </div>\n            <div class=\"info-item\">\n              <label>执行时间：</label>\n              <span>{{ taskPreviewDialog.taskData.executeTime }}</span>\n            </div>\n          </div>\n        </div>\n\n        <!-- 推送配置 -->\n        <div class=\"preview-section\" v-if=\"taskPreviewDialog.taskData.pushUrl\">\n          <h3 class=\"section-title\">\n            <i class=\"el-icon-s-promotion\"></i>\n            推送配置\n          </h3>\n          <div class=\"push-config\">\n            <div class=\"info-item\">\n              <label>推送地址：</label>\n              <div class=\"url-display\">\n                <span class=\"url-text\">{{ getMaskedUrl(taskPreviewDialog.taskData.pushUrl) }}</span>\n                <el-button\n                  type=\"text\"\n                  size=\"mini\"\n                  @click=\"copyToClipboard(taskPreviewDialog.taskData.pushUrl)\"\n                  title=\"复制完整地址\"\n                >\n                  <i class=\"el-icon-copy-document\"></i>\n                </el-button>\n              </div>\n            </div>\n            <div class=\"info-item\">\n              <label>推送类型：</label>\n              <el-tag size=\"small\">{{ getPushTypeText(taskPreviewDialog.taskData.pushUrl) }}</el-tag>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"hideTaskPreviewDialog\">关闭</el-button>\n        <el-button\n          type=\"primary\"\n          @click=\"editTaskFromPreview\"\n          v-if=\"taskPreviewDialog.taskData\"\n        >\n          编辑任务\n        </el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 模板选择弹窗 -->\n    <el-dialog\n      title=\"选择分析模板\"\n      :visible.sync=\"templateDialog.visible\"\n      width=\"700px\"\n      center\n      :close-on-click-modal=\"false\"\n      :close-on-press-escape=\"false\"\n      @close=\"closeTemplateDialog\"\n      class=\"template-dialog\"\n    >\n      <div class=\"template-content\">\n        <div class=\"template-list\">\n          <div\n            v-for=\"template in templateList\"\n            :key=\"template.id\"\n            class=\"template-item\"\n            :class=\"{ 'selected': templateDialog.selectedTemplate === template.id }\"\n            @click=\"templateDialog.selectedTemplate = template.id\"\n          >\n            <div class=\"template-header\">\n              <div class=\"template-title\">\n                <i class=\"el-icon-document\"></i>\n                {{ template.name }}\n              </div>\n              <div class=\"template-category\">{{ template.category }}</div>\n            </div>\n            <div class=\"template-details\">\n              <div class=\"template-field\">\n                <label>实体关键词：</label>\n                <span>{{ template.entityKeyword }}</span>\n              </div>\n              <div class=\"template-field\">\n                <label>具体需求：</label>\n                <p>{{ template.requirement }}</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"closeTemplateDialog\">取消</el-button>\n        <el-button\n          type=\"primary\"\n          @click=\"applyTemplate(templateList.find(t => t.id === templateDialog.selectedTemplate))\"\n          :disabled=\"!templateDialog.selectedTemplate\"\n        >\n          应用模板\n        </el-button>\n      </div>\n    </el-dialog>\n\n  </div>\n</template>\n\n<script>\nimport {\n  getRequirementList,\n  createRequirement,\n  updateRequirement,\n  deleteRequirement,\n  getKeywordCategories,\n  getDataSourceList,\n  createDataSource,\n  deleteDataSource as deleteDataSourceAPI,\n  getTaskStatistics,\n  getDataSourceStatistics,\n  getTimedTaskList,\n  getTimedTaskDetail,\n  createTimedTask,\n  updateTimedTask,\n  updateTaskStatus,\n  deleteTimedTask,\n  getTasksByRequirement,\n  performOnlineSearch,\n  startAnalysis,\n  pushReport,\n  getPublicReportData,\n  checkTaskExists,\n  generateRelatedKeywords,\n  createAnalysisProgressTask,\n  addProgressLog,\n  getAnalysisProgress,\n  completeAnalysisTask,\n  cancelAnalysisTask,\n  generateAndUploadReport\n} from '@/api/opinion-analysis'\n\nimport {\n  getTemplatesForSelection,\n  updateTemplateUsage\n} from '@/api/opinion-template'\n\nimport { getDashboardStatistics, getCurrentMonthAnalysisCount } from '@/api/dashboard'\n\nexport default {\n  name: 'OpinionAnalysis',\n  data() {\n    return {\n      currentStep: 1, // 当前步骤\n      requirementName: '', // 需求名称\n      entityKeyword: '', // 实体关键词\n      specificRequirement: '', // 具体需求\n      selectedKeywords: [], // 已选择的关键词\n      generatedKeywords: [], // 生成的所有关键词\n      maxKeywords: 5, // 最大选择数量\n      enableOnlineSearch: true, // 是否启用联网搜索\n      enableCustomDataSource: false, // 是否启用自定义数据源搜索（默认关闭，可选）\n      customDataSources: [], // 自定义数据源URL列表\n      showAddSourceInput: false, // 显示新增数据源表单\n      newSourceUrl: '', // 新增数据源URL\n      // 数据源列表相关状态\n      dataSourceList: [], // 数据源列表数据\n      selectedDataSources: [], // 选中的数据源\n      dataSourceListState: {\n        loading: false,\n        current_page: 1,\n        page_size: 10\n      },\n      showValidation: false, // 是否显示验证错误样式\n      timedTaskDialogVisible: false, // 定时任务抽屉显示状态\n      timedTaskList: [], // 定时任务列表\n      createTaskDialogVisible: false, // 创建任务弹窗显示状态\n      editingTaskIndex: -1, // 当前编辑的任务索引，-1表示新建任务\n      taskForm: {\n        requirementId: '', // 需求ID\n        name: '', // 任务名称\n        description: '',\n        executeTime: '16:00',\n        executeDateTime: '', // 一次性任务的执行日期时间\n        frequency: 'daily',\n        pushUrl: '' // 推送地址\n      },\n      requirementList: [], // 需求列表\n      keywordCategories: [], // 关键词分类列表\n      currentRequirementId: null, // 当前需求的ID\n      requirementSaved: false, // 需求是否已保存到数据库\n      requirementModified: false, // 需求是否被修改（需要更新）\n      reportData: { // 报告数据\n        totalArticles: 0,\n        totalKeywords: 0,\n        dataSources: 0,\n        sentiment: {\n          positive: 0,\n          neutral: 0,\n          negative: 0\n        },\n        onlineSearchCount: 0,\n        customSourceCounts: {}\n      },\n      analysisResults: null, // 完整的分析结果数据\n      // 文章列表相关状态\n      articleListState: {\n        currentPage: 1,\n        pageSize: 10,\n        searchKeyword: '',\n        selectedSource: '',\n        selectedSentiment: '',\n        expandedArticles: new Set() // 存储展开的文章ID\n      },\n      // 推送报告相关状态\n      pushReportDialog: {\n        visible: false,\n        url: '',\n        loading: false\n      },\n      // 任务预览弹窗状态\n      taskPreviewDialog: {\n        visible: false,\n        taskData: null,\n        loading: false\n      },\n      // 模板弹窗状态\n      templateDialog: {\n        visible: false,\n        selectedTemplate: null\n      },\n      // 模板数据（从数据库获取）\n      templateList: [],\n      // 分析进度相关状态\n      analysisStatus: 'idle', // 分析状态：idle-空闲，running-运行中，completed-已完成，failed-失败\n      analysisProgress: 0, // 分析进度百分比\n      analysisLogs: [], // 分析日志列表\n      autoScroll: true, // 是否自动滚动日志\n      currentTaskId: null, // 当前分析任务ID\n      websocket: null, // WebSocket连接\n      heartbeatTimer: null, // 心跳定时器\n      reconnectAttempts: 0, // 重连尝试次数\n      maxReconnectAttempts: 5, // 最大重连次数\n      reconnectTimer: null, // 重连定时器\n      statusPollingTimer: null, // 状态轮询定时器\n      // HTTP轮询相关状态\n      httpPollingTimers: new Map(), // 存储多个轮询定时器\n      pollingConfigs: new Map(), // 存储轮询配置\n      // 报告OSS相关状态\n      reportOssUrl: null, // 报告的OSS访问URL\n      reportPageId: null // 报告页面ID\n    }\n  },\n  computed: {\n    // 检查是否可以进入下一步\n    canGoToNextStep() {\n      // 检查需求名称是否填写\n      if (!this.requirementName.trim()) {\n        return false\n      }\n      \n      // 检查实体关键词是否填写\n      if (!this.entityKeyword.trim()) {\n        return false\n      }\n\n      // 检查具体需求是否填写\n      if (!this.specificRequirement.trim()) {\n        return false\n      }\n\n      // 检查是否至少选择了一个关键词\n      if (this.selectedKeywords.length === 0) {\n        return false\n      }\n\n      return true\n    },\n\n    // 获取所有文章数据\n    allArticles() {\n      if (!this.analysisResults || !this.analysisResults.analysis_results) {\n        return []\n      }\n\n      const articles = []\n      const results = this.analysisResults.analysis_results\n\n      // 添加联网搜索结果\n      if (results.online_search && results.online_search.data && results.online_search.data.articles) {\n        articles.push(...results.online_search.data.articles.map(article => ({\n          ...article,\n          sourceType: 'online_search'\n        })))\n      }\n\n      // 添加自定义数据源结果\n      if (results.custom_data_source && results.custom_data_source.data && results.custom_data_source.data.articles) {\n        articles.push(...results.custom_data_source.data.articles.map(article => ({\n          ...article,\n          sourceType: 'custom_data_source'\n        })))\n      }\n\n      return articles\n    },\n\n    // 获取所有唯一的数据源\n    uniqueSources() {\n      const sources = new Set()\n      this.allArticles.forEach(article => {\n        if (article.source) {\n          sources.add(article.source)\n        }\n      })\n      return Array.from(sources)\n    },\n\n    // 筛选后的文章列表\n    filteredArticles() {\n      let filtered = this.allArticles\n\n      // 按搜索关键词筛选\n      if (this.articleListState.searchKeyword) {\n        const keyword = this.articleListState.searchKeyword.toLowerCase()\n        filtered = filtered.filter(article =>\n          (article.title && article.title.toLowerCase().includes(keyword)) ||\n          (article.content && article.content.toLowerCase().includes(keyword))\n        )\n      }\n\n      // 按来源筛选\n      if (this.articleListState.selectedSource) {\n        filtered = filtered.filter(article => article.source === this.articleListState.selectedSource)\n      }\n\n      // 按情感筛选\n      if (this.articleListState.selectedSentiment) {\n        filtered = filtered.filter(article => article.sentiment === this.articleListState.selectedSentiment)\n      }\n\n      return filtered\n    },\n\n    // 分页后的文章列表\n    paginatedArticles() {\n      const start = (this.articleListState.currentPage - 1) * this.articleListState.pageSize\n      const end = start + this.articleListState.pageSize\n      return this.filteredArticles.slice(start, end)\n    },\n\n    // 分页后的数据源列表\n    paginatedDataSources() {\n      const start = (this.dataSourceListState.current_page - 1) * this.dataSourceListState.page_size\n      const end = start + this.dataSourceListState.page_size\n      return this.dataSourceList.slice(start, end)\n    },\n\n    // 将关键词按分类分组\n    groupedKeywords() {\n      if (this.generatedKeywords.length === 0) {\n        return {}\n      }\n\n      // 使用从API获取的分类，如果没有则使用默认分类\n      const categories = this.keywordCategories.length > 0\n        ? this.keywordCategories.map(cat => ({ name: cat.category_name, keywords: [] }))\n        : [\n            { name: '售后服务问题', keywords: [] },\n            { name: '产品质量问题', keywords: [] },\n            { name: '投诉处理结果', keywords: [] },\n            { name: '消费者不满', keywords: [] },\n            { name: '虚假宣传', keywords: [] }\n          ]\n\n      this.generatedKeywords.forEach(keyword => {\n        let assigned = false\n\n        categories.forEach(cat => {\n          if (cat.name === '售后服务问题' && (keyword.includes('售后') || keyword.includes('服务') || keyword.includes('客服'))) {\n            cat.keywords.push(keyword)\n            assigned = true\n          } else if (cat.name === '产品质量问题' && (keyword.includes('质量') || keyword.includes('爆炸') || keyword.includes('故障'))) {\n            cat.keywords.push(keyword)\n            assigned = true\n          } else if (cat.name === '投诉处理结果' && (keyword.includes('投诉') || keyword.includes('处理') || keyword.includes('对解'))) {\n            cat.keywords.push(keyword)\n            assigned = true\n          } else if (cat.name === '消费者不满' && (keyword.includes('不满') || keyword.includes('消费者'))) {\n            cat.keywords.push(keyword)\n            assigned = true\n          } else if (cat.name === '虚假宣传' && (keyword.includes('宣传') || keyword.includes('充好'))) {\n            cat.keywords.push(keyword)\n            assigned = true\n          }\n        })\n\n        if (!assigned) {\n          // 如果没有匹配的分类，添加到第一个有关键词的分类或创建新分类\n          if (categories[0].keywords.length === 0) {\n            categories[0].keywords.push(keyword)\n          } else {\n            categories.find(cat => cat.keywords.length > 0).keywords.push(keyword)\n          }\n        }\n      })\n\n      // 只返回有关键词的分类\n      const result = {}\n      categories.forEach(cat => {\n        if (cat.keywords.length > 0) {\n          result[cat.name] = cat.keywords\n        }\n      })\n\n      return result\n    }\n  },\n  watch: {\n    // 监听需求信息变化，重置保存状态\n    requirementName() {\n      this.resetRequirementSaveStatus()\n    },\n    entityKeyword() {\n      this.resetRequirementSaveStatus()\n    },\n    specificRequirement() {\n      this.resetRequirementSaveStatus()\n    },\n    // 监听筛选条件变化，重置分页到第一页\n    'articleListState.searchKeyword'() {\n      this.articleListState.currentPage = 1\n    },\n    'articleListState.selectedSource'() {\n      this.articleListState.currentPage = 1\n    },\n    'articleListState.selectedSentiment'() {\n      this.articleListState.currentPage = 1\n    }\n  },\n  mounted() {\n    // 页面初始化逻辑\n    console.log('🚀 舆情分析页面已加载')\n    this.debugLog('页面初始化', '舆情分析页面开始加载')\n\n    // 显示调试信息\n    console.log('🔧 调试模式已启用，可使用以下方法:')\n    console.log('  - this.clearDebugLogs() : 清除调试日志')\n    console.log('  - this.exportDebugLogs() : 导出调试日志')\n    console.log('  - localStorage.getItem(\"opinion_analysis_debug_logs\") : 查看调试日志')\n\n    // 加载关键词分类\n    this.loadKeywordCategories()\n\n    // 注释掉页面加载时的定时任务列表加载，改为点击定时推送按钮时才加载\n    // this.loadTimedTaskList()\n\n    // 注释掉页面加载时的数据源列表加载，改为进入第二步或启用自定义数据源时才加载\n    // this.loadDataSourceList()\n\n    // 注释掉页面加载时的模板列表加载，改为点击模板按钮时才加载\n    // this.loadTemplateList()\n\n    // 检查URL参数，如果有报告参数则自动加载\n    this.parseUrlParams()\n\n    this.debugLog('页面初始化', '舆情分析页面加载完成')\n  },\n\n  beforeDestroy() {\n    // 页面销毁前清理所有HTTP轮询\n    this.stopAllHttpPolling()\n    console.log('🧹 页面销毁，已清理所有HTTP轮询')\n  },\n  methods: {\n    // ==================== HTTP轮询相关方法 ====================\n\n    /**\n     * 启动HTTP轮询\n     * @param {string} pollingId - 轮询标识符，用于管理多个轮询任务\n     * @param {Function} requestFunction - 执行HTTP请求的函数，应返回Promise\n     * @param {Object} options - 轮询配置选项\n     * @param {number} options.interval - 轮询间隔（毫秒），默认3000ms\n     * @param {number} options.maxAttempts - 最大轮询次数，默认无限制\n     * @param {Function} options.onSuccess - 成功回调函数\n     * @param {Function} options.onError - 错误回调函数\n     * @param {Function} options.shouldStop - 停止条件判断函数，返回true时停止轮询\n     * @param {boolean} options.immediate - 是否立即执行第一次请求，默认true\n     */\n    startHttpPolling(pollingId, requestFunction, options = {}) {\n      const config = {\n        interval: 3000, // 默认3秒轮询一次\n        maxAttempts: 0, // 0表示无限制\n        onSuccess: null,\n        onError: null,\n        shouldStop: null,\n        immediate: true,\n        ...options\n      }\n\n      // 如果已存在相同ID的轮询，先停止它\n      this.stopHttpPolling(pollingId)\n\n      // 保存轮询配置\n      this.pollingConfigs.set(pollingId, {\n        ...config,\n        requestFunction,\n        attempts: 0,\n        isRunning: true\n      })\n\n      console.log(`🔄 启动HTTP轮询: ${pollingId}`, config)\n\n      // 轮询执行函数\n      const executePolling = async () => {\n        const pollingConfig = this.pollingConfigs.get(pollingId)\n        if (!pollingConfig || !pollingConfig.isRunning) {\n          return\n        }\n\n        try {\n          pollingConfig.attempts++\n          console.log(`📡 执行轮询请求: ${pollingId} (第${pollingConfig.attempts}次)`)\n\n          // 执行HTTP请求\n          const result = await requestFunction()\n\n          // 执行成功回调\n          if (config.onSuccess) {\n            config.onSuccess(result, pollingConfig.attempts)\n          }\n\n          // 检查是否应该停止轮询\n          if (config.shouldStop && config.shouldStop(result)) {\n            console.log(`✅ 轮询停止条件满足: ${pollingId}`)\n            this.stopHttpPolling(pollingId)\n            return\n          }\n\n          // 检查是否达到最大尝试次数\n          if (config.maxAttempts > 0 && pollingConfig.attempts >= config.maxAttempts) {\n            console.log(`⏰ 轮询达到最大次数: ${pollingId}`)\n            this.stopHttpPolling(pollingId)\n            return\n          }\n\n          // 设置下次轮询\n          if (pollingConfig.isRunning) {\n            const timer = setTimeout(executePolling, config.interval)\n            this.httpPollingTimers.set(pollingId, timer)\n          }\n\n        } catch (error) {\n          console.error(`❌ 轮询请求失败: ${pollingId}`, error)\n\n          // 执行错误回调\n          if (config.onError) {\n            const shouldContinue = config.onError(error, pollingConfig.attempts)\n            if (shouldContinue === false) {\n              console.log(`🛑 错误回调要求停止轮询: ${pollingId}`)\n              this.stopHttpPolling(pollingId)\n              return\n            }\n          }\n\n          // 继续下次轮询（除非明确要求停止）\n          if (pollingConfig.isRunning) {\n            const timer = setTimeout(executePolling, config.interval)\n            this.httpPollingTimers.set(pollingId, timer)\n          }\n        }\n      }\n\n      // 立即执行第一次请求或延迟执行\n      if (config.immediate) {\n        executePolling()\n      } else {\n        const timer = setTimeout(executePolling, config.interval)\n        this.httpPollingTimers.set(pollingId, timer)\n      }\n    },\n\n    /**\n     * 停止指定的HTTP轮询\n     * @param {string} pollingId - 轮询标识符\n     */\n    stopHttpPolling(pollingId) {\n      // 清除定时器\n      if (this.httpPollingTimers.has(pollingId)) {\n        clearTimeout(this.httpPollingTimers.get(pollingId))\n        this.httpPollingTimers.delete(pollingId)\n      }\n\n      // 标记轮询为停止状态\n      if (this.pollingConfigs.has(pollingId)) {\n        const config = this.pollingConfigs.get(pollingId)\n        config.isRunning = false\n        console.log(`🛑 停止HTTP轮询: ${pollingId} (共执行${config.attempts}次)`)\n      }\n    },\n\n    /**\n     * 停止所有HTTP轮询\n     */\n    stopAllHttpPolling() {\n      console.log('🛑 停止所有HTTP轮询')\n      for (const pollingId of this.httpPollingTimers.keys()) {\n        this.stopHttpPolling(pollingId)\n      }\n      this.httpPollingTimers.clear()\n      this.pollingConfigs.clear()\n    },\n\n    /**\n     * 获取轮询状态信息\n     * @param {string} pollingId - 轮询标识符\n     * @returns {Object|null} 轮询状态信息\n     */\n    getPollingStatus(pollingId) {\n      if (!this.pollingConfigs.has(pollingId)) {\n        return null\n      }\n\n      const config = this.pollingConfigs.get(pollingId)\n      return {\n        pollingId,\n        isRunning: config.isRunning,\n        attempts: config.attempts,\n        interval: config.interval,\n        maxAttempts: config.maxAttempts\n      }\n    },\n\n    /**\n     * 获取所有轮询状态\n     * @returns {Array} 所有轮询的状态信息\n     */\n    getAllPollingStatus() {\n      const statusList = []\n      for (const pollingId of this.pollingConfigs.keys()) {\n        statusList.push(this.getPollingStatus(pollingId))\n      }\n      return statusList\n    },\n\n    // ==================== 轮询应用示例方法 ====================\n\n    /**\n     * 轮询分析进度（主要通信方式，替代WebSocket）\n     */\n    startAnalysisProgressPolling() {\n      if (!this.currentTaskId) {\n        console.warn('⚠️ 没有当前任务ID，无法启动进度轮询')\n        return\n      }\n\n      console.log('🔄 启动分析进度HTTP轮询，任务ID:', this.currentTaskId)\n\n      this.startHttpPolling('analysisProgress', async () => {\n        // 调用获取分析进度的API\n        const response = await getAnalysisProgress(this.currentTaskId)\n        return response.data\n      }, {\n        interval: 1500, // 每1.5秒轮询一次，提高实时性\n        immediate: true, // 立即执行第一次请求\n        onSuccess: (data, attempts) => {\n          console.log(`📊 分析进度更新 (第${attempts}次):`, data)\n\n          // 进度条增长机制：每次轮询时自动增加1%（从当前进度开始）\n          if (this.analysisProgress < 95) { // 限制在95%以下，为最终完成留出空间\n            this.analysisProgress = Math.min(this.analysisProgress + 1, 95)\n            console.log(`🔄 进度条自动递增至: ${this.analysisProgress}%`)\n          }\n\n          // 更新进度数据（如果API返回了具体进度，优先使用API数据）\n          if (data.progress !== undefined && data.progress > this.analysisProgress) {\n            this.analysisProgress = Math.min(data.progress, 95) // 同样限制在95%\n            this.addLog('info', `分析进度: ${data.progress}%`)\n          }\n\n          if (data.status && data.status !== this.analysisStatus) {\n            this.analysisStatus = data.status\n            this.addLog('info', `状态变更: ${data.status}`)\n\n            // 状态变更时的特殊处理\n            if (data.status === 'completed') {\n              // 轮询完成处理：立即将进度条跳转到100%\n              this.analysisProgress = 100\n              this.addLog('success', '✅ 分析任务已完成！')\n              this.$message.success('分析完成！')\n              // 立即停止轮询，避免继续无意义的请求\n              this.stopHttpPolling('analysisProgress')\n              this.addLog('info', '分析完成，已停止进度轮询')\n            } else if (data.status === 'failed') {\n              this.addLog('error', '❌ 分析任务失败')\n              this.$message.error('分析失败，请重试')\n              // 失败时也停止轮询\n              this.stopHttpPolling('analysisProgress')\n              this.addLog('info', '分析失败，已停止进度轮询')\n            }\n          }\n\n          // 更新日志\n          if (data.logs && Array.isArray(data.logs)) {\n            // 只添加新的日志条目，并确保每个日志都有正确的level字段\n            const newLogs = data.logs\n              .filter(log => {\n                // 过滤空日志：检查message是否为空或只包含空白字符\n                if (!log.message || log.message.trim() === '') {\n                  return false\n                }\n                // 过滤重复日志\n                return !this.analysisLogs.some(existingLog =>\n                  existingLog.timestamp === log.timestamp &&\n                  existingLog.message === log.message\n                )\n              })\n              .map(log => ({\n                ...log,\n                level: log.level || 'info', // 确保level字段存在\n                message: log.message || '', // 确保message字段存在\n                timestamp: log.timestamp || new Date().toISOString() // 确保timestamp字段存在\n              }))\n            this.analysisLogs.push(...newLogs)\n\n            // 自动滚动到最新日志\n            if (this.autoScroll && newLogs.length > 0) {\n              this.$nextTick(() => {\n                this.scrollToBottom()\n              })\n            }\n          }\n\n          // 更新分析结果数据\n          if (data.analysis_results) {\n            this.analysisResults = data\n            this.updateReportData(data.analysis_results)\n          }\n        },\n        onError: (error, attempts) => {\n          console.error(`❌ 获取分析进度失败 (第${attempts}次):`, error)\n\n          // 添加错误日志\n          this.addLog('error', `获取进度失败: ${error.message || '网络错误'}`)\n\n          // 根据错误类型和尝试次数决定是否继续\n          if (error.response?.status === 404) {\n            this.addLog('error', '任务不存在，停止轮询')\n            this.$message.error('分析任务不存在')\n            return false // 停止轮询\n          }\n\n          if (error.response?.status === 401) {\n            this.addLog('error', '认证失败，请重新登录')\n            this.$message.error('认证失败，请重新登录')\n            return false // 停止轮询\n          }\n\n          // 如果连续失败8次，停止轮询\n          if (attempts >= 8) {\n            this.addLog('error', '连续获取进度失败，停止轮询')\n            this.$message.error('获取分析进度失败，请刷新页面重试')\n            this.analysisStatus = 'failed'\n            return false // 停止轮询\n          }\n\n          return true // 继续轮询\n        },\n        shouldStop: (data) => {\n          // 当分析完成或失败时停止轮询\n          const shouldStop = data.status === 'completed' || data.status === 'failed'\n          if (shouldStop) {\n            console.log('🛑 分析进度轮询停止，最终状态:', data.status)\n\n            // 轮询完成处理：无论当前进度是多少，立即将进度条跳转到100%\n            if (data.status === 'completed') {\n              this.analysisProgress = 100\n              console.log('✅ 轮询结束，进度条已跳转到100%')\n            }\n          }\n          return shouldStop\n        }\n      })\n    },\n\n    /**\n     * 示例：轮询任务状态\n     */\n    startTaskStatusPolling(taskId) {\n      this.startHttpPolling(`taskStatus_${taskId}`, async () => {\n        // 这里调用获取任务状态的API\n        const response = await getTimedTaskDetail(taskId)\n        return response.data\n      }, {\n        interval: 5000, // 每5秒轮询一次\n        maxAttempts: 60, // 最多轮询60次（5分钟）\n        onSuccess: (data) => {\n          console.log('任务状态更新:', data)\n          // 更新任务状态到界面\n        },\n        shouldStop: (data) => {\n          // 当任务完成时停止轮询\n          return data.status === 'completed' || data.status === 'failed'\n        }\n      })\n    },\n\n    /**\n     * 示例：轮询数据源状态\n     */\n    startDataSourcePolling() {\n      this.startHttpPolling('dataSourceStatus', async () => {\n        const response = await getDataSourceList()\n        return response.data\n      }, {\n        interval: 10000, // 每10秒轮询一次\n        onSuccess: (data) => {\n          // 更新数据源列表\n          if (data && Array.isArray(data.list)) {\n            this.dataSourceList = data.list\n          }\n        },\n        onError: (error, attempts) => {\n          console.error('获取数据源列表失败:', error)\n          if (attempts >= 3) {\n            this.$message.warning('数据源状态更新失败，请手动刷新')\n            return false // 停止轮询\n          }\n          return true // 继续轮询\n        }\n      })\n    },\n\n    /**\n     * 示例：轮询报告生成状态\n     */\n    startReportGenerationPolling(reportId) {\n      this.startHttpPolling(`reportGeneration_${reportId}`, async () => {\n        // 这里应该调用获取报告生成状态的API\n        const response = await getPublicReportData(reportId)\n        return response.data\n      }, {\n        interval: 3000, // 每3秒轮询一次\n        maxAttempts: 100, // 最多轮询100次（5分钟）\n        onSuccess: (data) => {\n          console.log('报告生成状态更新:', data)\n          if (data.status === 'completed') {\n            this.$message.success('报告生成完成！')\n            // 更新报告数据\n            this.reportData = data\n          }\n        },\n        onError: (error, attempts) => {\n          console.error('获取报告状态失败:', error)\n          if (attempts >= 5) {\n            this.$message.error('报告生成状态获取失败')\n            return false\n          }\n          return true\n        },\n        shouldStop: (data) => {\n          return data.status === 'completed' || data.status === 'failed'\n        }\n      })\n    },\n\n    /**\n     * 通用轮询方法：轮询API直到满足条件\n     * @param {string} apiFunction - API函数名\n     * @param {Array} apiParams - API参数数组\n     * @param {Function} successCondition - 成功条件判断函数\n     * @param {Object} options - 轮询选项\n     */\n    pollUntilCondition(apiFunction, apiParams = [], successCondition, options = {}) {\n      const pollingId = `generic_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`\n\n      this.startHttpPolling(pollingId, async () => {\n        // 动态调用API函数\n        const apiFunc = this[apiFunction] || window[apiFunction]\n        if (typeof apiFunc === 'function') {\n          return await apiFunc(...apiParams)\n        } else {\n          throw new Error(`API函数 ${apiFunction} 不存在`)\n        }\n      }, {\n        interval: options.interval || 2000,\n        maxAttempts: options.maxAttempts || 50,\n        onSuccess: (data) => {\n          if (options.onProgress) {\n            options.onProgress(data)\n          }\n        },\n        onError: (error, attempts) => {\n          console.error(`轮询API ${apiFunction} 失败:`, error)\n          if (options.onError) {\n            return options.onError(error, attempts)\n          }\n          return attempts < 5 // 默认失败5次后停止\n        },\n        shouldStop: successCondition\n      })\n\n      return pollingId // 返回轮询ID，便于外部控制\n    },\n\n    // 处理测试步骤切换\n    handleStepSwitch(step) {\n      const stepNumber = parseInt(step)\n      console.log(`🔄 测试切换到第${stepNumber}步`)\n\n      // 直接切换步骤，不进行验证（测试用）\n      this.currentStep = stepNumber\n\n      // 如果切换到第二步（数据概览），加载数据源列表\n      if (stepNumber === 2) {\n        this.loadDataSourceList()\n      }\n\n      // 显示切换成功消息\n      this.$message({\n        message: `已切换到第${stepNumber}步：${this.getStepName(stepNumber)}`,\n        type: 'success',\n        duration: 2000\n      })\n\n      // 记录调试日志\n      this.debugLog('步骤切换', `测试切换到第${stepNumber}步`, {\n        fromStep: this.currentStep,\n        toStep: stepNumber\n      })\n    },\n\n    // 获取步骤名称\n    getStepName(step) {\n      const stepNames = {\n        1: '舆情分析来源',\n        2: '数据概览',\n        3: '分析进度',\n        4: '报告预览'\n      }\n      return stepNames[step] || '未知步骤'\n    },\n\n    // 跳转到分析记录页面\n    goToAnalyzeRecord() {\n      this.$router.push('/analyze-record')\n    },\n\n    // 处理模板按钮点击\n    handleTemplateClick() {\n      this.templateDialog.visible = true\n      this.templateDialog.selectedTemplate = null\n      // 每次打开模板弹窗时重新加载模板数据\n      this.loadTemplateList()\n      console.log('模板弹窗已打开')\n    },\n\n    // 加载模板列表\n    async loadTemplateList() {\n      try {\n        const response = await getTemplatesForSelection()\n        if (response.code === 200) {\n          // 转换数据格式以适配现有的模板显示逻辑\n          // API返回的是下划线格式，需要正确映射字段名\n          this.templateList = response.data.map(template => ({\n            id: template.id,\n            name: template.template_name,\n            entityKeyword: template.entity_keyword,\n            requirement: template.specific_requirement,\n            category: template.template_category,\n            priority: template.priority,\n            usageCount: template.usage_count\n          }))\n          console.log('✅ 模板列表加载成功:', this.templateList.length, '个模板')\n          console.log('📋 模板数据:', this.templateList)\n        } else {\n          console.error('❌ 加载模板列表失败:', response.msg)\n          this.$message.error('加载模板列表失败: ' + response.msg)\n        }\n      } catch (error) {\n        console.error('❌ 加载模板列表异常:', error)\n        this.$message.error('加载模板列表失败，请稍后重试')\n      }\n    },\n\n    // 关闭模板弹窗\n    closeTemplateDialog() {\n      this.templateDialog.visible = false\n      this.templateDialog.selectedTemplate = null\n    },\n\n    // 应用模板\n    async applyTemplate(template) {\n      if (!template) {\n        this.$message.error('请选择一个模板')\n        return\n      }\n\n      try {\n        // 应用模板数据到表单\n        this.requirementName = template.name\n        this.entityKeyword = template.entityKeyword\n        this.specificRequirement = template.requirement\n\n        // 更新模板使用次数\n        await updateTemplateUsage(template.id, 1)\n\n        this.closeTemplateDialog()\n        this.$message.success('模板已应用')\n\n        console.log('✅ 模板应用成功:', template.name)\n      } catch (error) {\n        console.error('❌ 应用模板失败:', error)\n        // 即使更新使用次数失败，也要应用模板\n        this.closeTemplateDialog()\n        this.$message.success('模板已应用')\n      }\n    },\n\n    // 调试日志辅助方法\n    debugLog(tag, message, data = null) {\n      const timestamp = new Date().toISOString()\n      const logMessage = `[${timestamp}] ${tag}: ${message}`\n      console.log(logMessage)\n      if (data) {\n        console.log(`[${timestamp}] ${tag} - 数据:`, data)\n      }\n\n      // 可选：将日志保存到localStorage用于调试\n      try {\n        const logs = JSON.parse(localStorage.getItem('opinion_analysis_debug_logs') || '[]')\n        logs.push({\n          timestamp,\n          tag,\n          message,\n          data: data ? JSON.stringify(data, null, 2) : null\n        })\n        // 只保留最近100条日志\n        if (logs.length > 100) {\n          logs.splice(0, logs.length - 100)\n        }\n        localStorage.setItem('opinion_analysis_debug_logs', JSON.stringify(logs))\n      } catch (e) {\n        console.warn('保存调试日志失败:', e)\n      }\n    },\n\n    // 生成随机字符后缀，确保需求名称唯一性\n    generateRandomSuffix() {\n      // 生成时间戳（格式：YYYYMMDDHHMMSS）\n      const now = new Date()\n      const timestamp = now.getFullYear().toString() +\n        (now.getMonth() + 1).toString().padStart(2, '0') +\n        now.getDate().toString().padStart(2, '0') +\n        now.getHours().toString().padStart(2, '0') +\n        now.getMinutes().toString().padStart(2, '0') +\n        now.getSeconds().toString().padStart(2, '0')\n\n      // 生成随机字符串（6位字母数字组合）\n      const chars = 'abcdefghijklmnopqrstuvwxyz0123456789'\n      let randomStr = ''\n      for (let i = 0; i < 6; i++) {\n        randomStr += chars.charAt(Math.floor(Math.random() * chars.length))\n      }\n\n      return `_${timestamp}_${randomStr}`\n    },\n\n    // 为需求名称添加随机后缀\n    addRandomSuffixToRequirementName(originalName) {\n      const suffix = this.generateRandomSuffix()\n      const finalName = originalName + suffix\n\n      console.log('🎯 [需求名称] 原始名称:', originalName)\n      console.log('🎯 [需求名称] 生成后缀:', suffix)\n      console.log('🎯 [需求名称] 最终名称:', finalName)\n\n      return finalName\n    },\n\n    // 清除调试日志\n    clearDebugLogs() {\n      localStorage.removeItem('opinion_analysis_debug_logs')\n      console.log('🧹 调试日志已清除')\n    },\n\n    // 导出调试日志\n    exportDebugLogs() {\n      try {\n        const logs = JSON.parse(localStorage.getItem('opinion_analysis_debug_logs') || '[]')\n        const logText = logs.map(log =>\n          `[${log.timestamp}] ${log.tag}: ${log.message}${log.data ? '\\n数据: ' + log.data : ''}`\n        ).join('\\n\\n')\n\n        const blob = new Blob([logText], { type: 'text/plain' })\n        const url = URL.createObjectURL(blob)\n        const a = document.createElement('a')\n        a.href = url\n        a.download = `opinion_analysis_debug_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`\n        a.click()\n        URL.revokeObjectURL(url)\n\n        console.log('📄 调试日志已导出')\n      } catch (e) {\n        console.error('导出调试日志失败:', e)\n      }\n    },\n\n    // ==================== 分析进度相关方法 ====================\n\n    // 获取分析状态文本\n    getAnalysisStatusText() {\n      const statusMap = {\n        idle: '等待开始',\n        running: '分析中',\n        generating_report: '正在生成报告',\n        completed: '已完成',\n        failed: '分析失败'\n      }\n\n      // 特殊逻辑：如果进度条已达到100%但分析结果尚未完全生成，显示\"正在生成报告\"\n      if (this.analysisStatus === 'running' && this.analysisProgress >= 100) {\n        return '正在生成报告'\n      }\n\n      return statusMap[this.analysisStatus] || '未知状态'\n    },\n\n    // 格式化日志时间\n    formatLogTime(timestamp) {\n      if (!timestamp) return ''\n      const date = new Date(timestamp)\n      return date.toLocaleTimeString('zh-CN', {\n        hour12: false,\n        hour: '2-digit',\n        minute: '2-digit',\n        second: '2-digit'\n      })\n    },\n\n    // 清空日志\n    clearLogs() {\n      this.analysisLogs = []\n      this.$message.success('日志已清空')\n    },\n\n    // 切换自动滚动\n    toggleAutoScroll() {\n      this.autoScroll = !this.autoScroll\n      if (this.autoScroll) {\n        this.$nextTick(() => {\n          this.scrollToBottom()\n        })\n      }\n    },\n\n    // 滚动到日志底部\n    scrollToBottom() {\n      if (this.$refs.logsContainer) {\n        this.$refs.logsContainer.scrollTop = this.$refs.logsContainer.scrollHeight\n      }\n    },\n\n    // 添加日志\n    addLog(level, message) {\n      const log = {\n        timestamp: new Date().toISOString(),\n        level: level || 'info', // 确保level不为空\n        message: message || '' // 确保message不为空\n      }\n      this.analysisLogs.push(log)\n\n      // 限制日志数量，避免内存溢出\n      if (this.analysisLogs.length > 1000) {\n        this.analysisLogs.splice(0, this.analysisLogs.length - 1000)\n      }\n\n      // 自动滚动到底部\n      if (this.autoScroll) {\n        this.$nextTick(() => {\n          this.scrollToBottom()\n        })\n      }\n    },\n\n    // 分析完成后自动生成报告并上传到OSS\n    async generateAndUploadReportAfterAnalysis(analysisData) {\n      try {\n        this.addLog('info', '正在生成分析报告并上传到OSS...')\n\n        // 确保有需求ID\n        if (!this.currentRequirementId) {\n          await this.ensureRequirementExists()\n          if (!this.currentRequirementId) {\n            throw new Error('无法获取需求ID，报告生成失败')\n          }\n        }\n\n        // 准备报告数据\n        const reportData = {\n          requirement_id: this.currentRequirementId,\n          requirement_name: this.requirementName,\n          entity_keyword: this.entityKeyword,\n          specific_requirement: this.specificRequirement,\n          selected_keywords: this.selectedKeywords,\n          analysis_results: analysisData.analysis_results || {},\n          report_data: {\n            totalArticles: this.reportData.totalArticles,\n            totalKeywords: this.reportData.totalKeywords,\n            dataSources: this.reportData.dataSources,\n            sentiment: this.reportData.sentiment,\n            onlineSearchCount: this.reportData.onlineSearchCount,\n            customSourceCounts: this.reportData.customSourceCounts\n          },\n          enable_online_search: this.enableOnlineSearch,\n          enable_custom_data_source: this.enableCustomDataSource,\n          custom_data_sources: this.customDataSources\n        }\n\n        // 调用后端API生成报告并上传到OSS\n        const response = await generateAndUploadReport(reportData)\n\n        if (response.success && response.data) {\n          const { report_oss_url, page_id } = response.data\n          this.addLog('success', `报告已生成并上传到OSS: ${report_oss_url}`)\n\n          // 保存OSS URL到当前组件状态，供后续使用\n          this.reportOssUrl = report_oss_url\n          this.reportPageId = page_id\n\n          // 保存分析任务到数据库，包含OSS URL\n          await this.saveTaskForPush('', 'immediate')\n\n          return { success: true, oss_url: report_oss_url, page_id }\n        } else {\n          throw new Error(response.msg || '报告生成失败')\n        }\n      } catch (error) {\n        console.error('生成报告并上传到OSS失败:', error)\n        this.addLog('error', '报告生成失败: ' + error.message)\n        this.$message.warning('报告生成失败，但仍可查看本地报告')\n        return { success: false, error: error.message }\n      }\n    },\n\n    // 跳转到报告预览\n    async goToReportPreview() {\n      try {\n        // 在跳转到报告预览页面前，先保存分析任务到数据库\n        this.addLog('info', '正在保存分析报告任务...')\n\n        // 使用immediate类型保存分析报告任务，但不提供push_url\n        const taskResult = await this.saveTaskForPush('', 'immediate')\n        if (taskResult.success) {\n          if (taskResult.exists) {\n            this.addLog('info', '分析报告任务已存在')\n          } else {\n            this.addLog('success', '分析报告任务保存成功')\n            this.$message.success('分析报告已保存')\n          }\n        } else {\n          this.addLog('warning', '分析报告任务保存失败，但仍可查看报告')\n          this.$message.warning('任务保存失败，但仍可查看报告')\n        }\n\n        // 跳转到报告预览页面\n        this.currentStep = 4\n      } catch (error) {\n        console.error('保存分析报告任务失败:', error)\n        this.addLog('error', '分析报告任务保存失败: ' + error.message)\n        this.$message.warning('任务保存失败，但仍可查看报告')\n\n        // 即使保存失败，仍然允许用户查看报告\n        this.currentStep = 4\n      }\n    },\n\n    // 重试分析\n    retryAnalysis() {\n      this.disconnectWebSocket()\n      this.stopStatusPolling()\n      this.analysisStatus = 'idle'\n      this.analysisProgress = 0\n      this.analysisLogs = []\n      this.currentTaskId = null\n      this.currentStep = 2\n    },\n\n    // 取消分析\n    async cancelAnalysis() {\n      try {\n        if (this.currentTaskId) {\n          await cancelAnalysisTask(this.currentTaskId)\n          this.addLog('warning', '分析任务已取消')\n        }\n\n        // 停止HTTP轮询\n        this.stopHttpPolling('analysisProgress')\n        this.addLog('info', '已停止分析进度轮询')\n\n        this.analysisStatus = 'idle'\n        this.analysisProgress = 0\n        this.currentTaskId = null\n        this.$message.info('分析已取消')\n        this.currentStep = 2\n      } catch (error) {\n        console.error('取消分析失败:', error)\n        this.$message.error('取消分析失败')\n      }\n    },\n\n    // ==================== WebSocket相关方法 ====================\n\n    // 获取WebSocket连接URL\n    getWebSocketUrl(taskId) {\n      // 根据环境动态配置WebSocket地址\n      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'\n\n      // 获取WebSocket主机地址\n      let host\n      if (process.env.VUE_APP_WS_HOST) {\n        // 使用环境变量配置的主机地址\n        host = process.env.VUE_APP_WS_HOST\n      } else {\n        // 回退到当前域名\n        host = window.location.host\n      }\n\n      // 构建完整的WebSocket URL\n      const wsUrl = `${protocol}//${host}/ws/analysis-progress/${taskId}`\n      return wsUrl\n    },\n\n    // 连接WebSocket\n    connectWebSocket(taskId) {\n      if (this.websocket) {\n        this.websocket.close()\n      }\n\n      // 动态构建WebSocket URL\n      const wsUrl = this.getWebSocketUrl(taskId)\n      console.log('连接WebSocket:', wsUrl)\n\n      try {\n        this.websocket = new WebSocket(wsUrl)\n\n        this.websocket.onopen = () => {\n          console.log('WebSocket连接已建立:', taskId)\n          this.addLog('info', 'WebSocket连接已建立')\n\n          // 重置重连计数\n          this.reconnectAttempts = 0\n          this.stopReconnect()\n\n          // 启动心跳机制\n          this.startHeartbeat()\n        }\n\n        this.websocket.onmessage = (event) => {\n          try {\n            const data = JSON.parse(event.data)\n            this.handleWebSocketMessage(data)\n          } catch (error) {\n            console.error('解析WebSocket消息失败:', error)\n          }\n        }\n\n        this.websocket.onclose = (event) => {\n          console.log('WebSocket连接已关闭', event.code, event.reason)\n          this.websocket = null\n\n          // 如果不是正常关闭且分析正在进行，尝试重连\n          if (event.code !== 1000 && this.analysisStatus === 'running' && this.reconnectAttempts < this.maxReconnectAttempts) {\n            this.attemptReconnect(taskId)\n          }\n        }\n\n        this.websocket.onerror = (error) => {\n          console.error('WebSocket连接错误:', error)\n          this.addLog('error', `WebSocket连接错误: ${error.message || '未知错误'}`)\n\n          // 尝试重连\n          if (this.analysisStatus === 'running' && this.reconnectAttempts < this.maxReconnectAttempts) {\n            this.attemptReconnect(taskId)\n          }\n        }\n\n      } catch (error) {\n        console.error('创建WebSocket连接失败:', error)\n        this.addLog('error', '创建WebSocket连接失败: ' + error.message)\n      }\n    },\n\n    // 断开WebSocket连接\n    disconnectWebSocket() {\n      this.stopHeartbeat()\n      this.stopReconnect()\n      if (this.websocket) {\n        this.websocket.close()\n        this.websocket = null\n      }\n      this.reconnectAttempts = 0\n    },\n\n    // 尝试重连\n    attemptReconnect(taskId) {\n      if (this.reconnectTimer) {\n        return // 已经在重连中\n      }\n\n      this.reconnectAttempts++\n      const delay = Math.min(1000 * Math.pow(1.5, this.reconnectAttempts - 1), 15000) // 指数退避，最大30秒\n\n      this.addLog('warning', `WebSocket连接断开，${delay/1000}秒后尝试第${this.reconnectAttempts}次重连...`)\n\n      this.reconnectTimer = setTimeout(() => {\n        this.reconnectTimer = null\n        console.log(`尝试第${this.reconnectAttempts}次重连...`)\n        this.connectWebSocket(taskId)\n      }, delay)\n    },\n\n    // 停止重连\n    stopReconnect() {\n      if (this.reconnectTimer) {\n        clearTimeout(this.reconnectTimer)\n        this.reconnectTimer = null\n      }\n    },\n\n    // 启动心跳机制\n    startHeartbeat() {\n      this.stopHeartbeat()\n      this.heartbeatTimer = setInterval(() => {\n        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {\n          this.websocket.send(JSON.stringify({ type: 'ping' }))\n        }\n      }, 30000) // 每30秒发送一次心跳\n    },\n\n    // 停止心跳机制\n    stopHeartbeat() {\n      if (this.heartbeatTimer) {\n        clearInterval(this.heartbeatTimer)\n        this.heartbeatTimer = null\n      }\n    },\n\n    // 处理WebSocket消息\n    handleWebSocketMessage(data) {\n      console.log('收到WebSocket消息:', data)\n\n      switch (data.type) {\n        case 'connection_established':\n          this.addLog('info', data.message)\n          break\n\n        case 'progress_update':\n          this.handleProgressUpdate(data.data)\n          break\n\n        case 'log_message':\n          this.handleLogMessage(data.data)\n          break\n\n        case 'task_status_change':\n          this.handleTaskStatusChange(data.status, data.data)\n          break\n\n        case 'initial_progress':\n          this.handleInitialProgress(data.data)\n          break\n\n        case 'pong':\n          // 心跳响应，保持连接活跃\n          console.log('收到心跳响应:', data.timestamp)\n          break\n\n        case 'error':\n          this.addLog('error', data.message)\n          break\n\n        default:\n          console.log('未知的WebSocket消息类型:', data.type)\n      }\n    },\n\n    // 处理进度更新\n    handleProgressUpdate(data) {\n      if (data.progress_percentage !== undefined) {\n        this.analysisProgress = data.progress_percentage\n      }\n    },\n\n    // 处理日志消息\n    handleLogMessage(data) {\n      this.addLog(data.log_level, data.log_message)\n    },\n\n    // 处理任务状态变更\n    handleTaskStatusChange(status, data) {\n      this.analysisStatus = status\n\n      if (status === 'completed') {\n        this.analysisProgress = 100\n        this.addLog('success', '分析任务已完成！')\n        this.$message.success('分析完成！可以查看报告了')\n\n        // 移除自动跳转逻辑，让用户手动点击\"查看报告\"按钮\n        // setTimeout(() => {\n        //   this.goToReportPreview()\n        // }, 2000)\n      } else if (status === 'failed') {\n        this.addLog('error', '分析任务失败: ' + (data.error_message || '未知错误'))\n        this.$message.error('分析失败，请重试')\n      } else if (status === 'cancelled') {\n        this.addLog('warning', '分析任务已取消')\n        this.$message.info('分析已取消')\n      }\n    },\n\n    // 处理初始进度\n    handleInitialProgress(data) {\n      if (data.task) {\n        this.analysisStatus = data.task.task_status\n        this.analysisProgress = data.task.progress_percentage || 0\n      }\n\n      if (data.logs && data.logs.length > 0) {\n        this.analysisLogs = data.logs.map(log => ({\n          timestamp: log.create_time,\n          level: log.log_level,\n          message: log.log_message\n        }))\n      }\n    },\n\n    // ==================== 状态轮询相关方法 ====================\n\n    // 启动状态轮询\n    startStatusPolling() {\n      this.stopStatusPolling()\n      this.statusPollingTimer = setInterval(async () => {\n        if (this.currentTaskId && this.analysisStatus === 'running') {\n          await this.checkTaskStatus()\n        }\n      }, 5000) // 每5秒检查一次状态\n    },\n\n    // 停止状态轮询\n    stopStatusPolling() {\n      if (this.statusPollingTimer) {\n        clearInterval(this.statusPollingTimer)\n        this.statusPollingTimer = null\n      }\n    },\n\n    // 检查任务状态\n    async checkTaskStatus() {\n      try {\n        const response = await getAnalysisProgress(this.currentTaskId)\n        if (response.code === 200 && response.data) {\n          const taskData = response.data\n\n          // 更新进度\n          if (taskData.progress_percentage !== undefined) {\n            this.analysisProgress = taskData.progress_percentage\n          }\n\n          // 检查任务状态\n          if (taskData.task_status === 'completed' && this.analysisStatus !== 'completed') {\n            this.analysisStatus = 'completed'\n            this.analysisProgress = 100\n            this.addLog('success', '分析任务已完成！')\n            this.$message.success('分析完成！可以查看报告了')\n\n            // 停止轮询\n            this.stopStatusPolling()\n\n            // 移除自动跳转逻辑，让用户手动点击\"查看报告\"按钮\n            // setTimeout(() => {\n            //   this.goToReportPreview()\n            // }, 2000)\n          } else if (taskData.task_status === 'failed' && this.analysisStatus !== 'failed') {\n            this.analysisStatus = 'failed'\n            this.addLog('error', '分析任务失败')\n            this.$message.error('分析失败，请重试')\n            this.stopStatusPolling()\n          }\n        }\n      } catch (error) {\n        console.warn('检查任务状态失败:', error)\n        // 不显示错误消息，避免干扰用户\n      }\n    },\n\n    // ==================== 原有方法 ====================\n\n    // 切换关键词选择状态\n    toggleKeyword(keyword) {\n      const index = this.selectedKeywords.indexOf(keyword)\n      if (index > -1) {\n        // 如果已选中，则取消选择\n        this.selectedKeywords.splice(index, 1)\n      } else {\n        // 如果未选中，检查是否超过最大数量\n        if (this.selectedKeywords.length < this.maxKeywords) {\n          this.selectedKeywords.push(keyword)\n        } else {\n          this.$message.warning(`最多只能选择${this.maxKeywords}个关键词`)\n        }\n      }\n    },\n\n    // 检查关键词是否已选中\n    isKeywordSelected(keyword) {\n      return this.selectedKeywords.includes(keyword)\n    },\n\n    // 切换分类选择状态\n    toggleCategorySelection(categoryName, categoryKeywords) {\n      // 检查该分类下的所有关键词是否都已选中\n      const allSelected = categoryKeywords.every(keyword => this.isKeywordSelected(keyword))\n\n      if (allSelected) {\n        // 如果都已选中，则取消选择该分类下的所有关键词\n        categoryKeywords.forEach(keyword => {\n          const index = this.selectedKeywords.indexOf(keyword)\n          if (index > -1) {\n            this.selectedKeywords.splice(index, 1)\n          }\n        })\n        this.$message.info(`已取消选择\"${categoryName}\"分类下的所有关键词`)\n      } else {\n        // 如果没有全部选中，则选择该分类下的所有关键词\n        categoryKeywords.forEach(keyword => {\n          if (!this.isKeywordSelected(keyword) && this.selectedKeywords.length < this.maxKeywords) {\n            this.selectedKeywords.push(keyword)\n          }\n        })\n\n        // 检查是否因为数量限制而无法全部选择\n        const notSelected = categoryKeywords.filter(keyword => !this.isKeywordSelected(keyword))\n        if (notSelected.length > 0) {\n          this.$message.warning(`由于数量限制，无法选择\"${categoryName}\"分类下的所有关键词`)\n        } else {\n          this.$message.success(`已选择\"${categoryName}\"分类下的所有关键词`)\n        }\n      }\n    },\n\n\n    // 前往下一步\n    goToNextStep() {\n      // 显示验证样式\n      this.showValidation = true\n\n      // 验证表单是否填写完整\n      if (!this.entityKeyword.trim()) {\n        this.$message.warning('请填写实体关键词')\n        return\n      }\n\n      if (!this.specificRequirement.trim()) {\n        this.$message.warning('请填写具体需求')\n        return\n      }\n\n      if (this.selectedKeywords.length === 0) {\n        this.$message.warning('请至少选择一个关键词')\n        return\n      }\n\n      // 验证通过，隐藏验证样式并进入下一步\n      this.showValidation = false\n      if (this.currentStep < 3) {\n        this.currentStep++\n\n        // 如果进入第二步（数据概览），加载数据源列表\n        if (this.currentStep === 2) {\n          this.loadDataSourceList()\n        }\n      }\n    },\n\n    // 返回上一步\n    goToPreviousStep() {\n      if (this.currentStep > 1) {\n        this.currentStep--\n      }\n    },\n\n    // 检查套餐次数限制\n    async checkPackageLimits() {\n      try {\n        // 获取当前用户ID\n        const currentUserId = this.$store.getters.id || this.$store.state.user.id\n\n        if (!currentUserId) {\n          this.$message.error('无法获取用户信息，请重新登录')\n          return { canProceed: false }\n        }\n\n        // 获取用户统计数据\n        const response = await getDashboardStatistics(currentUserId)\n\n        if (response.code !== 200) {\n          this.$message.error('获取套餐信息失败，请稍后重试')\n          return { canProceed: false }\n        }\n\n        const userStats = response.data\n        const packageLimit = userStats.package_limit || 0\n        const remainingCount = userStats.remaining_count || 0\n        const totalAnalysis = userStats.total_analysis || 0\n\n        // 检查是否为无限制套餐\n        const isUnlimitedPackage = packageLimit === -1\n\n        if (isUnlimitedPackage) {\n          console.log('用户拥有无限制套餐，允许继续操作')\n          return { canProceed: true }\n        }\n\n        // 检查是否没有剩余次数\n        if (remainingCount <= 0) {\n          this.$confirm(\n            '您的套餐次数已用完，无法生成关键词。请升级套餐后继续使用。',\n            '套餐次数不足',\n            {\n              confirmButtonText: '升级套餐',\n              cancelButtonText: '取消',\n              type: 'warning',\n              showClose: false\n            }\n          ).then(() => {\n            this.upgradePackage()\n          }).catch(() => {\n            // 用户取消，不做任何操作\n          })\n          return { canProceed: false }\n        }\n\n        // 检查是否剩余次数较少（少于5次或少于总数的20%）\n        const isLowRemaining = remainingCount <= 5 ||\n                              (packageLimit > 0 && (remainingCount / packageLimit) <= 0.2)\n\n        if (isLowRemaining) {\n          try {\n            await this.$confirm(\n              `您的套餐剩余次数较少（${remainingCount}次），是否继续生成关键词？`,\n              '次数提醒',\n              {\n                confirmButtonText: '继续生成',\n                cancelButtonText: '升级套餐',\n                type: 'warning'\n              }\n            )\n            // 用户选择继续，允许操作\n            return { canProceed: true }\n          } catch {\n            // 用户选择升级套餐\n            this.upgradePackage()\n            return { canProceed: false }\n          }\n        }\n\n        // 次数充足，允许继续\n        console.log(`套餐检查通过，剩余次数: ${remainingCount}`)\n        return { canProceed: true }\n\n      } catch (error) {\n        console.error('套餐检查失败:', error)\n        this.$message.error('套餐检查失败，请稍后重试')\n        return { canProceed: false }\n      }\n    },\n\n    // 升级套餐方法\n    upgradePackage() {\n      // 跳转到套餐升级页面\n      this.$router.push('/set-meal/set-meal')\n    },\n\n    // 扣减套餐使用次数\n    async deductPackageUsage(operationType = '关键词生成') {\n      try {\n        console.log(`开始扣减套餐次数，操作类型: ${operationType}`)\n\n        // 这里可以调用后端API来扣减次数\n        // 目前先通过创建一个opinion_requirement记录来实现扣减\n        // 因为后端统计逻辑是基于opinion_requirement表的记录数\n\n        // 注意：实际的扣减逻辑应该在后端的关键词生成API中处理\n        // 这里只是一个前端的提示，真正的扣减应该在后端API调用成功后自动进行\n\n        console.log(`套餐次数扣减完成，操作类型: ${operationType}`)\n\n      } catch (error) {\n        console.error('扣减套餐次数失败:', error)\n        // 扣减失败不影响用户体验，只记录日志\n      }\n    },\n\n    // 开始分析\n    async startAnalysis() {\n      try {\n        // 首先检查套餐次数限制\n        const packageCheckResult = await this.checkPackageLimits()\n        if (!packageCheckResult.canProceed) {\n          return // 如果不能继续，直接返回\n        }\n\n        // 验证基本表单信息\n        if (!this.requirementName.trim()) {\n          this.$message.error('请填写需求名称')\n          return\n        }\n\n        if (!this.entityKeyword.trim()) {\n          this.$message.error('请填写实体关键词')\n          return\n        }\n\n        if (!this.specificRequirement.trim()) {\n          this.$message.error('请填写具体需求')\n          return\n        }\n\n        if (this.selectedKeywords.length === 0) {\n          this.$message.error('请至少选择一个关键词')\n          return\n        }\n\n        // 验证至少启用联网搜索（自定义数据源为可选）\n        if (!this.enableOnlineSearch) {\n          // 如果没有启用联网搜索，但启用了自定义数据源且有配置的数据源，则允许继续\n          if (this.enableCustomDataSource && this.customDataSources.length > 0) {\n            // 允许继续，使用自定义数据源\n          } else {\n            this.$message.error('请至少启用联网搜索或配置自定义数据源')\n            return\n          }\n        }\n\n        // 如果选择了自定义数据源，检查是否有配置的数据源（仅警告，不阻止）\n        if (this.enableCustomDataSource && this.customDataSources.length === 0) {\n          this.$message.warning('未配置自定义数据源，将仅使用联网搜索')\n          // 不return，允许继续执行\n        }\n\n        // 立即跳转到第三步分析进度页面\n        this.currentStep = 3\n        this.analysisStatus = 'running'\n        this.analysisProgress = 0 // 重置进度条为0%\n        this.analysisLogs = []\n\n        // 添加初始日志\n        this.addLog('info', '开始启动AI分析引擎...')\n        this.addLog('info', '正在验证分析参数...')\n        this.addLog('info', '参数验证通过，开始分析任务...')\n\n        // 如果没有需求ID，先尝试创建需求\n        if (!this.currentRequirementId) {\n          this.addLog('info', '正在创建临时需求记录...')\n          await this.createTemporaryRequirement()\n        }\n\n        // 首先创建分析任务\n        const taskData = {\n          requirement_id: this.currentRequirementId || 0, // 如果仍然没有ID，使用0作为临时值\n          user_id: 1, // 默认用户ID\n          task_name: `${this.requirementName} - 分析任务`,\n          analysis_config: {\n            entity_keyword: this.entityKeyword,\n            specific_requirement: this.specificRequirement,\n            selected_keywords: this.selectedKeywords,\n            enable_online_search: this.enableOnlineSearch,\n            enable_custom_data_source: this.enableCustomDataSource,\n            custom_data_sources: this.customDataSources.map(url => ({\n              url: url,\n              name: this.extractDomainName(url)\n            }))\n          }\n        }\n\n        this.addLog('info', '正在创建分析任务...')\n\n        // 创建分析任务\n        const taskResponse = await createAnalysisProgressTask(taskData)\n\n        if (taskResponse.code === 200) {\n          this.currentTaskId = taskResponse.data.task_id\n          this.addLog('info', `分析任务已创建，任务ID: ${this.currentTaskId}`)\n\n          // 使用HTTP轮询监控分析进度（替代WebSocket）\n          this.startAnalysisProgressPolling()\n\n          // 添加日志提示使用HTTP轮询\n          this.addLog('info', '已启动HTTP轮询监控分析进度...')\n\n          // 异步执行分析\n          this.performAsyncAnalysis()\n        } else {\n          throw new Error(taskResponse.msg || '创建分析任务失败')\n        }\n\n      } catch (error) {\n        console.error('分析启动失败:', error)\n        this.analysisStatus = 'failed'\n        this.addLog('error', '分析启动失败: ' + error.message)\n        this.$message.error('分析启动失败，请稍后重试')\n      }\n    },\n\n    // 创建临时需求记录\n    async createTemporaryRequirement() {\n      try {\n        // 为需求名称添加随机后缀以确保唯一性\n        const originalRequirementName = this.requirementName\n        const finalRequirementName = this.addRandomSuffixToRequirementName(originalRequirementName)\n\n        const requirementData = {\n          requirement_name: finalRequirementName,\n          entity_keyword: this.entityKeyword,\n          specific_requirement: this.specificRequirement,\n          priority: 'medium'\n        }\n\n        this.addLog('info', '正在保存需求信息...')\n        console.log('🚀 [临时需求] 原始需求名称:', originalRequirementName)\n        console.log('🚀 [临时需求] 最终需求名称:', finalRequirementName)\n\n        const response = await createRequirement(requirementData)\n\n        if (response.success) {\n          // 更新界面显示的需求名称\n          this.requirementName = finalRequirementName\n          this.currentRequirementId = response.data.id\n          this.requirementSaved = true\n          this.addLog('info', `需求信息保存成功，最终名称：${finalRequirementName}`)\n        } else {\n          // 如果创建失败（比如名称重复），尝试查找已存在的需求\n          if (response.msg && response.msg.includes('已存在')) {\n            this.addLog('info', '需求已存在，正在查找已有记录...')\n            await this.findExistingRequirementId()\n            if (this.currentRequirementId) {\n              this.addLog('info', '找到已存在的需求记录')\n            }\n          } else {\n            this.addLog('warning', '需求保存失败，将使用临时数据进行分析')\n          }\n        }\n      } catch (error) {\n        console.warn('创建临时需求失败:', error)\n        this.addLog('warning', '需求保存失败，将使用临时数据进行分析')\n        // 即使创建需求失败，也继续分析流程\n      }\n    },\n\n    // 执行异步分析\n    async performAsyncAnalysis() {\n      try {\n        this.addLog('info', '正在连接AI分析引擎...')\n\n        // 构建原有的分析请求参数\n        const analysisData = {\n          requirement_id: this.currentRequirementId || 0, // 如果没有ID，使用0作为临时值\n          entity_keyword: this.entityKeyword,\n          specific_requirement: this.specificRequirement,\n          selected_keywords: this.selectedKeywords,\n          enable_online_search: this.enableOnlineSearch,\n          enable_custom_data_source: this.enableCustomDataSource,\n          custom_data_sources: this.customDataSources.map(url => ({\n            url: url,\n            name: this.extractDomainName(url)\n          }))\n        }\n\n        // 调用原有的分析接口\n        const response = await startAnalysis(analysisData)\n\n        if (response.code === 200) {\n          this.addLog('success', '分析引擎启动成功！')\n\n          // 更新报告数据\n          this.updateReportData(response.data)\n          this.showAnalysisResults(response.data)\n\n          // 完成分析任务\n          if (this.currentTaskId) {\n            await completeAnalysisTask(this.currentTaskId, {\n              total_articles: response.data.analysis_results ?\n                Object.values(response.data.analysis_results).reduce((total, result) => {\n                  return total + (result.data?.articles?.length || 0)\n                }, 0) : 0,\n              analysis_results: response.data.analysis_results\n            })\n          }\n\n          // 手动触发分析完成状态更新\n          this.analysisStatus = 'completed'\n          this.analysisProgress = 100\n          this.addLog('success', '分析任务已完成！')\n\n          // 自动生成报告并上传到OSS\n          await this.generateAndUploadReportAfterAnalysis(response.data)\n\n          this.$message.success('分析完成！报告已生成并上传')\n\n          // 显式停止分析进度轮询\n          this.stopHttpPolling('analysisProgress')\n          this.addLog('info', '报告生成完成，已停止轮询')\n\n          // 移除自动跳转逻辑，让用户手动点击\"查看报告\"按钮\n          // setTimeout(() => {\n          //   this.goToReportPreview()\n          // }, 2000)\n\n        } else {\n          this.analysisStatus = 'failed'\n          this.addLog('error', '分析启动失败: ' + response.msg)\n\n          // 标记任务失败\n          if (this.currentTaskId) {\n            await this.failCurrentTask(response.msg)\n          }\n        }\n\n      } catch (error) {\n        this.analysisStatus = 'failed'\n        this.addLog('error', '分析过程中发生错误: ' + error.message)\n\n        // 标记任务失败\n        if (this.currentTaskId) {\n          await this.failCurrentTask(error.message)\n        }\n      }\n    },\n\n    // 标记当前任务失败\n    async failCurrentTask(errorMessage) {\n      try {\n        // 这里应该调用失败接口，但当前后端没有提供，所以使用取消接口\n        await cancelAnalysisTask(this.currentTaskId)\n      } catch (error) {\n        console.error('标记任务失败时出错:', error)\n      }\n    },\n\n    // 启动进度模拟（实际项目中应该通过WebSocket接收真实进度）\n    startProgressSimulation() {\n      const updateProgress = () => {\n        if (this.analysisStatus === 'running' && this.analysisProgress < 90) {\n          this.analysisProgress += Math.random() * 10\n          if (this.analysisProgress > 90) {\n            this.analysisProgress = 90\n          }\n          setTimeout(updateProgress, 1000 + Math.random() * 2000)\n        }\n      }\n      updateProgress()\n    },\n\n    // 显示分析结果\n    showAnalysisResults(results) {\n      console.log('分析结果:', results)\n\n      // 可以在这里添加结果展示逻辑\n      // 比如跳转到结果页面或显示结果弹窗\n      this.$notify({\n        title: '分析完成',\n        message: `分析任务已完成，需求ID: ${results.requirement_id}`,\n        type: 'success',\n        duration: 5000\n      })\n    },\n\n    // 处理联网搜索结果\n    handleOnlineSearchResults(searchResults) {\n      console.log('联网搜索结果:', searchResults)\n\n      if (searchResults.success && searchResults.data) {\n        const data = searchResults.data\n\n        // 显示搜索统计信息\n        let message = '联网搜索完成！\\n'\n        if (data.articles && data.articles.length > 0) {\n          message += `获取到 ${data.articles.length} 条相关信息\\n`\n        }\n        if (searchResults.saved_count !== undefined) {\n          message += `已保存 ${searchResults.saved_count} 条数据到数据库\\n`\n        }\n        if (data.sentiment_analysis) {\n          const sentiment = data.sentiment_analysis\n          message += `情感分析：正面 ${sentiment.positive}%，中性 ${sentiment.neutral}%，负面 ${sentiment.negative}%`\n        }\n\n        this.$notify({\n          title: '联网搜索结果',\n          message: message,\n          type: 'info',\n          duration: 8000\n        })\n      }\n    },\n\n    // 切换联网搜索\n    toggleOnlineSearch() {\n      this.enableOnlineSearch = !this.enableOnlineSearch\n    },\n\n    // 切换自定义数据源搜索\n    toggleCustomDataSource() {\n      this.enableCustomDataSource = !this.enableCustomDataSource\n\n      // 如果启用了自定义数据源，加载数据源列表\n      if (this.enableCustomDataSource) {\n        this.loadDataSourceList()\n      }\n    },\n\n    // 显示新增数据源表单\n    showAddSourceForm() {\n      this.showAddSourceInput = true\n      this.newSourceUrl = ''\n    },\n\n    // 隐藏新增数据源表单\n    hideAddSourceForm() {\n      this.showAddSourceInput = false\n      this.newSourceUrl = ''\n    },\n\n    // 确认新增数据源\n    async confirmAddSource() {\n      if (!this.newSourceUrl.trim()) {\n        this.$message.warning('请输入数据源网址')\n        return\n      }\n\n      // 简单的URL格式验证\n      const urlPattern = /^https?:\\/\\/.+/\n      if (!urlPattern.test(this.newSourceUrl.trim())) {\n        this.$message.warning('请输入有效的网址格式')\n        return\n      }\n\n      // 检查是否已存在相同的数据源\n      const trimmedUrl = this.newSourceUrl.trim()\n      if (this.customDataSources.includes(trimmedUrl)) {\n        this.$message.warning('该数据源已存在')\n        return\n      }\n\n      // 检查是否有当前需求ID\n      if (!this.currentRequirementId) {\n        this.$message.warning('请先保存需求信息')\n        return\n      }\n\n      try {\n        // 调用API保存数据源到数据库\n        const dataSourceData = {\n          requirement_id: this.currentRequirementId,\n          source_type: 'custom',\n          source_name: this.extractDomainName(trimmedUrl),\n          source_url: trimmedUrl,\n          remark: '用户自定义数据源'\n        }\n\n        const response = await createDataSource(dataSourceData)\n\n        if (response.success) {\n          // 将新的数据源添加到自定义数据源列表中\n          this.customDataSources.push(trimmedUrl)\n          // 自动启用自定义数据源搜索\n          this.enableCustomDataSource = true\n\n          this.$message.success('数据源添加成功')\n          // 清空输入框，但保持表单显示，允许继续添加\n          this.newSourceUrl = ''\n        } else {\n          this.$message.error('数据源添加失败：' + response.msg)\n        }\n      } catch (error) {\n        console.error('添加数据源失败:', error)\n        this.$message.error('数据源添加失败，请稍后重试')\n      }\n    },\n\n    // 从URL中提取域名作为数据源名称\n    extractDomainName(url) {\n      try {\n        const urlObj = new URL(url)\n        return urlObj.hostname.replace('www.', '')\n      } catch (error) {\n        // 如果URL解析失败，返回原始URL\n        return url\n      }\n    },\n\n    // 删除自定义数据源\n    removeCustomSource(index) {\n      // 从自定义数据源列表中移除\n      this.customDataSources.splice(index, 1)\n\n      // 如果没有自定义数据源了，自动关闭自定义数据源搜索\n      if (this.customDataSources.length === 0) {\n        this.enableCustomDataSource = false\n      }\n\n      this.$message.success('数据源删除成功')\n    },\n\n    // 刷新数据源列表\n    async refreshDataSourceList() {\n      try {\n        this.dataSourceListState.loading = true\n        await this.loadDataSourceList()\n        this.$message.success('数据源列表刷新成功')\n      } catch (error) {\n        console.error('刷新数据源列表失败:', error)\n        this.$message.error('刷新数据源列表失败')\n      } finally {\n        this.dataSourceListState.loading = false\n      }\n    },\n\n    // 加载数据源列表\n    async loadDataSourceList() {\n      try {\n        this.dataSourceListState.loading = true\n\n        const response = await getDataSourceList({\n          page_num: 1,\n          page_size: 1000 // 获取所有数据，前端分页\n        })\n\n        if (response.code === 200) {\n          // 处理分页数据结构 - records字段在响应根级别\n          if (response.records) {\n            this.dataSourceList = response.records\n          } else if (response.data && response.data.records) {\n            this.dataSourceList = response.data.records\n          } else if (response.data && response.data.rows) {\n            this.dataSourceList = response.data.rows\n          } else if (response.data && Array.isArray(response.data)) {\n            this.dataSourceList = response.data\n          } else {\n            this.dataSourceList = []\n          }\n        } else {\n          this.dataSourceList = []\n        }\n\n        console.log('加载数据源列表成功:', this.dataSourceList)\n        console.log('分页数据源列表:', this.paginatedDataSources)\n      } catch (error) {\n        console.error('加载数据源列表失败:', error)\n        this.dataSourceList = []\n        this.$message.error('加载数据源列表失败: ' + error.message)\n      } finally {\n        this.dataSourceListState.loading = false\n      }\n    },\n\n    // 处理数据源分页变化\n    handleDataSourcePageChange(page) {\n      this.dataSourceListState.current_page = page\n    },\n\n    // 处理数据源每页大小变化\n    handleDataSourceSizeChange(size) {\n      this.dataSourceListState.page_size = size\n      this.dataSourceListState.current_page = 1\n    },\n\n    // 处理数据源选择变化\n    handleDataSourceSelectionChange(selection) {\n      this.selectedDataSources = selection\n      console.log('选中的数据源:', selection)\n    },\n\n    // 删除数据源\n    async deleteDataSource(row) {\n      try {\n        await this.$confirm(`确定要删除数据源 \"${row.sourceUrl}\" 吗？`, '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        })\n\n        // 调用删除API - 使用导入的API方法\n        const response = await deleteDataSourceAPI(row.id)\n\n        if (response.code === 200) {\n          this.$message.success('删除成功')\n          // 重新加载数据源列表\n          await this.loadDataSourceList()\n        } else {\n          this.$message.error(response.msg || '删除失败')\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('删除数据源失败:', error)\n          this.$message.error('删除失败: ' + (error.message || '未知错误'))\n        }\n      }\n    },\n\n\n\n    // 生成关联词\n    async generateRelatedWords() {\n      // 首先检查套餐次数限制\n      const packageCheckResult = await this.checkPackageLimits()\n      if (!packageCheckResult.canProceed) {\n        return // 如果不能继续，直接返回\n      }\n\n      // 检查是否填写了实体关键词和具体需求\n      if (!this.entityKeyword.trim()) {\n        this.$message.warning('请先填写实体关键词')\n        return\n      }\n\n      if (!this.specificRequirement.trim()) {\n        this.$message.warning('请先填写具体需求')\n        return\n      }\n\n      if (!this.requirementName.trim()) {\n        this.$message.warning('请先填写需求名称')\n        return\n      }\n\n      // 先尝试保存或更新需求到数据库\n      this.$message.info('正在检查需求并生成关联词...')\n\n      try {\n        let response\n\n        // 检查是否需要更新已存在的需求\n        if (this.currentRequirementId && this.requirementModified) {\n          console.log('🔄 [生成关联词] 更新已存在的需求，ID:', this.currentRequirementId)\n\n          const updateData = {\n            entity_keyword: this.entityKeyword,\n            specific_requirement: this.specificRequirement,\n            priority: 'medium'\n          }\n\n          response = await updateRequirement(this.currentRequirementId, updateData)\n\n          if (response.success) {\n            this.requirementModified = false\n            console.log('✅ [生成关联词] 需求更新成功')\n          }\n        } else if (!this.currentRequirementId) {\n          // 创建新需求\n          console.log('🚀 [生成关联词] 创建新需求')\n\n          // 为需求名称添加随机后缀以确保唯一性\n          const originalRequirementName = this.requirementName\n          const finalRequirementName = this.addRandomSuffixToRequirementName(originalRequirementName)\n\n          const requirementData = {\n            requirement_name: finalRequirementName,\n            entity_keyword: this.entityKeyword,\n            specific_requirement: this.specificRequirement,\n            priority: 'medium'\n          }\n\n          console.log('📝 [生成关联词] 原始需求名称:', originalRequirementName)\n          console.log('📝 [生成关联词] 最终需求名称:', finalRequirementName)\n          console.log('📝 [生成关联词] 请求数据:', requirementData)\n\n          response = await createRequirement(requirementData)\n\n          if (response.success) {\n            // 更新界面显示的需求名称为最终保存的名称\n            this.requirementName = finalRequirementName\n            this.currentRequirementId = response.data.id\n            this.requirementSaved = true\n            this.requirementModified = false\n            console.log('🆔 [生成关联词] 保存的需求ID:', this.currentRequirementId)\n          }\n        } else {\n          // 需求已存在且未修改，直接使用\n          console.log('✅ [生成关联词] 使用已存在的需求，ID:', this.currentRequirementId)\n          response = { success: true }\n        }\n\n        console.log('📥 [生成关联词] API响应:', response)\n        console.log('📊 [生成关联词] 响应类型:', typeof response)\n        console.log('✅ [生成关联词] 响应成功状态:', response?.success)\n        console.log('💬 [生成关联词] 响应消息:', response?.msg)\n\n        if (response.success) {\n          console.log('✅ [生成关联词] 需求操作成功')\n\n          if (this.currentRequirementId && this.requirementSaved) {\n            this.$message.success('需求信息已更新')\n          } else {\n            this.$message.success(`需求保存成功，最终名称：${this.requirementName}`)\n          }\n\n          console.log('🆔 [生成关联词] 当前需求ID:', this.currentRequirementId)\n          console.log('🎯 [生成关联词] 界面需求名称:', this.requirementName)\n        } else {\n          console.log('❌ [生成关联词] 需求创建失败，开始检查错误类型')\n          console.log('🔍 [生成关联词] 错误消息:', response.msg)\n\n          // 检查是否是需求名称重复的错误\n          const isDuplicateError = response.msg && response.msg.includes('已存在')\n          console.log('🔄 [生成关联词] 是否为重复需求错误:', isDuplicateError)\n\n          if (isDuplicateError) {\n            console.log('🎯 [生成关联词] 检测到重复需求，跳过保存直接生成关联词')\n            // 需求已存在，给用户提示并尝试获取已存在需求的ID\n            this.$message.info('需求已存在，直接生成关联词')\n            this.requirementSaved = true\n\n            // 尝试通过需求名称查找已存在的需求ID\n            try {\n              console.log('🔍 [生成关联词] 开始查找已存在需求的ID')\n              await this.findExistingRequirementId()\n              console.log('✅ [生成关联词] 成功找到已存在需求ID:', this.currentRequirementId)\n            } catch (findError) {\n              console.warn('⚠️ [生成关联词] 查找已存在需求ID失败:', findError)\n              // 即使查找失败，也继续生成关联词\n            }\n          } else {\n            console.log('💥 [生成关联词] 其他类型错误，停止执行')\n            this.$message.error('需求保存失败：' + response.msg)\n            return\n          }\n        }\n      } catch (error) {\n        console.log('💥 [生成关联词] 捕获到异常，开始详细分析')\n        console.error('🔍 [生成关联词] 错误对象:', error)\n        console.log('📝 [生成关联词] 错误消息:', error.message)\n        console.log('🌐 [生成关联词] 错误响应:', error.response)\n        console.log('📊 [生成关联词] 错误状态码:', error.response?.status)\n        console.log('💬 [生成关联词] 错误响应数据:', error.response?.data)\n\n        // 检查是否是需求名称重复的错误（可能被axios拦截器处理）\n        // 检查多种可能的错误消息格式\n        const errorMessage = error.message || ''\n        const responseMsg = error.response?.data?.msg || ''\n\n        console.log('🔍 [生成关联词] 分析错误消息:')\n        console.log('  - error.message:', errorMessage)\n        console.log('  - response.data.msg:', responseMsg)\n\n        // 多重检测逻辑\n        const isExistError = errorMessage.includes('已存在') ||\n                           errorMessage.includes('already exists') ||\n                           responseMsg.includes('已存在') ||\n                           responseMsg.includes('already exists')\n\n        // 特殊情况：错误消息为空但可能是重复需求错误\n        // 基于以下线索判断：1. HTTP状态码500 2. 错误消息包含\"创建舆情需求失败\"\n        const isPossibleDuplicateError = !isExistError &&\n                                       (errorMessage.includes('创建舆情需求失败') ||\n                                        responseMsg.includes('创建舆情需求失败')) &&\n                                       (error.response?.status === 500 || errorMessage.includes('500'))\n\n        console.log('🎯 [生成关联词] 错误类型判断:')\n        console.log('  - 明确的重复错误:', isExistError)\n        console.log('  - 可能的重复错误:', isPossibleDuplicateError)\n\n        if (isExistError || isPossibleDuplicateError) {\n          const errorType = isExistError ? '明确检测到重复需求' : '推测为重复需求错误'\n          console.log(`🎯 [生成关联词] ${errorType}，跳过保存直接生成关联词`)\n\n          // 需求已存在，给用户提示并尝试获取已存在需求的ID\n          this.$message.info('需求已存在，直接生成关联词')\n          this.requirementSaved = true\n\n          // 尝试通过需求名称查找已存在的需求ID\n          try {\n            console.log('🔍 [生成关联词] 开始查找已存在需求的ID')\n            await this.findExistingRequirementId()\n            console.log('✅ [生成关联词] 成功找到已存在需求ID:', this.currentRequirementId)\n          } catch (findError) {\n            console.warn('⚠️ [生成关联词] 查找已存在需求ID失败:', findError)\n            // 即使查找失败，也继续生成关联词\n          }\n        } else {\n          console.log('💥 [生成关联词] 其他类型错误，停止执行')\n          this.$message.error('需求保存失败，请重试')\n          return\n        }\n      }\n\n      // 保存成功或需求已存在后，生成关联词\n      console.log('🎯 [生成关联词] 开始执行关联词生成')\n      console.log('📊 [生成关联词] 当前状态:')\n      console.log('  - requirementSaved:', this.requirementSaved)\n      console.log('  - currentRequirementId:', this.currentRequirementId)\n      console.log('  - requirementName:', this.requirementName)\n\n      this.generateKeywordsOnly()\n    },\n\n    // 查找已存在需求的ID\n    async findExistingRequirementId() {\n      console.log('🔍 [查找需求ID] 开始查找已存在需求')\n      console.log('📝 [查找需求ID] 查找条件 - 需求名称:', this.requirementName)\n\n      try {\n        const queryParams = {\n          page: 1,\n          size: 100,\n          requirement_name: this.requirementName\n        }\n\n        console.log('🚀 [查找需求ID] 调用需求列表API，参数:', queryParams)\n        const response = await getRequirementList(queryParams)\n\n        console.log('📥 [查找需求ID] API响应:', response)\n        console.log('✅ [查找需求ID] 响应成功状态:', response?.success)\n        console.log('📊 [查找需求ID] 数据行数:', response?.data?.rows?.length)\n\n        if (response.success && response.data && response.data.rows) {\n          console.log('🔍 [查找需求ID] 在返回的需求列表中查找匹配项')\n          console.log('📋 [查找需求ID] 需求列表:', response.data.rows.map(req => ({\n            id: req.id,\n            name: req.requirementName\n          })))\n\n          const existingRequirement = response.data.rows.find(\n            req => req.requirementName === this.requirementName\n          )\n\n          if (existingRequirement) {\n            this.currentRequirementId = existingRequirement.id\n            console.log('✅ [查找需求ID] 找到匹配的需求:', existingRequirement)\n            console.log('🆔 [查找需求ID] 设置需求ID:', this.currentRequirementId)\n          } else {\n            console.log('❌ [查找需求ID] 未找到匹配的需求')\n          }\n        } else {\n          console.log('❌ [查找需求ID] API响应格式异常或无数据')\n        }\n      } catch (error) {\n        console.error('💥 [查找需求ID] 查找失败:', error)\n        throw error\n      }\n    },\n\n    // 重新生成关联词（不保存到数据库）\n    regenerateKeywords() {\n      // 检查是否填写了实体关键词和具体需求\n      if (!this.entityKeyword.trim()) {\n        this.$message.warning('请先填写实体关键词')\n        return\n      }\n\n      if (!this.specificRequirement.trim()) {\n        this.$message.warning('请先填写具体需求')\n        return\n      }\n\n      // 只生成关联词，不保存需求到数据库\n      this.generateKeywordsOnly()\n    },\n\n    // 纯生成关联词方法（使用AI接口）\n    async generateKeywordsOnly() {\n      // 验证必要参数\n      if (!this.specificRequirement.trim()) {\n        this.$message.warning('请先填写具体需求内容')\n        return\n      }\n\n      if (this.specificRequirement.trim().length < 5) {\n        this.$message.warning('需求内容过短，请提供更详细的需求描述')\n        return\n      }\n\n      // 显示加载状态\n      const loadingMessage = this.$message({\n        message: '正在调用AI生成关联词，请稍候...',\n        type: 'info',\n        duration: 0, // 不自动关闭\n        showClose: false\n      })\n\n      try {\n        console.log('🚀 [AI关联词] 开始调用AI生成关联词接口')\n        console.log('📝 [AI关联词] 需求内容:', this.specificRequirement)\n\n        // 调用AI关联词生成接口\n        const requestData = {\n          requirement_content: this.specificRequirement.trim(),\n          max_count: 20 // 生成更多关联词供用户选择\n        }\n\n        console.log('📤 [AI关联词] 请求参数:', requestData)\n        const response = await generateRelatedKeywords(requestData)\n        console.log('📥 [AI关联词] API响应:', response)\n\n        // 关闭加载消息\n        if (loadingMessage) {\n          loadingMessage.close()\n        }\n\n        if (response.code === 200 && response.data) {\n          const { keywords } = response.data\n\n          // 验证返回的关联词数据\n          if (Array.isArray(keywords) && keywords.length > 0) {\n            // 数据验证和去重处理\n            const validKeywords = this.processAIKeywords(keywords)\n\n            if (validKeywords.length > 0) {\n              // 保存所有生成的关键词\n              this.generatedKeywords = [...validKeywords]\n\n              // 默认选中前几个关键词（不超过最大数量）\n              this.selectedKeywords = []\n              validKeywords.forEach(word => {\n                if (this.selectedKeywords.length < this.maxKeywords) {\n                  this.selectedKeywords.push(word)\n                }\n              })\n\n              this.$message.success(`AI成功生成 ${validKeywords.length} 个关联词`)\n              console.log('✅ [AI关联词] 关联词生成成功:', validKeywords)\n\n              // 关键词生成成功后，扣减套餐次数\n              await this.deductPackageUsage('关键词生成')\n            } else {\n              console.warn('⚠️ [AI关联词] 生成的关联词经过验证后为空，使用降级策略')\n              this.fallbackToDefaultKeywords()\n            }\n          } else {\n            console.warn('⚠️ [AI关联词] API返回数据格式异常，使用降级策略')\n            this.fallbackToDefaultKeywords()\n          }\n        } else {\n          console.error('❌ [AI关联词] API调用失败:', response.msg)\n          this.$message.error('AI生成关联词失败：' + (response.msg || '未知错误'))\n          this.fallbackToDefaultKeywords()\n        }\n\n      } catch (error) {\n        // 关闭加载消息\n        if (loadingMessage) {\n          loadingMessage.close()\n        }\n\n        console.error('💥 [AI关联词] 调用异常:', error)\n\n        // 根据错误类型提供不同的处理\n        if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {\n          this.$message.error('AI服务响应超时，正在使用备用方案生成关联词...')\n        } else if (error.response && error.response.status === 401) {\n          this.$message.error('认证失败，请重新登录后重试')\n          return\n        } else {\n          this.$message.error('AI服务暂时不可用，正在使用备用方案生成关联词...')\n        }\n\n        // 使用降级策略\n        this.fallbackToDefaultKeywords()\n      }\n    },\n\n    // 处理AI返回的关联词数据\n    processAIKeywords(keywords) {\n      console.log('🔧 [关联词处理] 开始处理AI返回的关联词:', keywords)\n\n      const processed = []\n      const seen = new Set()\n\n      keywords.forEach(keyword => {\n        if (typeof keyword === 'string') {\n          const cleaned = keyword.trim()\n\n          // 基本验证：长度、去重、有效性\n          if (cleaned &&\n              cleaned.length >= 2 &&\n              cleaned.length <= 20 &&\n              !seen.has(cleaned) &&\n              this.isValidKeyword(cleaned)) {\n            processed.push(cleaned)\n            seen.add(cleaned)\n          }\n        }\n      })\n\n      console.log('✅ [关联词处理] 处理完成，有效关联词数量:', processed.length)\n      return processed\n    },\n\n    // 验证关联词是否有效\n    isValidKeyword(keyword) {\n      // 过滤无效的关联词\n      const invalidPatterns = [\n        /^\\d+$/, // 纯数字\n        /^[a-zA-Z]+$/, // 纯英文\n        /^[^\\u4e00-\\u9fa5\\w\\s]+$/, // 只包含特殊字符\n        /^(的|了|是|在|有|和|与|或|但|然而|因此|所以)$/ // 常见停用词\n      ]\n\n      return !invalidPatterns.some(pattern => pattern.test(keyword))\n    },\n\n    // 降级策略：使用默认关联词\n    fallbackToDefaultKeywords() {\n      console.log('🔄 [降级策略] 使用默认关联词生成逻辑')\n\n      // 基于实体关键词和需求内容生成基础关联词\n      const entityKeyword = this.entityKeyword.trim()\n      const baseKeywords = [\n        `${entityKeyword} 售后服务`,\n        `${entityKeyword} 客服态度`,\n        `${entityKeyword} 质量问题`,\n        `${entityKeyword} 投诉处理`,\n        `${entityKeyword} 用户体验`,\n        `${entityKeyword} 产品缺陷`,\n        `${entityKeyword} 维修服务`,\n        `${entityKeyword} 退换货`,\n        `${entityKeyword} 品牌形象`,\n        `${entityKeyword} 消费者反馈`\n      ]\n\n      // 保存生成的关键词\n      this.generatedKeywords = [...baseKeywords]\n\n      // 默认选中前几个关键词\n      this.selectedKeywords = []\n      baseKeywords.forEach(word => {\n        if (this.selectedKeywords.length < this.maxKeywords) {\n          this.selectedKeywords.push(word)\n        }\n      })\n\n      this.$message.info('已使用备用方案生成关联词')\n      console.log('✅ [降级策略] 默认关联词生成完成:', baseKeywords)\n    },\n\n    // 处理定时推送按钮点击\n    handleTimedPush() {\n      this.timedTaskDialogVisible = true\n      // 打开定时推送弹窗时加载定时任务列表\n      this.loadTimedTaskList()\n\n      // 启动定时任务状态轮询，定期更新任务列表\n      this.startTimedTaskListPolling()\n    },\n\n    // 启动定时任务列表轮询\n    startTimedTaskListPolling() {\n      this.startHttpPolling('timedTaskList', async () => {\n        const response = await getTimedTaskList()\n        return response.data\n      }, {\n        interval: 10000, // 每10秒轮询一次\n        onSuccess: (data) => {\n          if (data && Array.isArray(data.list)) {\n            // 更新任务列表，保持界面数据最新\n            this.timedTaskList = data.list\n            console.log('定时任务列表已更新:', data.list.length, '个任务')\n          }\n        },\n        onError: (error, attempts) => {\n          console.error('获取定时任务列表失败:', error)\n          // 如果连续失败3次，停止轮询并提示用户\n          if (attempts >= 3) {\n            this.$message.warning('定时任务状态更新失败，请手动刷新')\n            return false // 停止轮询\n          }\n          return true // 继续轮询\n        },\n        shouldStop: () => {\n          // 当定时任务弹窗关闭时停止轮询\n          return !this.timedTaskDialogVisible\n        }\n      })\n    },\n\n    // 关闭定时任务弹窗\n    closeTimedTaskDialog() {\n      this.timedTaskDialogVisible = false\n      // 停止定时任务列表轮询\n      this.stopHttpPolling('timedTaskList')\n    },\n\n    // 处理创建定时任务\n    handleCreateTimedTask() {\n      this.resetTaskForm()\n      this.editingTaskIndex = -1 // 设置为新建模式\n      this.loadRequirementList() // 加载需求列表\n      this.createTaskDialogVisible = true\n    },\n\n    // 处理添加定时任务按钮\n    handleAddTimedTask() {\n      this.resetTaskForm()\n      this.editingTaskIndex = -1 // 设置为新建模式\n      this.loadRequirementList() // 加载需求列表\n      this.createTaskDialogVisible = true\n    },\n\n    // 加载需求列表\n    async loadRequirementList() {\n      try {\n        const response = await getRequirementList({\n          page: 1,\n          size: 100\n        })\n\n        if (response.success) {\n          // 处理分页数据\n          if (response.data && response.data.rows) {\n            this.requirementList = response.data.rows.map(item => ({\n              id: item.id,\n              requirementName: item.requirementName\n            }))\n          } else if (Array.isArray(response.data)) {\n            this.requirementList = response.data.map(item => ({\n              id: item.id,\n              requirementName: item.requirementName\n            }))\n          } else {\n            this.requirementList = []\n          }\n        } else {\n          console.error('获取需求列表失败:', response.msg)\n          this.$message.error(response.msg || '获取需求列表失败')\n          // 使用模拟数据作为后备\n          this.requirementList = [\n            { id: 1, requirementName: '老板电器舆情监控' },\n            { id: 2, requirementName: '品牌声誉分析' },\n            { id: 3, requirementName: '竞品对比分析' },\n            { id: 4, requirementName: '用户反馈监控' }\n          ]\n        }\n      } catch (error) {\n        console.error('加载需求列表失败:', error)\n        this.$message.error('加载需求列表失败')\n        // 使用模拟数据作为后备\n        this.requirementList = [\n          { id: 1, requirementName: '老板电器舆情监控' },\n          { id: 2, requirementName: '品牌声誉分析' },\n          { id: 3, requirementName: '竞品对比分析' },\n          { id: 4, requirementName: '用户反馈监控' }\n        ]\n      }\n    },\n\n    // 加载关键词分类\n    async loadKeywordCategories() {\n      try {\n        const response = await getKeywordCategories()\n\n        if (response.success && Array.isArray(response.data)) {\n          this.keywordCategories = response.data\n          console.log('关键词分类加载成功:', this.keywordCategories)\n        } else {\n          console.error('获取关键词分类失败:', response.msg)\n          // 使用默认分类作为后备\n          this.keywordCategories = []\n        }\n      } catch (error) {\n        console.error('加载关键词分类失败:', error)\n        // 使用默认分类作为后备\n        this.keywordCategories = []\n      }\n    },\n\n    // 加载定时任务列表\n    async loadTimedTaskList() {\n      try {\n        const response = await getTimedTaskList()\n\n        if (response.success) {\n          // 处理分页数据 - 后端返回的是PageResponseModel格式\n          if (response.records && Array.isArray(response.records)) {\n            this.timedTaskList = response.records.map(task => ({\n              id: task.id,\n              requirementId: task.requirementId,\n              name: task.taskName,\n              description: task.taskDescription,\n              executeTime: task.executeTime,\n              frequency: task.frequency,\n              status: task.status === 'running' ? 'running' : 'pending',\n              pushUrl: task.pushUrl\n            }))\n          } else if (response.data && response.data.records && Array.isArray(response.data.records)) {\n            // 兼容嵌套在data中的情况\n            this.timedTaskList = response.data.records.map(task => ({\n              id: task.id,\n              requirementId: task.requirementId,\n              name: task.taskName,\n              description: task.taskDescription,\n              executeTime: task.executeTime,\n              frequency: task.frequency,\n              status: task.status === 'running' ? 'running' : 'pending',\n              pushUrl: task.pushUrl\n            }))\n          }\n          console.log('定时任务列表加载成功:', this.timedTaskList)\n        } else {\n          console.error('获取定时任务列表失败:', response.msg)\n          this.timedTaskList = []\n        }\n      } catch (error) {\n        console.error('加载定时任务列表失败:', error)\n        this.timedTaskList = []\n      }\n    },\n\n    // 关闭创建任务弹窗\n    closeCreateTaskDialog() {\n      this.createTaskDialogVisible = false\n      this.resetTaskForm()\n      this.editingTaskIndex = -1 // 重置编辑状态\n    },\n\n    // 保存并运行任务\n    async saveAndRunTask() {\n      if (!this.validateTaskForm()) {\n        return\n      }\n\n      try {\n        if (this.editingTaskIndex === -1) {\n          // 创建新任务\n          const taskData = {\n            requirement_id: this.taskForm.requirementId,\n            task_name: this.taskForm.name,\n            task_type: 'scheduled',\n            schedule_type: this.taskForm.frequency,\n            schedule_config: {\n              execute_time: this.taskForm.executeTime\n            },\n            task_description: this.taskForm.description,\n            push_url: this.taskForm.pushUrl,\n            priority: 'medium'\n          }\n\n          const response = await createTimedTask(taskData)\n          if (response.success) {\n            this.$message.success('任务已保存并开始运行')\n            // 重新加载任务列表\n            await this.loadTimedTaskList()\n            // 更新任务状态为运行中\n            if (response.data && response.data.id) {\n              await updateTaskStatus(response.data.id, 'running')\n              await this.loadTimedTaskList()\n            }\n          } else {\n            this.$message.error('创建任务失败：' + response.msg)\n            return\n          }\n        } else {\n          // 更新现有任务\n          const task = this.timedTaskList[this.editingTaskIndex]\n          const taskData = {\n            task_name: this.taskForm.name,\n            task_description: this.taskForm.description,\n            schedule_config: {\n              execute_time: this.taskForm.executeTime\n            },\n            schedule_type: this.taskForm.frequency,\n            push_url: this.taskForm.pushUrl\n          }\n\n          const response = await updateTimedTask(task.id, taskData)\n          if (response.success) {\n            // 更新任务状态为运行中\n            await updateTaskStatus(task.id, 'running')\n            this.$message.success('任务已更新并开始运行')\n            // 重新加载任务列表\n            await this.loadTimedTaskList()\n          } else {\n            this.$message.error('更新任务失败：' + response.msg)\n            return\n          }\n        }\n\n        this.createTaskDialogVisible = false\n        this.resetTaskForm()\n        this.editingTaskIndex = -1 // 重置编辑状态\n      } catch (error) {\n        console.error('保存任务失败:', error)\n        this.$message.error('保存任务失败，请重试')\n      }\n    },\n\n    // 保存任务计划\n    async saveTaskPlan() {\n      if (!this.validateTaskForm()) {\n        return\n      }\n\n      try {\n        if (this.editingTaskIndex === -1) {\n          // 创建新任务\n          const taskData = {\n            requirement_id: this.taskForm.requirementId,\n            task_name: this.taskForm.name,\n            task_type: 'scheduled',\n            schedule_type: this.taskForm.frequency,\n            schedule_config: {\n              execute_time: this.taskForm.executeTime\n            },\n            task_description: this.taskForm.description,\n            push_url: this.taskForm.pushUrl,\n            priority: 'medium'\n          }\n\n          const response = await createTimedTask(taskData)\n          if (response.success) {\n            this.$message.success('任务计划已保存')\n            // 重新加载任务列表\n            await this.loadTimedTaskList()\n          } else {\n            this.$message.error('保存任务计划失败：' + response.msg)\n            return\n          }\n        } else {\n          // 更新现有任务\n          const task = this.timedTaskList[this.editingTaskIndex]\n          const taskData = {\n            task_name: this.taskForm.name,\n            task_description: this.taskForm.description,\n            schedule_config: {\n              execute_time: this.taskForm.executeTime\n            },\n            schedule_type: this.taskForm.frequency,\n            push_url: this.taskForm.pushUrl\n          }\n\n          const response = await updateTimedTask(task.id, taskData)\n          if (response.success) {\n            this.$message.success('任务计划已更新')\n            // 重新加载任务列表\n            await this.loadTimedTaskList()\n          } else {\n            this.$message.error('更新任务计划失败：' + response.msg)\n            return\n          }\n        }\n\n        this.createTaskDialogVisible = false\n        this.resetTaskForm()\n        this.editingTaskIndex = -1 // 重置编辑状态\n      } catch (error) {\n        console.error('保存任务计划失败:', error)\n        this.$message.error('保存任务计划失败，请重试')\n      }\n    },\n\n    // 修改计划\n    modifyPlan() {\n      this.$message.info('修改计划功能开发中...')\n      // TODO: 实现修改计划逻辑\n    },\n\n    // 验证任务表单\n    validateTaskForm() {\n      if (!this.taskForm.requirementId) {\n        this.$message.warning('请选择需求名称')\n        return false\n      }\n\n      if (!this.taskForm.name.trim()) {\n        this.$message.warning('请输入任务名称')\n        return false\n      }\n\n      if (!this.taskForm.description.trim()) {\n        this.$message.warning('请输入任务描述')\n        return false\n      }\n\n      // 验证执行时间\n      if (this.taskForm.frequency === 'once') {\n        if (!this.taskForm.executeDateTime) {\n          this.$message.warning('请选择执行日期和时间')\n          return false\n        }\n        // 检查是否是未来时间\n        const executeTime = new Date(this.taskForm.executeDateTime)\n        if (executeTime <= new Date()) {\n          this.$message.warning('执行时间必须是未来时间')\n          return false\n        }\n      } else {\n        if (!this.taskForm.executeTime) {\n          this.$message.warning('请选择执行时间')\n          return false\n        }\n      }\n\n      return true\n    },\n\n    // 重置任务表单\n    resetTaskForm() {\n      this.taskForm = {\n        requirementId: '',\n        name: '',\n        description: '',\n        executeTime: '16:00',\n        executeDateTime: '',\n        frequency: 'daily',\n        pushUrl: ''\n      }\n    },\n\n    // 编辑任务\n    editTask(index) {\n      const task = this.timedTaskList[index]\n\n      // 将任务数据填充到表单中\n      this.taskForm = {\n        requirementId: task.requirementId || '',\n        name: task.name,\n        description: task.description,\n        executeTime: task.executeTime,\n        executeDateTime: task.executeDateTime || '',\n        frequency: task.frequency,\n        pushUrl: task.pushUrl || ''\n      }\n\n      // 加载需求列表\n      this.loadRequirementList()\n\n      // 打开编辑弹窗\n      this.createTaskDialogVisible = true\n\n      // 保存当前编辑的任务索引，用于后续更新\n      this.editingTaskIndex = index\n    },\n\n    // 删除任务\n    deleteTask(index) {\n      this.$confirm('确定要删除该任务吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(async () => {\n        try {\n          const task = this.timedTaskList[index]\n          const response = await deleteTimedTask(task.id)\n\n          if (response.success) {\n            this.$message.success('删除成功')\n            // 重新加载任务列表\n            await this.loadTimedTaskList()\n          } else {\n            this.$message.error('删除失败：' + response.msg)\n          }\n        } catch (error) {\n          console.error('删除任务失败:', error)\n          this.$message.error('删除任务失败，请重试')\n        }\n      }).catch(() => {\n        // 取消删除操作\n      })\n    },\n\n    // 预览任务详情\n    previewTask(index) {\n      const task = this.timedTaskList[index]\n      this.taskPreviewDialog.taskData = { ...task }\n      this.taskPreviewDialog.visible = true\n    },\n\n    // 隐藏任务预览弹窗\n    hideTaskPreviewDialog() {\n      this.taskPreviewDialog.visible = false\n      this.taskPreviewDialog.taskData = null\n    },\n\n    // 从预览弹窗编辑任务\n    editTaskFromPreview() {\n      if (this.taskPreviewDialog.taskData) {\n        // 找到任务在列表中的索引\n        const taskIndex = this.timedTaskList.findIndex(task =>\n          task.id === this.taskPreviewDialog.taskData.id\n        )\n        if (taskIndex !== -1) {\n          this.hideTaskPreviewDialog()\n          this.editTask(taskIndex)\n        }\n      }\n    },\n\n    // 获取频率文本\n    getFrequencyText(frequency) {\n      const frequencyMap = {\n        'once': '仅一次',\n        'daily': '每天',\n        'weekly': '每周',\n        'monthly': '每月'\n      }\n      return frequencyMap[frequency] || frequency\n    },\n\n    // 获取任务调度文本\n    getTaskScheduleText(task) {\n      if (task.frequency === 'once') {\n        // 一次性任务显示具体执行时间\n        return `仅一次 ${task.executeDateTime || task.executeTime}`\n      } else {\n        // 周期性任务显示频率和时间\n        const frequencyText = this.getFrequencyText(task.frequency)\n        return `${frequencyText} ${task.executeTime}`\n      }\n    },\n\n    // 获取推送类型文本\n    getPushTypeText(url) {\n      if (!url) return '未配置'\n\n      if (url.includes('oapi.dingtalk.com/robot/send')) {\n        return '钉钉机器人'\n      } else if (url.includes('qyapi.weixin.qq.com/cgi-bin/webhook/send')) {\n        return '企业微信机器人'\n      } else if (url.includes('open.feishu.cn/open-apis/bot/v2/hook/')) {\n        return '飞书机器人'\n      } else if (url.includes('localhost') || url.includes('127.0.0.1')) {\n        return '本地接口'\n      } else {\n        return 'HTTP接口'\n      }\n    },\n\n    // 获取脱敏的URL显示\n    getMaskedUrl(url) {\n      if (!url) return ''\n\n      try {\n        const urlObj = new URL(url)\n        const domain = urlObj.hostname\n        const path = urlObj.pathname\n\n        // 对于机器人URL，隐藏token部分\n        if (url.includes('access_token=') || url.includes('key=')) {\n          return `${urlObj.protocol}//${domain}${path}?***`\n        }\n\n        // 对于普通URL，显示域名和路径\n        return `${urlObj.protocol}//${domain}${path}`\n      } catch (error) {\n        // 如果URL解析失败，显示前50个字符\n        return url.length > 50 ? url.substring(0, 50) + '...' : url\n      }\n    },\n\n    // 复制到剪贴板\n    copyToClipboard(text) {\n      const textArea = document.createElement('textarea')\n      textArea.value = text\n      textArea.style.position = 'fixed'\n      textArea.style.opacity = '0'\n      document.body.appendChild(textArea)\n      textArea.select()\n      try {\n        document.execCommand('copy')\n        this.$message.success('地址已复制到剪贴板')\n      } catch (err) {\n        this.$message.error('复制失败，请手动复制')\n      } finally {\n        document.body.removeChild(textArea)\n      }\n    },\n\n    // 预览任务详情\n    previewTask(index) {\n      const task = this.timedTaskList[index]\n      this.taskPreviewDialog.taskData = { ...task }\n      this.taskPreviewDialog.visible = true\n    },\n\n    // 隐藏任务预览弹窗\n    hideTaskPreviewDialog() {\n      this.taskPreviewDialog.visible = false\n      this.taskPreviewDialog.taskData = null\n    },\n\n    // 从预览弹窗编辑任务\n    editTaskFromPreview() {\n      if (this.taskPreviewDialog.taskData) {\n        // 找到任务在列表中的索引\n        const taskIndex = this.timedTaskList.findIndex(task =>\n          task.id === this.taskPreviewDialog.taskData.id\n        )\n        if (taskIndex !== -1) {\n          this.hideTaskPreviewDialog()\n          this.editTask(taskIndex)\n        }\n      }\n    },\n\n    // 获取频率文本\n    getFrequencyText(frequency) {\n      const frequencyMap = {\n        'daily': '每天',\n        'weekly': '每周',\n        'monthly': '每月'\n      }\n      return frequencyMap[frequency] || frequency\n    },\n\n    // 获取推送类型文本\n    getPushTypeText(url) {\n      if (!url) return '未配置'\n\n      if (url.includes('oapi.dingtalk.com/robot/send')) {\n        return '钉钉机器人'\n      } else if (url.includes('qyapi.weixin.qq.com/cgi-bin/webhook/send')) {\n        return '企业微信机器人'\n      } else if (url.includes('open.feishu.cn/open-apis/bot/v2/hook/')) {\n        return '飞书机器人'\n      } else if (url.includes('localhost') || url.includes('127.0.0.1')) {\n        return '本地接口'\n      } else {\n        return 'HTTP接口'\n      }\n    },\n\n    // 获取脱敏的URL显示\n    getMaskedUrl(url) {\n      if (!url) return ''\n\n      try {\n        const urlObj = new URL(url)\n        const domain = urlObj.hostname\n        const path = urlObj.pathname\n\n        // 对于机器人URL，隐藏token部分\n        if (url.includes('access_token=') || url.includes('key=')) {\n          return `${urlObj.protocol}//${domain}${path}?***`\n        }\n\n        // 对于普通URL，显示域名和路径\n        return `${urlObj.protocol}//${domain}${path}`\n      } catch (error) {\n        // 如果URL解析失败，显示前50个字符\n        return url.length > 50 ? url.substring(0, 50) + '...' : url\n      }\n    },\n\n    // 复制到剪贴板\n    copyToClipboard(text) {\n      const textArea = document.createElement('textarea')\n      textArea.value = text\n      textArea.style.position = 'fixed'\n      textArea.style.opacity = '0'\n      document.body.appendChild(textArea)\n      textArea.select()\n      try {\n        document.execCommand('copy')\n        this.$message.success('地址已复制到剪贴板')\n      } catch (err) {\n        this.$message.error('复制失败，请手动复制')\n      } finally {\n        document.body.removeChild(textArea)\n      }\n    },\n\n    // 切换任务状态\n    async toggleTaskStatus(index) {\n      try {\n        const task = this.timedTaskList[index]\n        const newStatus = task.status === 'running' ? 'pending' : 'running'\n\n        const response = await updateTaskStatus(task.id, newStatus)\n\n        if (response.success) {\n          // 显示状态变更提示\n          if (newStatus === 'running') {\n            this.$message.success(`任务「${task.name}」已启动`)\n          } else {\n            this.$message.info(`任务「${task.name}」已暂停`)\n          }\n          // 重新加载任务列表\n          await this.loadTimedTaskList()\n        } else {\n          this.$message.error('状态更新失败：' + response.msg)\n        }\n      } catch (error) {\n        console.error('切换任务状态失败:', error)\n        this.$message.error('状态更新失败，请重试')\n      }\n    },\n\n    // 更新报告数据\n    updateReportData(analysisData) {\n      try {\n        console.log('开始更新报告数据，原始数据:', analysisData)\n\n        // 保存完整的分析结果\n        this.analysisResults = analysisData\n\n        // 重置报告数据\n        this.reportData = {\n          totalArticles: 0,\n          totalKeywords: this.selectedKeywords.length,\n          dataSources: (this.enableOnlineSearch ? 1 : 0) + this.customDataSources.length,\n          sentiment: {\n            positive: 0,\n            neutral: 0,\n            negative: 0\n          },\n          onlineSearchCount: 0,\n          customSourceCounts: {}\n        }\n\n        // 重置文章列表状态\n        this.articleListState.currentPage = 1\n        this.articleListState.searchKeyword = ''\n        this.articleListState.selectedSource = ''\n        this.articleListState.selectedSentiment = ''\n        this.articleListState.expandedArticles.clear()\n\n        // 处理分析结果\n        if (analysisData.analysis_results) {\n          const results = analysisData.analysis_results\n\n          // 处理联网搜索结果\n          if (results.online_search && results.online_search.data) {\n            const onlineData = results.online_search.data\n            console.log('联网搜索数据:', onlineData)\n\n            if (onlineData.articles && Array.isArray(onlineData.articles)) {\n              this.reportData.totalArticles += onlineData.articles.length\n              this.reportData.onlineSearchCount = onlineData.articles.length\n\n              console.log(`联网搜索获取到 ${onlineData.articles.length} 篇文章`)\n            }\n\n            // 更新情感分析数据\n            if (onlineData.sentiment_analysis) {\n              console.log('联网搜索情感分析数据:', onlineData.sentiment_analysis)\n              this.reportData.sentiment = {\n                positive: onlineData.sentiment_analysis.positive || 0,\n                neutral: onlineData.sentiment_analysis.neutral || 0,\n                negative: onlineData.sentiment_analysis.negative || 0\n              }\n            } else {\n              // 如果没有整体情感分析，从文章中计算\n              if (onlineData.articles && Array.isArray(onlineData.articles)) {\n                this.reportData.sentiment = this.calculateSentimentFromArticles(onlineData.articles)\n              }\n            }\n          }\n\n          // 处理自定义数据源结果\n          if (results.custom_data_source && results.custom_data_source.data) {\n            const customData = results.custom_data_source.data\n            console.log('自定义数据源数据:', customData)\n\n            if (customData.articles && Array.isArray(customData.articles)) {\n              this.reportData.totalArticles += customData.articles.length\n\n              // 统计各数据源的文章数量\n              customData.articles.forEach(article => {\n                const source = article.source || '未知来源'\n                this.reportData.customSourceCounts[source] = (this.reportData.customSourceCounts[source] || 0) + 1\n              })\n\n              // 合并情感分析结果\n              const customSentiment = this.calculateSentimentFromArticles(customData.articles)\n              this.reportData.sentiment = this.mergeSentimentData(this.reportData.sentiment, customSentiment, customData.articles.length)\n            }\n          }\n        }\n\n        console.log('报告数据更新完成:', this.reportData)\n      } catch (error) {\n        console.error('更新报告数据失败:', error)\n        this.$message.error('报告数据更新失败，请重试')\n      }\n    },\n\n    // 格式化分析时间\n    formatAnalysisTime(date) {\n      const now = new Date(date)\n      return now.toLocaleString('zh-CN', {\n        year: 'numeric',\n        month: '2-digit',\n        day: '2-digit',\n        hour: '2-digit',\n        minute: '2-digit'\n      })\n    },\n\n    // 从文章列表计算情感分析百分比\n    calculateSentimentFromArticles(articles) {\n      if (!articles || !Array.isArray(articles) || articles.length === 0) {\n        return { positive: 0, neutral: 0, negative: 0 }\n      }\n\n      const sentimentCounts = { positive: 0, neutral: 0, negative: 0 }\n\n      articles.forEach(article => {\n        const sentiment = article.sentiment || 'neutral'\n        if (sentimentCounts.hasOwnProperty(sentiment)) {\n          sentimentCounts[sentiment]++\n        } else {\n          sentimentCounts.neutral++\n        }\n      })\n\n      const total = articles.length\n      return {\n        positive: Math.round((sentimentCounts.positive / total) * 100),\n        neutral: Math.round((sentimentCounts.neutral / total) * 100),\n        negative: Math.round((sentimentCounts.negative / total) * 100)\n      }\n    },\n\n    // 合并多个数据源的情感分析结果\n    mergeSentimentData(sentiment1, sentiment2, weight2) {\n      const total1 = this.reportData.onlineSearchCount || 0\n      const total2 = weight2 || 0\n      const totalArticles = total1 + total2\n\n      if (totalArticles === 0) {\n        return { positive: 0, neutral: 0, negative: 0 }\n      }\n\n      return {\n        positive: Math.round(((sentiment1.positive * total1) + (sentiment2.positive * total2)) / totalArticles),\n        neutral: Math.round(((sentiment1.neutral * total1) + (sentiment2.neutral * total2)) / totalArticles),\n        negative: Math.round(((sentiment1.negative * total1) + (sentiment2.negative * total2)) / totalArticles)\n      }\n    },\n\n    // 文章展开/收起切换\n    toggleArticleExpand(index) {\n      const articleKey = `page-${this.articleListState.currentPage}-item-${index}`\n      if (this.articleListState.expandedArticles.has(articleKey)) {\n        this.articleListState.expandedArticles.delete(articleKey)\n      } else {\n        this.articleListState.expandedArticles.add(articleKey)\n      }\n    },\n\n    // 检查文章是否已展开\n    isArticleExpanded(index) {\n      const articleKey = `page-${this.articleListState.currentPage}-item-${index}`\n      return this.articleListState.expandedArticles.has(articleKey)\n    },\n\n    // 获取内容摘要（前200字符）\n    getContentSummary(content) {\n      if (!content) return '暂无内容'\n      return content.length > 200 ? content.substring(0, 200) + '...' : content\n    },\n\n    // 获取情感标签文本\n    getSentimentLabel(sentiment) {\n      const labels = {\n        positive: '正面',\n        neutral: '中性',\n        negative: '负面'\n      }\n      return labels[sentiment] || '未知'\n    },\n\n    // 格式化发布时间\n    formatPublishTime(publishTime) {\n      if (!publishTime) return '未知时间'\n      try {\n        const date = new Date(publishTime)\n        return date.toLocaleString('zh-CN', {\n          year: 'numeric',\n          month: '2-digit',\n          day: '2-digit',\n          hour: '2-digit',\n          minute: '2-digit'\n        })\n      } catch (error) {\n        return publishTime\n      }\n    },\n\n    // 复制文章内容\n    copyArticleContent(article) {\n      const content = `标题：${article.title}\\n来源：${article.source}\\n时间：${this.formatPublishTime(article.publish_time)}\\n情感：${this.getSentimentLabel(article.sentiment)}\\n内容：${article.content}`\n\n      if (navigator.clipboard) {\n        navigator.clipboard.writeText(content).then(() => {\n          this.$message.success('文章内容已复制到剪贴板')\n        }).catch(() => {\n          this.fallbackCopyText(content)\n        })\n      } else {\n        this.fallbackCopyText(content)\n      }\n    },\n\n    // 备用复制方法\n    fallbackCopyText(text) {\n      const textArea = document.createElement('textarea')\n      textArea.value = text\n      document.body.appendChild(textArea)\n      textArea.select()\n      try {\n        document.execCommand('copy')\n        this.$message.success('文章内容已复制到剪贴板')\n      } catch (err) {\n        this.$message.error('复制失败，请手动复制')\n      }\n      document.body.removeChild(textArea)\n    },\n\n    // 获取URL提示信息\n    getUrlTooltip(url) {\n      if (!url) return ''\n\n      try {\n        const urlObj = new URL(url)\n        return `访问 ${urlObj.hostname}`\n      } catch (error) {\n        return `访问链接: ${url}`\n      }\n    },\n\n    // 打开原文链接 - 直接使用AI返回的原始URL\n    openArticleUrl(url) {\n      if (url && url.trim()) {\n        window.open(url, '_blank')\n        this.$message.success('正在打开原文链接...')\n      } else {\n        this.$message.warning('暂无原文链接')\n      }\n    },\n\n    // 处理分页变化\n    handlePageChange(page) {\n      this.articleListState.currentPage = page\n      // 清空当前页的展开状态\n      const keysToDelete = []\n      this.articleListState.expandedArticles.forEach(key => {\n        if (key.startsWith(`page-${page}-`)) {\n          keysToDelete.push(key)\n        }\n      })\n      keysToDelete.forEach(key => this.articleListState.expandedArticles.delete(key))\n    },\n\n    // 重置需求保存状态\n    resetRequirementSaveStatus() {\n      // 如果需求已保存，标记为已修改而不是重置ID\n      if (this.requirementSaved && this.currentRequirementId) {\n        this.requirementModified = true\n        console.log('需求信息已变更，标记为需要更新')\n      } else if (!this.currentRequirementId) {\n        // 只有在没有需求ID时才重置状态\n        this.requirementSaved = false\n        this.requirementModified = false\n        console.log('需求信息已变更，重置保存状态')\n      }\n    },\n\n    // 确保需求存在（恢复或创建）\n    async ensureRequirementExists() {\n      try {\n        // 如果需求信息不完整，无法创建\n        if (!this.requirementName.trim() || !this.entityKeyword.trim() || !this.specificRequirement.trim()) {\n          console.log('需求信息不完整，无法创建需求')\n          return false\n        }\n\n        // 尝试根据需求名称查找已存在的需求\n        try {\n          const response = await getRequirementList({\n            requirement_name: this.requirementName,\n            page: 1,\n            page_size: 1\n          })\n\n          if (response.success && response.data && response.data.rows && response.data.rows.length > 0) {\n            const existingRequirement = response.data.rows[0]\n            this.currentRequirementId = existingRequirement.id\n            this.requirementSaved = true\n            this.requirementModified = false\n            console.log('找到已存在的需求，ID:', this.currentRequirementId)\n            return true\n          }\n        } catch (error) {\n          console.warn('查找已存在需求失败:', error)\n        }\n\n        // 如果没找到，创建新需求\n        console.log('未找到已存在需求，创建新需求...')\n        await this.createTemporaryRequirement()\n        return !!this.currentRequirementId\n      } catch (error) {\n        console.error('确保需求存在失败:', error)\n        return false\n      }\n    },\n\n    // 保存推送任务到数据库（带重复检查）\n    async saveTaskForPush(pushUrl, taskType = 'immediate') {\n      try {\n        // 检查是否有需求ID，如果没有则尝试恢复\n        if (!this.currentRequirementId) {\n          console.log('当前没有需求ID，尝试恢复或创建需求...')\n          await this.ensureRequirementExists()\n\n          // 如果仍然没有需求ID，抛出错误\n          if (!this.currentRequirementId) {\n            throw new Error('无法获取或创建需求信息，请重新填写需求')\n          }\n        }\n\n        // 第一步：检查任务是否已存在\n        console.log('检查任务是否已存在...')\n\n        // 对于查看报告的保存操作（没有push_url），跳过重复检查，直接创建任务\n        if (!pushUrl) {\n          console.log('查看报告任务，跳过重复检查')\n        } else {\n          // 只对推送类型的任务进行重复检查\n          const checkParams = {\n            requirement_id: this.currentRequirementId,\n            task_type: taskType,\n            push_url: pushUrl\n          }\n\n          try {\n            const checkResponse = await checkTaskExists(checkParams)\n            console.log('任务存在性检查结果:', checkResponse)\n\n            if (checkResponse.success && checkResponse.data.exists) {\n              // 任务已存在，跳过保存\n              console.log('任务已存在，跳过保存步骤')\n              return {\n                success: true,\n                taskId: checkResponse.data.task_id,\n                exists: true,\n                existingTask: {\n                  id: checkResponse.data.task_id,\n                  name: checkResponse.data.task_name,\n                  createTime: checkResponse.data.create_time\n                },\n                message: '任务已存在，直接推送'\n              }\n            }\n          } catch (error) {\n            console.log('任务存在性检查失败，继续创建新任务:', error.message)\n            // 如果检查失败，继续创建新任务\n          }\n        }\n\n        // 第二步：任务不存在，创建新任务\n        console.log('任务不存在，创建新任务...')\n\n        // 根据任务类型设置不同的任务名称和描述\n        let taskName, taskDescription\n        if (taskType === 'immediate') {\n          // 如果没有push_url，说明是查看报告的保存操作\n          if (!pushUrl) {\n            taskName = `${this.requirementName} - 分析报告`\n            taskDescription = `舆情分析报告查看任务`\n          } else {\n            taskName = `${this.requirementName} - 立即推送`\n            taskDescription = `舆情分析报告推送任务`\n          }\n        } else {\n          taskName = `${this.requirementName} - 推送计划`\n          taskDescription = `舆情分析报告推送任务`\n        }\n\n        const taskData = {\n          requirement_id: this.currentRequirementId,\n          task_name: taskName,\n          task_type: taskType,\n          task_description: taskDescription,\n          push_url: pushUrl || '', // report_view类型不需要push_url\n          priority: 'high',\n          schedule_type: taskType === 'immediate' ? 'once' : 'manual',\n          schedule_config: taskType === 'immediate' ?\n            { execute_time: new Date().toISOString() } :\n            { execute_time: null },\n          report_oss_url: this.reportOssUrl || null // 包含报告OSS URL\n        }\n\n        console.log('保存推送任务:', taskData)\n        const response = await createTimedTask(taskData)\n\n        if (response.success) {\n          console.log('推送任务保存成功，ID:', response.data.id)\n          return {\n            success: true,\n            taskId: response.data.id,\n            exists: false,\n            message: '任务创建成功'\n          }\n        } else {\n          throw new Error(response.msg || '任务保存失败')\n        }\n      } catch (error) {\n        console.error('保存推送任务失败:', error)\n        throw error\n      }\n    },\n\n\n\n    // 显示推送弹窗\n    showPushDialog() {\n      this.pushReportDialog.visible = true\n      this.pushReportDialog.url = ''\n      this.pushReportDialog.loading = false\n    },\n\n    // 隐藏推送弹窗\n    hidePushDialog() {\n      this.pushReportDialog.visible = false\n      this.pushReportDialog.url = ''\n      this.pushReportDialog.loading = false\n    },\n\n    // 验证推送URL格式（宽松验证，支持所有地址）\n    validatePushUrl(url) {\n      if (!url || !url.trim()) {\n        this.$message.error('请输入推送目标URL地址')\n        return false\n      }\n\n      let fullUrl = url.trim()\n\n      // 智能协议补全\n      if (!fullUrl.match(/^[a-zA-Z][a-zA-Z0-9+.-]*:/)) {\n        // 如果没有协议，根据地址特征智能添加\n        if (fullUrl.includes('localhost') || fullUrl.match(/^\\d+\\.\\d+\\.\\d+\\.\\d+/) || fullUrl.startsWith('127.0.0.1')) {\n          // 本地地址默认使用http\n          fullUrl = 'http://' + fullUrl\n        } else {\n          // 其他地址默认使用https\n          fullUrl = 'https://' + fullUrl\n        }\n      }\n\n      // 宽松的URL格式验证\n      try {\n        // 基本格式检查\n        if (fullUrl.includes(' ')) {\n          this.$message.error('URL地址不能包含空格')\n          return false\n        }\n\n        // 尝试创建URL对象进行基本验证\n        new URL(fullUrl)\n\n        // 特殊地址格式提示\n        if (fullUrl.includes('oapi.dingtalk.com/robot/send')) {\n          console.log('检测到钉钉机器人URL，将使用钉钉消息格式')\n          if (!fullUrl.includes('access_token=')) {\n            console.warn('钉钉机器人URL建议包含access_token参数')\n          }\n        } else if (fullUrl.includes('qyapi.weixin.qq.com/cgi-bin/webhook/send')) {\n          console.log('检测到企业微信机器人URL')\n        } else if (fullUrl.includes('open.feishu.cn/open-apis/bot/v2/hook/')) {\n          console.log('检测到飞书机器人URL')\n        }\n\n        return fullUrl\n      } catch (error) {\n        // 如果URL对象创建失败，进行更宽松的检查\n        console.warn('URL格式验证失败，尝试宽松验证:', error)\n\n        // 检查是否包含基本的URL结构\n        if (fullUrl.includes('://') && fullUrl.length > 10) {\n          console.log('使用宽松验证通过URL:', fullUrl)\n          return fullUrl\n        }\n\n        this.$message.error('URL格式可能不正确，但仍将尝试推送。如果推送失败，请检查URL格式')\n        return fullUrl\n      }\n    },\n\n    // 直接推送报告\n    async directPushReport() {\n      try {\n        // 验证URL\n        const validUrl = this.validatePushUrl(this.pushReportDialog.url)\n        if (!validUrl) {\n          return\n        }\n\n        // 检查是否有报告数据\n        if (!this.reportData || this.reportData.totalArticles === 0) {\n          this.$message.error('暂无报告数据可推送，请先完成分析')\n          return\n        }\n\n        // 设置loading状态\n        this.pushReportDialog.loading = true\n\n        // 移除任务保存逻辑，只执行推送功能\n        console.log('开始推送报告，不保存任务到数据库')\n\n        // 生成报告页面链接\n        const reportPageUrl = this.generateReportPageUrl()\n\n        // 准备推送数据\n        const pushData = {\n          reportData: {\n            ...this.reportData,\n            requirementName: this.requirementName,\n            entityKeyword: this.entityKeyword,\n            specificRequirement: this.specificRequirement,\n            selectedKeywords: this.selectedKeywords,\n            reportPageUrl: reportPageUrl, // 添加报告页面链接\n            pushTime: new Date().toISOString()\n          },\n          analysisResults: this.analysisResults,\n          requirementInfo: {\n            name: this.requirementName,\n            entityKeyword: this.entityKeyword,\n            specificRequirement: this.specificRequirement,\n            selectedKeywords: this.selectedKeywords\n          }\n        }\n\n        console.log('开始推送报告到:', validUrl)\n        console.log('推送数据:', pushData)\n\n        // 调用后端推送API\n        const pushRequestData = {\n          target_url: validUrl,\n          report_data: pushData.reportData,\n          analysis_results: pushData.analysisResults,\n          requirement_id: this.currentRequirementId,\n          push_type: 'immediate'\n        }\n\n        console.log('调用后端推送API:', pushRequestData)\n        const response = await pushReport(pushRequestData)\n\n        console.log('推送API响应:', response)\n\n        // 验证推送结果\n        if (response.success && response.data && response.data.success) {\n          // 推送成功\n          const pushResult = response.data\n          console.log('推送成功，推送ID:', pushResult.push_id)\n\n          // 显示详细成功信息\n          this.$message.success(pushResult.message || '报告推送成功！')\n\n          // 如果有响应状态码，也显示\n          if (pushResult.response_status) {\n            console.log(`目标服务器响应状态: ${pushResult.response_status}`)\n          }\n        } else {\n          // 推送失败\n          const errorMsg = response.data?.error_details || response.msg || '推送失败'\n          throw new Error(errorMsg)\n        }\n\n        // 关闭弹窗\n        this.hidePushDialog()\n\n      } catch (error) {\n        console.error('推送报告失败:', error)\n\n        // 错误处理\n        if (error.message && error.message.includes('timeout')) {\n          this.$message.error('推送超时，请检查目标URL是否可访问')\n        } else if (error.response && error.response.status) {\n          this.$message.error(`推送失败：${error.response.status} ${error.response.statusText || ''}`)\n        } else if (error.message) {\n          this.$message.error('推送失败：' + error.message)\n        } else {\n          this.$message.error('推送失败，请重试')\n        }\n      } finally {\n        this.pushReportDialog.loading = false\n      }\n    },\n\n    // 保存推送计划\n    async savePushPlan() {\n      try {\n        // 验证URL\n        const validUrl = this.validatePushUrl(this.pushReportDialog.url)\n        if (!validUrl) {\n          return\n        }\n\n        // 保存推送计划任务到数据库（带重复检查）\n        try {\n          const taskResult = await this.saveTaskForPush(validUrl, 'scheduled')\n          if (!taskResult.success) {\n            this.$message.error('推送计划保存失败')\n            return\n          }\n\n          // 处理任务存在性检查结果\n          if (taskResult.exists) {\n            console.log('推送计划任务已存在:', taskResult.existingTask)\n            this.$message.info(`推送计划已存在（${taskResult.existingTask.name}）`)\n          } else {\n            console.log('推送计划任务保存成功，任务ID:', taskResult.taskId)\n            this.$message.success('推送计划保存成功！')\n          }\n        } catch (error) {\n          this.$message.error('推送计划保存失败：' + error.message)\n          return\n        }\n\n        // 关闭弹窗\n        this.hidePushDialog()\n\n      } catch (error) {\n        console.error('保存推送计划失败:', error)\n        this.$message.error('保存推送计划失败：' + error.message)\n      }\n    },\n\n    // 生成报告页面链接\n    generateReportPageUrl() {\n      try {\n        // 获取当前页面的基础URL\n        const baseUrl = window.location.origin\n        const currentPath = window.location.pathname\n\n        // 构建报告页面链接，包含当前的分析参数\n        const reportParams = new URLSearchParams({\n          step: '3', // 直接跳转到第三步报告预览\n          requirementId: this.currentRequirementId || '', // 添加需求ID用于获取真实数据\n          requirementName: this.requirementName || '',\n          entityKeyword: this.entityKeyword || '',\n          specificRequirement: this.specificRequirement || '',\n          selectedKeywords: JSON.stringify(this.selectedKeywords || []),\n          timestamp: Date.now() // 添加时间戳确保链接唯一性\n        })\n\n        const reportUrl = `${baseUrl}${currentPath}?${reportParams.toString()}`\n        console.log('生成的报告页面链接:', reportUrl)\n\n        return reportUrl\n      } catch (error) {\n        console.error('生成报告页面链接失败:', error)\n        // 如果生成失败，返回当前页面链接\n        return window.location.href\n      }\n    },\n\n    // 解析URL参数\n    parseUrlParams() {\n      try {\n        const urlParams = new URLSearchParams(window.location.search)\n\n        // 检查是否有报告相关参数\n        const step = urlParams.get('step')\n        const requirementId = urlParams.get('requirementId')\n        const requirementName = urlParams.get('requirementName')\n        const entityKeyword = urlParams.get('entityKeyword')\n        const specificRequirement = urlParams.get('specificRequirement')\n        const selectedKeywords = urlParams.get('selectedKeywords')\n\n        if (step && (requirementId || (requirementName && entityKeyword))) {\n          console.log('检测到报告链接参数，自动加载报告')\n\n          // 设置表单数据\n          if (requirementId) {\n            this.currentRequirementId = parseInt(requirementId)\n          }\n          this.requirementName = requirementName || ''\n          this.entityKeyword = entityKeyword || ''\n          this.specificRequirement = specificRequirement || ''\n\n          // 解析选中的关键词\n          if (selectedKeywords) {\n            try {\n              this.selectedKeywords = JSON.parse(selectedKeywords)\n            } catch (e) {\n              console.warn('解析关键词参数失败:', e)\n              this.selectedKeywords = []\n            }\n          }\n\n          // 跳转到指定步骤\n          const targetStep = parseInt(step)\n          if (targetStep >= 1 && targetStep <= 3) {\n            this.currentStep = targetStep\n\n            // 如果是第三步，显示提示信息\n            if (targetStep === 3) {\n              this.$message.info('正在加载报告预览，请稍候...')\n              // 这里可以添加模拟的报告数据或者重新执行分析\n              this.loadReportFromParams()\n            }\n          }\n        }\n      } catch (error) {\n        console.error('解析URL参数失败:', error)\n      }\n    },\n\n    // 从URL参数加载报告数据\n    async loadReportFromParams() {\n      try {\n        // 检查是否有需求ID参数\n        const urlParams = new URLSearchParams(window.location.search)\n        const requirementId = urlParams.get('requirementId')\n\n        if (requirementId) {\n          // 调用公开API获取报告数据\n          const response = await getPublicReportData(requirementId)\n\n          if (response.success && response.data) {\n            const data = response.data\n\n            // 更新基础信息\n            this.requirementName = data.requirement_name || ''\n            this.entityKeyword = data.entity_keyword || ''\n            this.specificRequirement = data.specific_requirement || ''\n            this.currentRequirementId = data.requirement_id\n\n            // 更新报告数据\n            this.reportData = {\n              totalArticles: data.analysis_results?.total_articles || 0,\n              totalKeywords: this.selectedKeywords.length,\n              dataSources: 1,\n              sentiment: {\n                positive: data.analysis_results?.sentiment?.positive || 0,\n                neutral: data.analysis_results?.sentiment?.neutral || 0,\n                negative: data.analysis_results?.sentiment?.negative || 0\n              },\n              onlineSearchCount: data.analysis_results?.online_search?.length || 0,\n              customSourceCounts: {}\n            }\n\n            // 处理在线搜索结果\n            if (data.analysis_results?.online_search) {\n              this.handleOnlineSearchResults(data.analysis_results.online_search)\n            }\n\n            this.$message.success('报告数据加载成功')\n          } else {\n            throw new Error(response.msg || '获取报告数据失败')\n          }\n        } else {\n          // 没有需求ID，使用默认数据\n          this.reportData = {\n            totalArticles: 0,\n            totalKeywords: this.selectedKeywords.length,\n            dataSources: 1,\n            sentiment: {\n              positive: 0,\n              neutral: 0,\n              negative: 0\n            },\n            onlineSearchCount: 0,\n            customSourceCounts: {}\n          }\n        }\n      } catch (error) {\n        console.error('加载报告数据失败:', error)\n        this.$message.warning('加载报告数据失败，显示默认数据')\n\n        // 使用默认数据\n        this.reportData = {\n          totalArticles: 0,\n          totalKeywords: this.selectedKeywords.length,\n          dataSources: 1,\n          sentiment: {\n            positive: 0,\n            neutral: 0,\n            negative: 0\n          },\n          onlineSearchCount: 0,\n          customSourceCounts: {}\n        }\n      }\n\n      // 提示用户可以重新执行分析获取最新数据\n      this.$message.warning('这是通过链接访问的报告，数据可能不是最新的。建议重新执行分析获取最新结果。')\n    },\n\n    // 获取关键词频率（基于实际数据）\n    getKeywordFrequency(keyword) {\n      // TODO: 这里可以根据实际分析结果返回关键词频率\n      // 暂时返回模拟数据，后续可以从reportData中计算真实频率\n      return Math.floor(Math.random() * 50) + 10\n    },\n\n    // 获取数据源统计数量\n    getSourceCount(source) {\n      if (this.reportData.customSourceCounts) {\n        return this.reportData.customSourceCounts[source] || 0\n      }\n      return 0\n    },\n\n\n\n    // 提取域名\n    extractDomainName(url) {\n      try {\n        const urlObj = new URL(url)\n        return urlObj.hostname.replace('www.', '')\n      } catch (error) {\n        // 如果不是有效URL，直接返回原字符串的前20个字符\n        return url.length > 20 ? url.substring(0, 20) + '...' : url\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.opinion-analysis {\n  padding: 0;\n  background-color: #f8f9fa;\n  min-height: 100vh;\n}\n\n// 步骤指示器样式\n.steps-container {\n  background: white;\n  padding: 20px 24px;\n  border-bottom: 1px solid #e8e8e8;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n\n  .left-actions {\n    flex: 0 0 auto;\n\n    .timed-push-btn {\n      font-size: 14px;\n      padding: 8px 16px;\n      border-radius: 6px;\n      font-weight: 500;\n      transition: all 0.3s ease;\n\n      &:hover {\n        transform: translateY(-1px);\n        box-shadow: 0 2px 8px rgba(84, 112, 198, 0.3);\n      }\n\n      &:active {\n        transform: translateY(0);\n      }\n    }\n  }\n\n  .steps-wrapper {\n    flex: 1;\n    display: flex;\n    justify-content: center;\n    gap: 60px;\n  }\n\n  .right-placeholder {\n    flex: 0 0 auto;\n    width: 88px; // 与左侧按钮宽度保持平衡\n  }\n\n  .right-actions {\n    flex: 0 0 auto;\n    display: flex;\n    align-items: center;\n    gap: 12px;\n\n    .step-switch-dropdown {\n      .step-switch-btn {\n        font-size: 13px;\n        padding: 6px 12px;\n        border-radius: 6px;\n        font-weight: 500;\n        background: #f39c12;\n        border-color: #f39c12;\n        color: white;\n        transition: all 0.3s ease;\n        box-shadow: 0 2px 4px rgba(243, 156, 18, 0.2);\n\n        &:hover {\n          background: #e67e22;\n          border-color: #e67e22;\n          transform: translateY(-1px);\n          box-shadow: 0 4px 8px rgba(243, 156, 18, 0.3);\n        }\n\n        &:active {\n          transform: translateY(0);\n        }\n\n        .el-icon--right {\n          margin-left: 4px;\n        }\n      }\n    }\n\n    .analyze-record-btn,\n    .timed-push-btn {\n      font-size: 13px;\n      padding: 6px 12px;\n      border-radius: 6px;\n      font-weight: 500;\n      transition: all 0.3s ease;\n\n      &:hover {\n        transform: translateY(-1px);\n        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n      }\n\n      &:active {\n        transform: translateY(0);\n      }\n    }\n  }\n\n  .step-item {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n    color: #999;\n    font-size: 14px;\n\n    &.active {\n      color: #5470c6;\n      font-weight: 500;\n\n      .step-number {\n        background: #5470c6;\n        color: white;\n      }\n    }\n\n    .step-number {\n      width: 24px;\n      height: 24px;\n      border-radius: 50%;\n      background: #e8e8e8;\n      color: #999;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 12px;\n      font-weight: 500;\n    }\n  }\n}\n\n.main-content {\n  max-width: 800px;\n  margin: 0 auto;\n  padding: 40px 24px;\n}\n\n// 报告预览区域\n.report-preview {\n  background: white;\n  border-radius: 8px;\n  padding: 32px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n  margin-bottom: 32px;\n\n  .section-title {\n    font-size: 18px;\n    font-weight: 600;\n    color: #333;\n    margin: 0 0 24px 0;\n  }\n}\n\n// 报告概览卡片\n.report-overview {\n  margin-bottom: 24px;\n\n  .overview-card {\n    background: #f8f9fa;\n    border-radius: 8px;\n    padding: 20px;\n    border: 1px solid #e8e8e8;\n\n    .card-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 16px;\n\n      h3 {\n        font-size: 16px;\n        font-weight: 600;\n        color: #333;\n        margin: 0;\n      }\n\n      .analysis-time {\n        font-size: 13px;\n        color: #666;\n      }\n    }\n\n    .overview-stats {\n      display: flex;\n      gap: 32px;\n\n      .stat-item {\n        text-align: center;\n\n        .stat-number {\n          font-size: 24px;\n          font-weight: 600;\n          color: #5470c6;\n          margin-bottom: 4px;\n        }\n\n        .stat-label {\n          font-size: 13px;\n          color: #666;\n        }\n      }\n    }\n  }\n}\n\n// 分析卡片通用样式\n.analysis-card {\n  background: white;\n  border-radius: 8px;\n  padding: 20px;\n  border: 1px solid #e8e8e8;\n  margin-bottom: 16px;\n\n  h3 {\n    font-size: 16px;\n    font-weight: 600;\n    color: #333;\n    margin: 0 0 16px 0;\n  }\n}\n\n// 情感分析\n.sentiment-analysis {\n  margin-bottom: 24px;\n\n  .sentiment-chart {\n    .sentiment-item {\n      display: flex;\n      align-items: center;\n      margin-bottom: 12px;\n\n      &:last-child {\n        margin-bottom: 0;\n      }\n\n      .sentiment-bar {\n        flex: 1;\n        height: 20px;\n        background: #f0f0f0;\n        border-radius: 10px;\n        overflow: hidden;\n        margin-right: 12px;\n\n        .bar-fill {\n          height: 100%;\n          border-radius: 10px;\n          transition: width 0.3s ease;\n        }\n      }\n\n      &.positive .bar-fill {\n        background: linear-gradient(90deg, #52c41a, #73d13d);\n      }\n\n      &.neutral .bar-fill {\n        background: linear-gradient(90deg, #faad14, #ffc53d);\n      }\n\n      &.negative .bar-fill {\n        background: linear-gradient(90deg, #ff4d4f, #ff7875);\n      }\n\n      .sentiment-info {\n        display: flex;\n        align-items: center;\n        gap: 8px;\n        min-width: 80px;\n\n        .sentiment-label {\n          font-size: 14px;\n          color: #666;\n        }\n\n        .sentiment-value {\n          font-size: 14px;\n          font-weight: 600;\n          color: #333;\n        }\n      }\n    }\n  }\n}\n\n// 关键词分析\n.keyword-analysis {\n  margin-bottom: 24px;\n\n  .selected-keywords-display {\n    .keyword-list {\n      display: flex;\n      flex-wrap: wrap;\n      gap: 12px;\n\n      .keyword-item {\n        display: flex;\n        align-items: center;\n        background: #f0f7ff;\n        border: 1px solid #d6e4ff;\n        border-radius: 16px;\n        padding: 6px 12px;\n        font-size: 13px;\n\n        .keyword-text {\n          color: #1890ff;\n          margin-right: 6px;\n        }\n\n        .keyword-frequency {\n          background: #1890ff;\n          color: white;\n          border-radius: 8px;\n          padding: 2px 6px;\n          font-size: 11px;\n          font-weight: 500;\n        }\n      }\n    }\n  }\n}\n\n// 数据来源统计\n.data-source-stats {\n  margin-bottom: 24px;\n\n  .source-list {\n    .source-item {\n      display: flex;\n      align-items: center;\n      padding: 12px 0;\n      border-bottom: 1px solid #f0f0f0;\n\n      &:last-child {\n        border-bottom: none;\n      }\n\n      .source-icon {\n        width: 32px;\n        height: 32px;\n        border-radius: 6px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        margin-right: 12px;\n\n        &.online {\n          background: #e6f4ff;\n          color: #1890ff;\n        }\n\n        &.custom {\n          background: #f6ffed;\n          color: #52c41a;\n        }\n\n        i {\n          font-size: 16px;\n        }\n      }\n\n      .source-info {\n        flex: 1;\n\n        .source-name {\n          font-size: 14px;\n          font-weight: 500;\n          color: #333;\n          margin-bottom: 2px;\n        }\n\n        .source-desc {\n          font-size: 12px;\n          color: #999;\n        }\n      }\n\n      .source-count {\n        font-size: 14px;\n        font-weight: 600;\n        color: #5470c6;\n      }\n    }\n  }\n}\n\n// 详细数据样式\n.detailed-data {\n  margin-bottom: 24px;\n\n  .card-header-with-controls {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 20px;\n\n    h3 {\n      margin: 0;\n    }\n\n    .data-controls {\n      display: flex;\n      align-items: center;\n    }\n  }\n\n  .article-list {\n    .article-item {\n      background: #fafafa;\n      border: 1px solid #f0f0f0;\n      border-radius: 8px;\n      margin-bottom: 12px;\n      overflow: hidden;\n      transition: all 0.3s ease;\n\n      &:hover {\n        border-color: #d9d9d9;\n        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n      }\n\n      .article-header {\n        padding: 16px;\n        background: white;\n\n        .article-title-row {\n          display: flex;\n          justify-content: space-between;\n          align-items: flex-start;\n          margin-bottom: 8px;\n\n          .article-title {\n            flex: 1;\n            margin: 0;\n            font-size: 16px;\n            font-weight: 500;\n            color: #262626;\n            cursor: pointer;\n            line-height: 1.4;\n            margin-right: 12px;\n            transition: color 0.3s ease;\n\n            &:hover {\n              color: #1890ff;\n            }\n\n            i {\n              margin-left: 8px;\n              font-size: 14px;\n              color: #8c8c8c;\n            }\n          }\n\n          .article-meta {\n            display: flex;\n            align-items: center;\n            gap: 8px;\n            flex-shrink: 0;\n\n            .article-source {\n              font-size: 12px;\n              color: #8c8c8c;\n              background: #f5f5f5;\n              padding: 2px 8px;\n              border-radius: 4px;\n            }\n\n            .sentiment-tag {\n              font-size: 12px;\n              padding: 2px 8px;\n              border-radius: 4px;\n              font-weight: 500;\n\n              &.positive {\n                background: #f6ffed;\n                color: #52c41a;\n                border: 1px solid #b7eb8f;\n              }\n\n              &.neutral {\n                background: #f5f5f5;\n                color: #8c8c8c;\n                border: 1px solid #d9d9d9;\n              }\n\n              &.negative {\n                background: #fff2f0;\n                color: #ff4d4f;\n                border: 1px solid #ffccc7;\n              }\n            }\n          }\n        }\n\n        .article-info {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n\n          .publish-time {\n            font-size: 12px;\n            color: #8c8c8c;\n          }\n\n          .article-actions {\n            display: flex;\n            gap: 8px;\n          }\n        }\n      }\n\n      .article-content {\n        padding: 0 16px 16px;\n\n        .content-summary {\n          margin: 0;\n          color: #595959;\n          line-height: 1.6;\n          font-size: 14px;\n        }\n\n        &.expanded {\n          background: #fafafa;\n          border-top: 1px solid #f0f0f0;\n          padding: 16px;\n\n          .content-full {\n            margin: 0;\n            color: #262626;\n            line-height: 1.6;\n            font-size: 14px;\n            white-space: pre-wrap;\n          }\n        }\n      }\n    }\n  }\n\n  .pagination-wrapper {\n    display: flex;\n    justify-content: center;\n    margin-top: 20px;\n    padding-top: 20px;\n    border-top: 1px solid #f0f0f0;\n  }\n}\n\n// 报告操作\n.report-actions {\n  margin-top: 32px;\n  text-align: center;\n\n  .action-buttons {\n    display: flex;\n    justify-content: center;\n    gap: 16px;\n\n    .el-button {\n      padding: 12px 24px;\n      font-size: 14px;\n      border-radius: 6px;\n    }\n  }\n}\n\n// 分析来源区域\n.analysis-source {\n  background: white;\n  border-radius: 8px;\n  padding: 32px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n  margin-bottom: 32px;\n\n  .section-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 24px;\n\n    .section-title {\n      font-size: 18px;\n      font-weight: 600;\n      color: #333;\n      margin: 0;\n    }\n\n    .template-btn {\n      font-size: 14px;\n      padding: 6px 16px;\n    }\n  }\n\n  .section-title {\n    font-size: 18px;\n    font-weight: 600;\n    color: #333;\n    margin: 0 0 24px 0;\n  }\n}\n\n// 输入区域样式\n.input-section {\n  margin-bottom: 24px;\n\n  .input-label {\n    font-size: 14px;\n    color: #333;\n    margin-bottom: 8px;\n    font-weight: 500;\n\n    .required {\n      color: #ff4d4f;\n      margin-left: 2px;\n    }\n\n    .keyword-count {\n      color: #999;\n      font-weight: normal;\n      margin-left: 8px;\n      font-size: 13px;\n\n      &.max-reached {\n        color: #ff4d4f;\n        font-weight: 500;\n      }\n    }\n  }\n\n  .entity-input {\n    :deep(.el-input__inner) {\n      border-radius: 6px;\n      border: 1px solid #d9d9d9;\n      padding: 12px 16px;\n      font-size: 14px;\n\n      &::placeholder {\n        color: #bfbfbf;\n      }\n\n      &:focus {\n        border-color: #5470c6;\n        box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);\n      }\n    }\n\n    &.error {\n      :deep(.el-input__inner) {\n        border-color: #ff4d4f;\n\n        &:focus {\n          border-color: #ff4d4f;\n          box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.1);\n        }\n      }\n    }\n  }\n}\n\n// 任务列表样式\n.task-list {\n  padding: 24px;\n\n  .empty-task-list {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n    padding: 40px 0;\n\n    .empty-icon {\n      font-size: 48px;\n      margin-bottom: 16px;\n      color: #d9d9d9;\n    }\n\n    .empty-text {\n      font-size: 16px;\n      color: #999;\n      margin-bottom: 24px;\n    }\n\n    .add-task-btn {\n      padding: 8px 20px;\n    }\n  }\n\n  .task-items {\n    display: flex;\n    flex-direction: column;\n    gap: 16px;\n  }\n\n  .task-item {\n    background: white;\n    border-radius: 8px;\n    padding: 16px;\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n    display: flex;\n    justify-content: space-between;\n    transition: all 0.3s ease;\n\n    &:hover {\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n      transform: translateY(-2px);\n    }\n\n    .task-info {\n       flex: 1;\n\n       .task-header {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        margin-bottom: 8px;\n       }\n\n       .task-name {\n        font-size: 16px;\n        font-weight: 500;\n        color: #333;\n       }\n\n       .task-status {\n        font-size: 12px;\n        padding: 2px 8px;\n        border-radius: 10px;\n        font-weight: 500;\n\n        &.status-running {\n          background-color: rgba(82, 196, 26, 0.1);\n          color: #52c41a;\n        }\n\n        &.status-pending {\n          background-color: rgba(250, 173, 20, 0.1);\n          color: #faad14;\n        }\n       }\n\n       /* 任务描述样式已移除，因为不再显示任务描述 */\n\n       .task-schedule {\n        font-size: 13px;\n        color: #999;\n        display: flex;\n        align-items: center;\n        gap: 4px;\n        margin-top: 8px;\n\n        i {\n          font-size: 14px;\n        }\n       }\n     }\n\n    .task-actions {\n      display: flex;\n      align-items: flex-start;\n      gap: 8px;\n\n      .el-button {\n        padding: 4px;\n\n        i {\n          font-size: 16px;\n        }\n      }\n    }\n  }\n}\n\n.requirement-textarea {\n  :deep(.el-textarea__inner) {\n    border-radius: 6px;\n    border: 1px solid #d9d9d9;\n    padding: 12px 16px;\n    font-size: 14px;\n    line-height: 1.6;\n    resize: vertical;\n\n    &::placeholder {\n      color: #bfbfbf;\n    }\n\n    &:focus {\n      border-color: #5470c6;\n      box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);\n    }\n  }\n\n  &.error {\n    :deep(.el-textarea__inner) {\n      border-color: #ff4d4f;\n\n      &:focus {\n        border-color: #ff4d4f;\n        box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.1);\n      }\n    }\n  }\n}\n\n// 选择关联词区域\n.related-words-section {\n  .section-header {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    margin-bottom: 16px;\n\n    .header-left {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n\n      .section-label {\n        font-size: 14px;\n        color: #333;\n        font-weight: 500;\n      }\n\n      .word-count {\n        font-size: 14px;\n        color: #999;\n        font-weight: normal;\n        margin-left: 8px;\n        transition: color 0.3s ease;\n\n        &.max-reached {\n          color: #ff4d4f;\n          font-weight: 500;\n        }\n      }\n    }\n\n    .regenerate-btn {\n      font-size: 13px;\n      color: #5470c6;\n      padding: 4px 8px;\n      border-radius: 4px;\n      transition: all 0.3s ease;\n\n      &:hover {\n        background-color: #f0f7ff;\n        color: #4096ff;\n      }\n\n      i {\n        margin-right: 4px;\n        font-size: 12px;\n      }\n    }\n  }\n\n  .words-container {\n    text-align: center;\n\n    .generate-word-btn {\n      display: inline-flex;\n      align-items: center;\n      justify-content: center;\n      gap: 6px;\n      padding: 8px 16px;\n      background: #f0f7ff;\n      color: #5470c6;\n      border: 1px dashed #5470c6;\n      border-radius: 4px;\n      font-size: 14px;\n      cursor: pointer;\n      transition: all 0.3s ease;\n      margin-bottom: 12px;\n\n      &:hover {\n        background: #e6f4ff;\n        border-color: #4096ff;\n      }\n\n      i {\n        font-size: 12px;\n      }\n    }\n\n    .word-description {\n      font-size: 12px;\n      color: #999;\n      line-height: 1.5;\n    }\n  }\n}\n\n// 关键词文本框包装器\n.keywords-textbox-wrapper {\n  border: 1px solid #d9d9d9;\n  border-radius: 6px;\n  padding: 16px;\n  background: #fff;\n  min-height: 120px;\n  transition: border-color 0.3s ease, box-shadow 0.3s ease;\n\n  &:hover {\n    border-color: #5470c6;\n  }\n\n  &:focus-within {\n    border-color: #5470c6;\n    box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);\n  }\n}\n\n// 生成的关键词显示区域\n.generated-keywords-display {\n  margin-bottom: 16px;\n\n  .keyword-category {\n    display: flex;\n    align-items: flex-start;\n    gap: 16px;\n    margin-bottom: 16px;\n\n    &:last-child {\n      margin-bottom: 0;\n    }\n\n    .category-button {\n      min-width: 100px;\n      margin-right: 16px;\n      margin-bottom: 8px;\n      font-size: 13px;\n      border-radius: 16px;\n      transition: all 0.3s ease;\n\n      &:hover {\n        transform: translateY(-1px);\n        box-shadow: 0 2px 8px rgba(84, 112, 198, 0.3);\n      }\n\n      &:active {\n        transform: translateY(0);\n      }\n    }\n\n    .keyword-tags {\n      flex: 1;\n      display: flex;\n      flex-wrap: wrap;\n      gap: 8px;\n\n      .keyword-tag {\n        font-size: 13px;\n        padding: 6px 12px;\n        border-radius: 16px;\n        cursor: pointer;\n        transition: all 0.3s ease;\n        border: 1px solid #d9d9d9;\n        background: #fff;\n        color: #666;\n        display: inline-flex;\n        align-items: center;\n        justify-content: center;\n        text-align: center;\n        user-select: none;\n\n        &:hover {\n          border-color: #5470c6;\n          color: #5470c6;\n        }\n\n        &.selected {\n          background: #5470c6;\n          color: white;\n          border-color: #5470c6;\n        }\n      }\n    }\n  }\n}\n\n\n\n// 关键词选择区域\n.keywords-selection-section {\n  .keywords-grid {\n    display: flex;\n    flex-direction: column;\n    gap: 20px;\n  }\n\n  .keyword-category {\n    display: flex;\n    align-items: flex-start;\n    gap: 16px;\n\n    .category-label {\n      font-size: 14px;\n      color: #333;\n      font-weight: 500;\n      min-width: 80px;\n      padding-top: 6px;\n    }\n\n    .keyword-tags {\n      flex: 1;\n      display: flex;\n      flex-wrap: wrap;\n      gap: 8px;\n\n      .keyword-tag {\n        font-size: 13px;\n        padding: 6px 12px;\n        border-radius: 16px;\n        cursor: pointer;\n        transition: all 0.3s ease;\n        border: 1px solid #d9d9d9;\n        background: #fff;\n        color: #666;\n        display: inline-flex;\n        align-items: center;\n        justify-content: center;\n        text-align: center;\n        user-select: none;\n\n        &:hover {\n          border-color: #5470c6;\n          color: #5470c6;\n        }\n\n        &.selected {\n          background: #5470c6;\n          color: white;\n          border-color: #5470c6;\n        }\n\n        &.highlight {\n          background: #333;\n          color: white;\n          border-color: #333;\n          position: relative;\n          cursor: default;\n\n          &::after {\n            content: '';\n            position: absolute;\n            bottom: -8px;\n            left: 50%;\n            transform: translateX(-50%);\n            width: 0;\n            height: 0;\n            border-left: 6px solid transparent;\n            border-right: 6px solid transparent;\n            border-top: 6px solid #333;\n          }\n        }\n      }\n    }\n  }\n}\n\n// 第二步：数据概览样式\n.data-overview {\n  background: white;\n  border-radius: 8px;\n  padding: 32px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n  margin-bottom: 32px;\n\n  .section-title {\n    font-size: 18px;\n    font-weight: 600;\n    color: #333;\n    margin: 0 0 24px 0;\n  }\n}\n\n.data-source-section {\n  .source-option {\n    display: flex;\n    align-items: flex-start;\n    gap: 16px;\n    padding: 20px;\n    border: 1px solid #e8e8e8;\n    border-radius: 8px;\n    margin-bottom: 16px;\n    cursor: pointer;\n    transition: all 0.3s ease;\n\n    &:hover {\n      border-color: #5470c6;\n      background: #f8f9fa;\n    }\n\n    .source-checkbox {\n      :deep(.el-checkbox__input) {\n        .el-checkbox__inner {\n          width: 18px;\n          height: 18px;\n          border-radius: 4px;\n          border: 2px solid #d9d9d9;\n\n          &::after {\n            width: 5px;\n            height: 9px;\n            left: 5px;\n            top: 1px;\n          }\n        }\n\n        &.is-checked .el-checkbox__inner {\n          background-color: #5470c6;\n          border-color: #5470c6;\n        }\n      }\n    }\n\n    .source-icon {\n      width: 40px;\n      height: 40px;\n      background: #f0f7ff;\n      border-radius: 8px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n\n      i {\n        font-size: 20px;\n        color: #5470c6;\n      }\n    }\n\n    .source-content {\n      flex: 1;\n\n      h3 {\n        font-size: 16px;\n        font-weight: 500;\n        color: #333;\n        margin: 0 0 4px 0;\n        word-break: break-all;\n        line-height: 1.4;\n      }\n\n      .source-description {\n        margin: 0;\n        font-size: 14px;\n        color: #666;\n        line-height: 1.4;\n      }\n\n      .source-count {\n        color: #409eff;\n        font-weight: 500;\n      }\n    }\n\n    .source-actions {\n      display: flex;\n      align-items: center;\n\n      .el-icon-delete {\n        font-size: 16px;\n        color: #999;\n        cursor: pointer;\n        transition: color 0.3s ease;\n\n        &:hover {\n          color: #ff4d4f;\n        }\n      }\n    }\n  }\n\n  .add-source-btn {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    gap: 8px;\n    padding: 16px;\n    border: 2px dashed #d9d9d9;\n    border-radius: 8px;\n    color: #999;\n    cursor: pointer;\n    transition: all 0.3s ease;\n    font-size: 14px;\n\n    &:hover {\n      border-color: #5470c6;\n      color: #5470c6;\n      background: #f8f9fa;\n    }\n\n    i {\n      font-size: 16px;\n    }\n  }\n}\n\n// 底部按钮区域\n.bottom-actions {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: 16px;\n  padding-top: 24px;\n\n  .el-button {\n    padding: 12px 32px;\n    font-size: 16px;\n\n    &:disabled {\n      background-color: #f5f5f5;\n      border-color: #d9d9d9;\n      color: #bfbfbf;\n      cursor: not-allowed;\n    }\n  }\n}\n\n// 新增数据源表单样式\n.add-source-form {\n  background: white;\n  border: 1px solid #e8e8e8;\n  border-radius: 8px;\n  padding: 20px;\n  margin-top: 16px;\n\n  .form-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 20px;\n    padding-bottom: 12px;\n    border-bottom: 1px solid #f0f0f0;\n\n    h3 {\n      font-size: 16px;\n      font-weight: 600;\n      color: #333;\n      margin: 0;\n    }\n\n    .el-icon-close {\n      font-size: 18px;\n      color: #999;\n      cursor: pointer;\n      transition: color 0.3s ease;\n\n      &:hover {\n        color: #666;\n      }\n    }\n  }\n\n  .form-item {\n    .form-label {\n      display: block;\n      font-size: 14px;\n      color: #333;\n      font-weight: 500;\n      margin-bottom: 8px;\n\n      .required {\n        color: #ff4d4f;\n        margin-left: 2px;\n      }\n    }\n\n    .input-group {\n      display: flex;\n      gap: 12px;\n      align-items: flex-start;\n\n      .source-url-input {\n        flex: 1;\n\n        :deep(.el-input__inner) {\n          border-radius: 6px;\n          border: 1px solid #d9d9d9;\n          padding: 12px 16px;\n          font-size: 14px;\n\n          &::placeholder {\n            color: #bfbfbf;\n          }\n\n          &:focus {\n            border-color: #5470c6;\n            box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);\n          }\n        }\n      }\n\n      .el-button {\n        padding: 12px 24px;\n        font-size: 14px;\n        border-radius: 6px;\n        white-space: nowrap;\n      }\n    }\n  }\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  .steps-container {\n    padding: 16px 12px;\n    flex-direction: column;\n    gap: 16px;\n\n    .left-actions {\n      align-self: flex-start;\n\n      .timed-push-btn {\n        font-size: 13px;\n        padding: 6px 12px;\n      }\n    }\n\n    .steps-wrapper {\n      gap: 30px;\n    }\n\n    .right-placeholder {\n      display: none;\n    }\n\n    .step-item {\n      font-size: 13px;\n    }\n  }\n\n  .main-content {\n    padding: 24px 16px;\n  }\n\n  .analysis-source {\n    padding: 24px 20px;\n  }\n\n  .document-content {\n    padding: 12px;\n    min-height: 100px;\n  }\n}\n\n// 定时任务抽屉样式\n.timed-task-drawer {\n  :deep(.el-drawer__header) {\n    padding: 20px 24px 16px;\n    border-bottom: 1px solid #f0f0f0;\n    margin-bottom: 0;\n  }\n\n  :deep(.el-drawer__body) {\n    padding: 0;\n  }\n}\n\n.drawer-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n\n  .drawer-title {\n    font-size: 18px;\n    font-weight: 600;\n    color: #333;\n  }\n\n  .add-task-btn {\n    font-size: 14px;\n    padding: 6px 12px;\n    border-radius: 4px;\n\n    .el-icon-plus {\n      margin-right: 4px;\n    }\n  }\n}\n\n.drawer-content {\n  padding: 24px;\n  min-height: 400px;\n}\n\n.empty-state {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 400px;\n}\n\n.empty-content {\n  text-align: center;\n\n  .empty-icon {\n    margin-bottom: 24px;\n    display: flex;\n    justify-content: center;\n\n    svg {\n      opacity: 0.6;\n    }\n  }\n\n  .empty-text {\n    font-size: 16px;\n    color: #909399;\n    margin: 0 0 24px 0;\n    font-weight: 500;\n  }\n\n  .create-btn {\n    padding: 10px 24px;\n    font-size: 14px;\n    border-radius: 6px;\n    font-weight: 500;\n  }\n}\n\n// 创建任务弹窗样式\n.create-task-dialog {\n  :deep(.el-dialog) {\n    border-radius: 8px;\n  }\n\n  :deep(.el-dialog__header) {\n    padding: 20px 24px 16px;\n    border-bottom: 1px solid #f0f0f0;\n  }\n\n  :deep(.el-dialog__body) {\n    padding: 24px;\n  }\n\n  :deep(.el-dialog__footer) {\n    padding: 16px 24px 24px;\n    border-top: 1px solid #f0f0f0;\n  }\n}\n\n.task-form {\n  .task-requirement-section {\n    margin-bottom: 24px;\n\n    .section-label {\n      font-size: 14px;\n      font-weight: 500;\n      color: #333;\n      margin-bottom: 16px;\n\n      .required {\n        color: #ff4d4f;\n        margin-left: 2px;\n      }\n    }\n\n    .form-group {\n      margin-bottom: 16px;\n\n      &:last-child {\n        margin-bottom: 0;\n      }\n\n      .input-label {\n        font-size: 13px;\n        color: #666;\n        margin-bottom: 6px;\n        font-weight: 500;\n      }\n\n      .task-name-input {\n        :deep(.el-input__inner) {\n          border-radius: 6px;\n          border: 1px solid #d9d9d9;\n          padding: 10px 12px;\n          font-size: 14px;\n\n          &::placeholder {\n            color: #bfbfbf;\n          }\n\n          &:focus {\n            border-color: #5470c6;\n            box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);\n          }\n        }\n      }\n\n      .task-description-input {\n        :deep(.el-textarea__inner) {\n          border-radius: 6px;\n          border: 1px solid #d9d9d9;\n          padding: 10px 12px;\n          font-size: 14px;\n          line-height: 1.5;\n          resize: vertical;\n\n          &::placeholder {\n            color: #bfbfbf;\n          }\n\n          &:focus {\n            border-color: #5470c6;\n            box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);\n          }\n        }\n      }\n    }\n  }\n\n  .execute-time-section {\n    .section-label {\n      font-size: 14px;\n      font-weight: 500;\n      color: #333;\n      margin-bottom: 12px;\n    }\n\n    .time-selector {\n      display: flex;\n      gap: 12px;\n      align-items: center;\n\n      .frequency-select {\n        width: 120px;\n      }\n\n      .time-picker {\n        width: 140px;\n      }\n\n      .datetime-picker {\n        width: 200px;\n      }\n    }\n  }\n}\n\n.dialog-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 12px;\n\n  .modify-btn {\n    color: #666;\n    border-color: #d9d9d9;\n\n    &:hover {\n      color: #5470c6;\n      border-color: #5470c6;\n    }\n  }\n\n  .run-btn {\n    background: #5470c6;\n    border-color: #5470c6;\n\n    &:hover {\n      background: #4096ff;\n      border-color: #4096ff;\n    }\n  }\n\n  .save-btn {\n    background: #52c41a;\n    border-color: #52c41a;\n\n    &:hover {\n      background: #73d13d;\n      border-color: #73d13d;\n    }\n  }\n}\n\n// 自定义数据源管理区域样式\n.custom-sources-management {\n  margin-top: 16px;\n  padding: 16px;\n  background: #f8f9fa;\n  border-radius: 8px;\n  border: 1px solid #e9ecef;\n\n  .management-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 16px;\n\n    h4 {\n      margin: 0;\n      font-size: 14px;\n      font-weight: 600;\n      color: #333;\n    }\n  }\n\n  .existing-sources {\n    .source-item {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: 12px;\n      background: white;\n      border: 1px solid #e8e8e8;\n      border-radius: 6px;\n      margin-bottom: 8px;\n\n      .source-info {\n        display: flex;\n        align-items: center;\n        gap: 8px;\n        flex: 1;\n\n        i {\n          color: #409eff;\n        }\n\n        .source-name {\n          font-weight: 500;\n          color: #333;\n        }\n\n        .source-url {\n          color: #666;\n          font-size: 12px;\n          margin-left: 8px;\n        }\n      }\n\n      .source-actions {\n        i {\n          cursor: pointer;\n          color: #f56c6c;\n          font-size: 16px;\n          padding: 4px;\n\n          &:hover {\n            color: #f78989;\n          }\n        }\n      }\n    }\n  }\n\n  .no-sources {\n    text-align: center;\n    padding: 20px;\n    color: #999;\n    font-size: 14px;\n  }\n}\n\n// 数据源列表区域样式\n.data-source-list-section {\n  margin-top: 16px;\n  padding: 16px;\n  background: #f8f9fa;\n  border-radius: 8px;\n  border: 1px solid #e9ecef;\n\n  .list-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 16px;\n\n    h4 {\n      margin: 0;\n      font-size: 14px;\n      font-weight: 600;\n      color: #333;\n    }\n\n    .header-controls {\n      display: flex;\n      gap: 8px;\n      align-items: center;\n    }\n  }\n\n  .data-source-table {\n    background: white;\n    border-radius: 6px;\n    overflow: hidden;\n\n    .url-cell {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n\n      i {\n        color: #409eff;\n        font-size: 14px;\n        flex-shrink: 0;\n      }\n\n      .url-text {\n        color: #333;\n        font-size: 13px;\n        flex: 1;\n      }\n    }\n\n    .pagination-wrapper {\n      padding: 16px;\n      text-align: right;\n      background: #fafafa;\n      border-top: 1px solid #e8e8e8;\n    }\n  }\n}\n\n// 任务列表样式（待实现）\n// .task-list {\n//   // TODO: 任务列表样式\n// }\n\n// 推送报告弹窗样式\n.form-tip {\n  margin-top: 8px;\n  font-size: 12px;\n  color: #909399;\n  line-height: 1.4;\n\n  i {\n    margin-right: 4px;\n    color: #409eff;\n  }\n}\n\n.dialog-footer {\n  text-align: right;\n\n  .el-button {\n    margin-left: 8px;\n  }\n}\n\n// 推送按钮图标样式\n.el-button .el-icon-s-promotion {\n  margin-right: 6px;\n}\n\n// 任务预览弹窗样式\n.task-preview-dialog {\n  :deep(.el-dialog) {\n    border-radius: 8px;\n  }\n\n  :deep(.el-dialog__header) {\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    color: white;\n    padding: 20px 24px;\n    border-radius: 8px 8px 0 0;\n\n    .el-dialog__title {\n      color: white;\n      font-weight: 600;\n      font-size: 16px;\n    }\n\n    .el-dialog__close {\n      color: white;\n      font-size: 18px;\n\n      &:hover {\n        color: #f0f0f0;\n      }\n    }\n  }\n\n  :deep(.el-dialog__body) {\n    padding: 24px;\n    max-height: 60vh;\n    overflow-y: auto;\n  }\n\n  :deep(.el-dialog__footer) {\n    padding: 16px 24px 24px;\n    border-top: 1px solid #f0f0f0;\n  }\n}\n\n.task-preview-content {\n  .preview-section {\n    margin-bottom: 24px;\n\n    &:last-child {\n      margin-bottom: 0;\n    }\n\n    .section-title {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      font-size: 16px;\n      font-weight: 600;\n      color: #333;\n      margin-bottom: 16px;\n      padding-bottom: 8px;\n      border-bottom: 2px solid #f0f0f0;\n\n      i {\n        color: #667eea;\n        font-size: 18px;\n      }\n    }\n\n    .info-grid {\n      display: grid;\n      grid-template-columns: 1fr 1fr;\n      gap: 16px;\n\n      @media (max-width: 768px) {\n        grid-template-columns: 1fr;\n      }\n    }\n\n    .info-item {\n      display: flex;\n      align-items: flex-start;\n      gap: 8px;\n\n      label {\n        font-weight: 600;\n        color: #666;\n        min-width: 80px;\n        flex-shrink: 0;\n      }\n\n      span {\n        color: #333;\n        word-break: break-all;\n      }\n\n      .el-tag {\n        margin: 0;\n      }\n    }\n\n    .description-content {\n      background: #f8f9fa;\n      border: 1px solid #e9ecef;\n      border-radius: 6px;\n      padding: 16px;\n      color: #333;\n      line-height: 1.6;\n      white-space: pre-wrap;\n    }\n\n    .push-config {\n      .url-display {\n        display: flex;\n        align-items: center;\n        gap: 8px;\n        background: #f8f9fa;\n        border: 1px solid #e9ecef;\n        border-radius: 6px;\n        padding: 12px;\n\n        .url-text {\n          flex: 1;\n          color: #333;\n          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;\n          font-size: 13px;\n          word-break: break-all;\n        }\n\n        .el-button {\n          padding: 4px 8px;\n\n          i {\n            color: #667eea;\n          }\n\n          &:hover i {\n            color: #5a67d8;\n          }\n        }\n      }\n    }\n  }\n}\n\n/* ==================== 分析进度页面样式 ==================== */\n.analysis-progress {\n  .progress-overview {\n    margin-bottom: 24px;\n\n    .status-card {\n      background: #fff;\n      border-radius: 8px;\n      padding: 24px;\n      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n\n      .status-header {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        margin-bottom: 16px;\n\n        h3 {\n          margin: 0;\n          font-size: 16px;\n          font-weight: 600;\n        }\n\n        .status-indicator {\n          display: flex;\n          align-items: center;\n          gap: 8px;\n\n          .status-dot {\n            width: 8px;\n            height: 8px;\n            border-radius: 50%;\n            background: #ccc;\n\n            &.running {\n              background: #1890ff;\n              animation: pulse 1.5s infinite;\n            }\n\n            &.completed {\n              background: #52c41a;\n            }\n\n            &.failed {\n              background: #ff4d4f;\n            }\n          }\n\n          .status-text {\n            font-size: 14px;\n            color: #666;\n          }\n        }\n\n        &.running .status-text {\n          color: #1890ff;\n        }\n\n        &.completed .status-text {\n          color: #52c41a;\n        }\n\n        &.failed .status-text {\n          color: #ff4d4f;\n        }\n      }\n\n      .progress-bar-container {\n        display: flex;\n        align-items: center;\n        gap: 12px;\n\n        .progress-bar {\n          flex: 1;\n          height: 8px;\n          background: #f5f5f5;\n          border-radius: 4px;\n          overflow: hidden;\n\n          .progress-fill {\n            height: 100%;\n            background: linear-gradient(90deg, #1890ff, #40a9ff);\n            border-radius: 4px;\n            transition: width 0.3s ease;\n          }\n        }\n\n        .progress-text {\n          font-size: 14px;\n          font-weight: 600;\n          color: #1890ff;\n          min-width: 40px;\n        }\n      }\n    }\n  }\n\n  .real-time-logs {\n    .logs-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 16px;\n\n      h3 {\n        margin: 0;\n        font-size: 16px;\n        font-weight: 600;\n      }\n\n      .logs-controls {\n        display: flex;\n        gap: 8px;\n      }\n    }\n\n    .logs-container {\n      background: #1e1e1e;\n      border-radius: 8px;\n      padding: 16px;\n      height: 400px;\n      overflow-y: auto;\n      font-family: 'Consolas', 'Monaco', 'Courier New', monospace;\n\n      .no-logs {\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        justify-content: center;\n        height: 100%;\n        color: #888;\n\n        i {\n          font-size: 24px;\n          margin-bottom: 8px;\n        }\n      }\n\n      .logs-list {\n        .log-item {\n          display: flex;\n          gap: 12px;\n          margin-bottom: 8px;\n          font-size: 13px;\n          line-height: 1.4;\n\n          .log-time {\n            color: #888;\n            min-width: 80px;\n          }\n\n          .log-level {\n            min-width: 60px;\n            font-weight: 600;\n          }\n\n          .log-message {\n            flex: 1;\n            color: #fff;\n          }\n\n          &.info .log-level {\n            color: #40a9ff;\n          }\n\n          &.success .log-level {\n            color: #52c41a;\n          }\n\n          &.warning .log-level {\n            color: #faad14;\n          }\n\n          &.error .log-level {\n            color: #ff4d4f;\n          }\n        }\n      }\n\n      /* 滚动条样式 */\n      &::-webkit-scrollbar {\n        width: 6px;\n      }\n\n      &::-webkit-scrollbar-track {\n        background: #2a2a2a;\n        border-radius: 3px;\n      }\n\n      &::-webkit-scrollbar-thumb {\n        background: #555;\n        border-radius: 3px;\n\n        &:hover {\n          background: #777;\n        }\n      }\n    }\n  }\n\n  .analysis-completed,\n  .analysis-failed {\n    text-align: center;\n    margin-top: 24px;\n    padding: 24px;\n    background: #fff;\n    border-radius: 8px;\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n\n    .completion-message,\n    .failure-message {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: 8px;\n      margin-bottom: 16px;\n      font-size: 16px;\n      font-weight: 600;\n\n      i {\n        font-size: 20px;\n      }\n    }\n\n    .completion-message {\n      color: #52c41a;\n    }\n\n    .failure-message {\n      color: #ff4d4f;\n    }\n  }\n}\n\n@keyframes pulse {\n  0% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.5;\n  }\n  100% {\n    opacity: 1;\n  }\n}\n\n// 模板弹窗样式\n.template-dialog {\n  :deep(.el-dialog) {\n    border-radius: 8px;\n  }\n\n  :deep(.el-dialog__header) {\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    color: white;\n    padding: 20px 24px;\n    border-radius: 8px 8px 0 0;\n\n    .el-dialog__title {\n      color: white;\n      font-weight: 600;\n      font-size: 16px;\n    }\n\n    .el-dialog__close {\n      color: white;\n      font-size: 18px;\n\n      &:hover {\n        color: #f0f0f0;\n      }\n    }\n  }\n\n  :deep(.el-dialog__body) {\n    padding: 0;\n    max-height: 60vh;\n    overflow-y: auto;\n  }\n\n  :deep(.el-dialog__footer) {\n    padding: 16px 24px 24px;\n    border-top: 1px solid #f0f0f0;\n  }\n}\n\n.template-content {\n  .template-list {\n    padding: 24px;\n  }\n\n  .template-item {\n    border: 2px solid #e8e8e8;\n    border-radius: 8px;\n    padding: 20px;\n    margin-bottom: 16px;\n    cursor: pointer;\n    transition: all 0.3s ease;\n\n    &:hover {\n      border-color: #409eff;\n      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);\n    }\n\n    &.selected {\n      border-color: #409eff;\n      background-color: #f0f7ff;\n      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);\n    }\n\n    &:last-child {\n      margin-bottom: 0;\n    }\n\n    .template-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 12px;\n\n      .template-title {\n        display: flex;\n        align-items: center;\n        font-size: 16px;\n        font-weight: 600;\n        color: #333;\n\n        i {\n          margin-right: 8px;\n          color: #409eff;\n        }\n      }\n\n      .template-category {\n        background: #409eff;\n        color: white;\n        padding: 4px 8px;\n        border-radius: 4px;\n        font-size: 12px;\n      }\n    }\n\n    .template-details {\n      .template-field {\n        margin-bottom: 8px;\n\n        &:last-child {\n          margin-bottom: 0;\n        }\n\n        label {\n          font-weight: 600;\n          color: #666;\n          margin-right: 8px;\n        }\n\n        span {\n          color: #333;\n        }\n\n        p {\n          margin: 4px 0 0 0;\n          color: #666;\n          line-height: 1.5;\n          font-size: 14px;\n        }\n      }\n    }\n  }\n}\n\n// 步骤切换下拉菜单样式\n.el-dropdown-menu {\n  .el-dropdown-menu__item {\n    padding: 8px 16px;\n    font-size: 13px;\n    line-height: 1.4;\n\n    i {\n      margin-right: 8px;\n      color: #666;\n    }\n\n    &.is-active {\n      background-color: #f0f7ff;\n      color: #409eff;\n      font-weight: 500;\n\n      i {\n        color: #409eff;\n      }\n    }\n\n    &:hover {\n      background-color: #f5f7fa;\n    }\n\n    &.is-active:hover {\n      background-color: #e6f4ff;\n    }\n  }\n}\n</style>\n"]}]}