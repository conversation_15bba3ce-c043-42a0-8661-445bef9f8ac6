{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue", "mtime": 1753695251423}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753065270161}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753065271554}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753065270161}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753065272328}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";AAuiCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/opinion-analysis", "sourcesContent": ["<template>\n  <div class=\"opinion-analysis\">\n    <!-- 步骤指示器 -->\n    <div class=\"steps-container\">\n      <!-- 左侧占位区域，保持布局平衡 -->\n      <div class=\"left-placeholder\"></div>\n\n      <!-- 步骤指示器 -->\n      <div class=\"steps-wrapper\">\n        <div class=\"step-item\" :class=\"{ active: currentStep === 1 }\">\n          <span class=\"step-number\">1</span>\n          <span class=\"step-text\">舆情分析来源</span>\n        </div>\n        <div class=\"step-item\" :class=\"{ active: currentStep === 2 }\">\n          <span class=\"step-number\">2</span>\n          <span class=\"step-text\">数据概览</span>\n        </div>\n        <div class=\"step-item\" :class=\"{ active: currentStep === 3 }\">\n          <span class=\"step-number\">3</span>\n          <span class=\"step-text\">分析进度</span>\n        </div>\n        <div class=\"step-item\" :class=\"{ active: currentStep === 4 }\">\n          <span class=\"step-number\">4</span>\n          <span class=\"step-text\">报告预览</span>\n        </div>\n      </div>\n\n      <!-- 右侧按钮区域 -->\n      <div class=\"right-actions\">\n        \n\n        <el-button\n          class=\"analyze-record-btn\"\n          type=\"info\"\n          size=\"small\"\n          @click=\"goToAnalyzeRecord\"\n        >\n          分析记录\n        </el-button>\n        <el-button\n          class=\"timed-push-btn\"\n          type=\"primary\"\n          size=\"small\"\n          @click=\"handleTimedPush\"\n        >\n          定时推送\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 主要内容区域 -->\n    <div class=\"main-content\">\n      <!-- 第一步：分析需求 -->\n      <div v-if=\"currentStep === 1\" class=\"analysis-source\">\n        <div class=\"section-header\">\n          <h2 class=\"section-title\">分析需求</h2>\n          <el-button\n            type=\"primary\"\n            size=\"small\"\n            @click=\"handleTemplateClick\"\n            class=\"template-btn\"\n          >\n            模板\n          </el-button>\n        </div>\n<div class=\"input-section\">\n          <div class=\"input-label\">\n            需求名称\n            <span class=\"required\">*</span>\n          </div>\n          <el-input\n            v-model=\"requirementName\"\n            placeholder=\"请输入需求名称\"\n            class=\"entity-input\"\n            :class=\"{ 'error': !requirementName.trim() && showValidation }\"\n          />\n        </div>\n        <!-- 实体关键词区域 -->\n        <div class=\"input-section\">\n          <div class=\"input-label\">\n            实体关键词\n            <span class=\"required\">*</span>\n          </div>\n          <el-input\n            v-model=\"entityKeyword\"\n            placeholder=\"请输入1个本次舆情分析最关注的实体词，例如你关注的品牌名称、产品名、人物名字等\"\n            class=\"entity-input\"\n            :class=\"{ 'error': !entityKeyword.trim() && showValidation }\"\n          />\n        </div>\n\n        <!-- 具体需求区域 -->\n        <div class=\"input-section\">\n          <div class=\"input-label\">\n            具体需求\n            <span class=\"required\">*</span>\n          </div>\n          <el-input\n            v-model=\"specificRequirement\"\n            type=\"textarea\"\n            :rows=\"4\"\n            placeholder=\"请描述你在本次分析中具体关注哪些条件，例如，分析社交媒体中与拍子空间相关的消费主题及情感倾向（正面/中性/负面）\"\n            class=\"requirement-textarea\"\n            :class=\"{ 'error': !specificRequirement.trim() && showValidation }\"\n          />\n        </div>\n\n        <!-- 选择关联词区域 -->\n        <div class=\"related-words-section\">\n          <div class=\"section-header\">\n            <div class=\"header-left\">\n              <span class=\"section-label\">选择关联词</span>\n              <span class=\"word-count\" :class=\"{ 'max-reached': selectedKeywords.length >= maxKeywords }\">\n                ({{ selectedKeywords.length }}/{{ maxKeywords }})\n              </span>\n            </div>\n            <el-button\n              v-if=\"generatedKeywords.length > 0\"\n              class=\"regenerate-btn\"\n              size=\"mini\"\n              type=\"text\"\n              @click=\"regenerateKeywords\"\n            >\n              <i class=\"el-icon-refresh\"></i>\n              重新生成\n            </el-button>\n          </div>\n\n          <div class=\"keywords-textbox-wrapper\">\n            <!-- 显示生成的关键词 -->\n            <div v-if=\"generatedKeywords.length > 0\" class=\"generated-keywords-display\">\n              <div v-for=\"(category, categoryName) in groupedKeywords\" :key=\"categoryName\" class=\"keyword-category\">\n                <el-button\n                  class=\"category-button\"\n                  size=\"small\"\n                  type=\"primary\"\n                  plain\n                  @click=\"toggleCategorySelection(categoryName, category)\"\n                >\n                  {{ categoryName }}\n                </el-button>\n                <div class=\"keyword-tags\">\n                  <el-tag\n                    v-for=\"(keyword, index) in category\"\n                    :key=\"index\"\n                    :class=\"['keyword-tag', { selected: isKeywordSelected(keyword) }]\"\n                    @click=\"toggleKeyword(keyword)\"\n                  >\n                    {{ keyword }}\n                  </el-tag>\n                </div>\n              </div>\n            </div>\n\n            <!-- 生成关联词按钮区域 -->\n            <div v-if=\"generatedKeywords.length === 0\" class=\"words-container\">\n              <div class=\"generate-word-btn\" @click=\"generateRelatedWords\">\n                <i class=\"el-icon-magic-stick\"></i>\n                <span>生成关联词</span>\n              </div>\n              <div class=\"word-description\">\n                根据你填写的需求和关键词生成关联词\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 第二步：数据概览 -->\n      <div v-if=\"currentStep === 2\" class=\"data-overview\">\n        <h2 class=\"section-title\">选择数据来源</h2>\n\n        <!-- 数据来源选项 -->\n        <div class=\"data-source-section\">\n          <!-- 联网搜索选项 -->\n          <div class=\"source-option\" @click=\"toggleOnlineSearch\">\n            <el-checkbox\n              v-model=\"enableOnlineSearch\"\n              class=\"source-checkbox\"\n            ></el-checkbox>\n            <div class=\"source-icon\">\n              <i class=\"el-icon-search\"></i>\n            </div>\n            <div class=\"source-content\">\n              <h3>联网搜索</h3>\n              <p class=\"source-description\">使用AI搜索引擎获取最新网络信息</p>\n            </div>\n          </div>\n\n          <!-- 自定义数据源搜索选项 -->\n          <div class=\"source-option\" @click=\"toggleCustomDataSource\">\n            <el-checkbox\n              v-model=\"enableCustomDataSource\"\n              class=\"source-checkbox\"\n            ></el-checkbox>\n            <div class=\"source-icon\">\n              <i class=\"el-icon-link\"></i>\n            </div>\n            <div class=\"source-content\">\n              <h3>自定义数据源搜索 <span style=\"color: #909399; font-size: 12px;\">(可选)</span></h3>\n              <p class=\"source-description\">\n                从已配置的数据源网站抓取相关信息，可与联网搜索配合使用\n                <span v-if=\"customDataSources.length > 0\" class=\"source-count\">\n                  ({{ customDataSources.length }}个数据源)\n                </span>\n              </p>\n            </div>\n          </div>\n\n          <!-- 数据源列表区域 -->\n          <div v-if=\"enableCustomDataSource\" class=\"data-source-list-section\">\n            <div class=\"list-header\">\n              <h4>数据源列表</h4>\n              <div class=\"header-controls\">\n                <el-button\n                  size=\"small\"\n                  type=\"primary\"\n                  @click=\"showAddSourceForm\"\n                >\n                  <i class=\"el-icon-plus\"></i>\n                  新增数据源\n                </el-button>\n                <el-button\n                  size=\"small\"\n                  type=\"success\"\n                  @click=\"refreshDataSourceList\"\n                >\n                  <i class=\"el-icon-refresh\"></i>\n                  刷新\n                </el-button>\n              </div>\n            </div>\n\n            <!-- 数据源表格 -->\n            <div class=\"data-source-table\">\n              <el-table\n                :data=\"paginatedDataSources\"\n                v-loading=\"dataSourceListState.loading\"\n                style=\"width: 100%\"\n                empty-text=\"暂无数据源\"\n                size=\"small\"\n                @selection-change=\"handleDataSourceSelectionChange\"\n              >\n                <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\n                <el-table-column prop=\"sourceUrl\" label=\"数据源URL\">\n                  <template slot-scope=\"scope\">\n                    <div class=\"url-cell\">\n                      <i class=\"el-icon-link\"></i>\n                      <span class=\"url-text\">{{ scope.row.sourceUrl }}</span>\n                    </div>\n                  </template>\n                </el-table-column>\n                <el-table-column label=\"操作\" width=\"80\" fixed=\"right\">\n                  <template slot-scope=\"scope\">\n                    <el-button\n                      type=\"text\"\n                      size=\"mini\"\n                      @click=\"deleteDataSource(scope.row)\"\n                      title=\"删除\"\n                      style=\"color: #f56c6c;\"\n                    >\n                      <i class=\"el-icon-delete\"></i>\n                    </el-button>\n                  </template>\n                </el-table-column>\n              </el-table>\n\n              <!-- 分页 -->\n              <div class=\"pagination-wrapper\" v-if=\"dataSourceList.length > 0\">\n                <el-pagination\n                  @current-change=\"handleDataSourcePageChange\"\n                  @size-change=\"handleDataSourceSizeChange\"\n                  :current-page=\"dataSourceListState.current_page\"\n                  :page-sizes=\"[10, 20, 50]\"\n                  :page-size=\"dataSourceListState.page_size\"\n                  :total=\"dataSourceList.length\"\n                  layout=\"total, sizes, prev, pager, next\"\n                  small\n                />\n              </div>\n            </div>\n          </div>\n\n          <!-- 新增数据源表单 -->\n          <div v-if=\"showAddSourceInput\" class=\"add-source-form\">\n            <div class=\"form-header\">\n              <h3>新增数据源</h3>\n              <i class=\"el-icon-close\" @click=\"hideAddSourceForm\"></i>\n            </div>\n            <div class=\"form-item\">\n              <label class=\"form-label\">\n                数据源网址\n                <span class=\"required\">*</span>\n              </label>\n              <div class=\"input-group\">\n                <el-input\n                  v-model=\"newSourceUrl\"\n                  placeholder=\"请输入网址，例如：https://www.example.com\"\n                  class=\"source-url-input\"\n                  @keyup.enter=\"confirmAddSource\"\n                />\n                <el-button type=\"primary\" @click=\"confirmAddSource\">确定</el-button>\n              </div>\n            </div>\n          </div>\n\n          <!-- 新增来源按钮 -->\n          <div v-if=\"!showAddSourceInput\" class=\"add-source-btn\" @click=\"showAddSourceForm\">\n            <i class=\"el-icon-plus\"></i>\n            <span>新增来源</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- 第三步：分析进度 -->\n      <div v-if=\"currentStep === 3\" class=\"analysis-progress\">\n        <h2 class=\"section-title\">分析进度</h2>\n\n        <!-- 分析状态概览 -->\n        <div class=\"progress-overview\">\n          <div class=\"status-card\">\n            <div class=\"status-header\">\n              <h3>当前状态</h3>\n              <div class=\"status-indicator\" :class=\"analysisStatus\">\n                <span class=\"status-dot\"></span>\n                <span class=\"status-text\">{{ getAnalysisStatusText() }}</span>\n              </div>\n            </div>\n            <div class=\"progress-bar-container\">\n              <div class=\"progress-bar\">\n                <div class=\"progress-fill\" :style=\"{ width: analysisProgress + '%' }\"></div>\n              </div>\n              <span class=\"progress-text\">{{ analysisProgress }}%</span>\n            </div>\n          </div>\n        </div>\n\n        <!-- 实时日志显示 -->\n        <div class=\"real-time-logs\">\n          <div class=\"logs-header\">\n            <h3>分析日志</h3>\n            <div class=\"logs-controls\">\n              <el-button size=\"mini\" @click=\"clearLogs\" type=\"text\">清空日志</el-button>\n              <el-button size=\"mini\" @click=\"toggleAutoScroll\" type=\"text\">\n                {{ autoScroll ? '停止滚动' : '自动滚动' }}\n              </el-button>\n            </div>\n          </div>\n          <div class=\"logs-container\" ref=\"logsContainer\">\n            <div v-if=\"analysisLogs.length === 0\" class=\"no-logs\">\n              <i class=\"el-icon-loading\"></i>\n              <span>等待分析开始...</span>\n            </div>\n            <div v-else class=\"logs-list\">\n              <div\n                v-for=\"(log, index) in analysisLogs\"\n                :key=\"index\"\n                class=\"log-item\"\n                :class=\"log.level\"\n              >\n                <span class=\"log-time\">{{ formatLogTime(log.timestamp) }}</span>\n                <span class=\"log-level\">{{ (log.level || 'info').toUpperCase() }}</span>\n                <span class=\"log-message\">{{ log.message }}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 分析完成后的操作 -->\n        <div v-if=\"analysisStatus === 'completed'\" class=\"analysis-completed\">\n          <div class=\"completion-message\">\n            <i class=\"el-icon-success\"></i>\n            <span>分析已完成！</span>\n          </div>\n          <el-button type=\"primary\" size=\"large\" @click=\"goToReportPreview\">\n            查看分析报告\n          </el-button>\n        </div>\n\n        <!-- 分析失败时的操作 -->\n        <div v-if=\"analysisStatus === 'failed'\" class=\"analysis-failed\">\n          <div class=\"failure-message\">\n            <i class=\"el-icon-error\"></i>\n            <span>分析失败，请重试</span>\n          </div>\n          <el-button type=\"danger\" size=\"large\" @click=\"retryAnalysis\">\n            重新分析\n          </el-button>\n        </div>\n      </div>\n\n      <!-- 第四步：报告预览 -->\n      <div v-if=\"currentStep === 4\" class=\"report-preview\">\n        <h2 class=\"section-title\">分析报告预览</h2>\n\n        <!-- 报告概览 -->\n        <div class=\"report-overview\">\n          <div class=\"overview-card\">\n            <div class=\"card-header\">\n              <h3>分析概览</h3>\n              <span class=\"analysis-time\">{{ formatAnalysisTime(new Date()) }}</span>\n            </div>\n            <div class=\"overview-stats\">\n              <div class=\"stat-item\">\n                <div class=\"stat-number\">{{ reportData.totalArticles || 0 }}</div>\n                <div class=\"stat-label\">相关文章</div>\n              </div>\n              <div class=\"stat-item\">\n                <div class=\"stat-number\">{{ reportData.totalKeywords || selectedKeywords.length }}</div>\n                <div class=\"stat-label\">关键词</div>\n              </div>\n              <div class=\"stat-item\">\n                <div class=\"stat-number\">{{ reportData.dataSources || (enableOnlineSearch ? 1 : 0) + customDataSources.length }}</div>\n                <div class=\"stat-label\">数据源</div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 情感分析结果 -->\n        <div class=\"sentiment-analysis\">\n          <div class=\"analysis-card\">\n            <h3>情感倾向分析</h3>\n            <div class=\"sentiment-chart\">\n              <div class=\"sentiment-item positive\">\n                <div class=\"sentiment-bar\">\n                  <div class=\"bar-fill\" :style=\"{ width: (reportData.sentiment?.positive || 0) + '%' }\"></div>\n                </div>\n                <div class=\"sentiment-info\">\n                  <span class=\"sentiment-label\">正面</span>\n                  <span class=\"sentiment-value\">{{ reportData.sentiment?.positive || 0 }}%</span>\n                </div>\n              </div>\n              <div class=\"sentiment-item neutral\">\n                <div class=\"sentiment-bar\">\n                  <div class=\"bar-fill\" :style=\"{ width: (reportData.sentiment?.neutral || 0) + '%' }\"></div>\n                </div>\n                <div class=\"sentiment-info\">\n                  <span class=\"sentiment-label\">中性</span>\n                  <span class=\"sentiment-value\">{{ reportData.sentiment?.neutral || 0 }}%</span>\n                </div>\n              </div>\n              <div class=\"sentiment-item negative\">\n                <div class=\"sentiment-bar\">\n                  <div class=\"bar-fill\" :style=\"{ width: (reportData.sentiment?.negative || 0) + '%' }\"></div>\n                </div>\n                <div class=\"sentiment-info\">\n                  <span class=\"sentiment-label\">负面</span>\n                  <span class=\"sentiment-value\">{{ reportData.sentiment?.negative || 0 }}%</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 关键词分析 -->\n        <div class=\"keyword-analysis\">\n          <div class=\"analysis-card\">\n            <h3>关键词分析</h3>\n            <div class=\"selected-keywords-display\">\n              <div class=\"keyword-list\">\n                <div class=\"keyword-item\" v-for=\"(keyword, index) in selectedKeywords\" :key=\"index\">\n                  <span class=\"keyword-text\">{{ keyword }}</span>\n                  <span class=\"keyword-frequency\">{{ getKeywordFrequency(keyword) }}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 数据来源统计 -->\n        <div class=\"data-source-stats\">\n          <div class=\"analysis-card\">\n            <h3>数据来源统计</h3>\n            <div class=\"source-list\">\n              <div v-if=\"enableOnlineSearch\" class=\"source-item\">\n                <div class=\"source-icon online\">\n                  <i class=\"el-icon-search\"></i>\n                </div>\n                <div class=\"source-info\">\n                  <div class=\"source-name\">联网搜索</div>\n                  <div class=\"source-desc\">AI搜索引擎数据</div>\n                </div>\n                <div class=\"source-count\">{{ reportData.onlineSearchCount || 0 }} 条</div>\n              </div>\n              <div v-for=\"(source, index) in customDataSources\" :key=\"index\" class=\"source-item\">\n                <div class=\"source-icon custom\">\n                  <i class=\"el-icon-link\"></i>\n                </div>\n                <div class=\"source-info\">\n                  <div class=\"source-name\">{{ extractDomainName(source) }}</div>\n                  <div class=\"source-desc\">自定义数据源</div>\n                </div>\n                <div class=\"source-count\">{{ getSourceCount(source) }} 条</div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 详细数据 -->\n        <div class=\"detailed-data\" v-if=\"allArticles.length > 0\">\n          <div class=\"analysis-card\">\n            <div class=\"card-header-with-controls\">\n              <h3>详细数据</h3>\n              <div class=\"data-controls\">\n                <el-input\n                  v-model=\"articleListState.searchKeyword\"\n                  placeholder=\"搜索文章标题或内容\"\n                  prefix-icon=\"el-icon-search\"\n                  size=\"small\"\n                  style=\"width: 200px; margin-right: 12px;\"\n                  clearable\n                />\n                <el-select\n                  v-model=\"articleListState.selectedSource\"\n                  placeholder=\"筛选来源\"\n                  size=\"small\"\n                  style=\"width: 120px; margin-right: 12px;\"\n                  clearable\n                >\n                  <el-option label=\"全部来源\" value=\"\"></el-option>\n                  <el-option\n                    v-for=\"source in uniqueSources\"\n                    :key=\"source\"\n                    :label=\"source\"\n                    :value=\"source\"\n                  ></el-option>\n                </el-select>\n                <el-select\n                  v-model=\"articleListState.selectedSentiment\"\n                  placeholder=\"筛选情感\"\n                  size=\"small\"\n                  style=\"width: 100px;\"\n                  clearable\n                >\n                  <el-option label=\"全部情感\" value=\"\"></el-option>\n                  <el-option label=\"正面\" value=\"positive\"></el-option>\n                  <el-option label=\"中性\" value=\"neutral\"></el-option>\n                  <el-option label=\"负面\" value=\"negative\"></el-option>\n                </el-select>\n              </div>\n            </div>\n\n            <div class=\"article-list\">\n              <div\n                v-for=\"(article, index) in paginatedArticles\"\n                :key=\"`article-${index}`\"\n                class=\"article-item\"\n              >\n                <div class=\"article-header\">\n                  <div class=\"article-title-row\">\n                    <h4 class=\"article-title\" @click=\"toggleArticleExpand(index)\">\n                      {{ article.title }}\n                      <i :class=\"isArticleExpanded(index) ? 'el-icon-arrow-up' : 'el-icon-arrow-down'\"></i>\n                    </h4>\n                    <div class=\"article-meta\">\n                      <span class=\"article-source\">{{ article.source }}</span>\n                      <span :class=\"['sentiment-tag', article.sentiment]\">\n                        {{ getSentimentLabel(article.sentiment) }}\n                      </span>\n                    </div>\n                  </div>\n                  <div class=\"article-info\">\n                    <span class=\"publish-time\" v-if=\"article.publish_time\">\n                      {{ formatPublishTime(article.publish_time) }}\n                    </span>\n                    <div class=\"article-actions\">\n                      <el-button\n                        type=\"text\"\n                        size=\"mini\"\n                        icon=\"el-icon-copy-document\"\n                        @click=\"copyArticleContent(article)\"\n                      >\n                        复制\n                      </el-button>\n                      <el-button\n                        v-if=\"article.url && article.url.trim()\"\n                        type=\"text\"\n                        size=\"mini\"\n                        icon=\"el-icon-link\"\n                        @click=\"openArticleUrl(article.url)\"\n                        :title=\"getUrlTooltip(article.url)\"\n                      >\n                        原文链接\n                      </el-button>\n                    </div>\n                  </div>\n                </div>\n\n                <div class=\"article-content\" v-if=\"!isArticleExpanded(index)\">\n                  <p class=\"content-summary\">{{ getContentSummary(article.content) }}</p>\n                </div>\n\n                <div class=\"article-content expanded\" v-if=\"isArticleExpanded(index)\">\n                  <p class=\"content-full\">{{ article.content }}</p>\n                </div>\n              </div>\n            </div>\n\n            <!-- 分页 -->\n            <div class=\"pagination-wrapper\" v-if=\"filteredArticles.length > 0\">\n              <el-pagination\n                @current-change=\"handlePageChange\"\n                :current-page=\"articleListState.currentPage\"\n                :page-size=\"articleListState.pageSize\"\n                :total=\"filteredArticles.length\"\n                layout=\"prev, pager, next, total\"\n                small\n              />\n            </div>\n          </div>\n        </div>\n\n        <!-- 报告操作 -->\n        <div class=\"report-actions\">\n          <div class=\"action-buttons\">\n            <el-button size=\"large\" icon=\"el-icon-download\">导出报告</el-button>\n            <el-button size=\"large\" icon=\"el-icon-share\">分享报告</el-button>\n            <el-button type=\"primary\" size=\"large\" icon=\"el-icon-s-promotion\">生成完整报告</el-button>\n          </div>\n        </div>\n      </div>\n\n      <!-- 底部按钮区域 -->\n      <div class=\"bottom-actions\">\n        <el-button v-if=\"currentStep === 2 || currentStep === 4\" @click=\"goToPreviousStep\" size=\"large\">上一步</el-button>\n        <el-button\n          v-if=\"currentStep === 1\"\n          @click=\"goToNextStep\"\n          type=\"primary\"\n          size=\"large\"\n          :disabled=\"!canGoToNextStep\"\n        >下一步</el-button>\n        <el-button v-if=\"currentStep === 2\" @click=\"startAnalysis\" type=\"primary\" size=\"large\">开始分析</el-button>\n        <el-button v-if=\"currentStep === 3 && analysisStatus === 'running'\" @click=\"cancelAnalysis\" size=\"large\">取消分析</el-button>\n        <el-button v-if=\"currentStep === 4\" @click=\"showPushDialog\" type=\"primary\" size=\"large\">\n          <i class=\"el-icon-s-promotion\"></i>\n          推送报告\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 定时任务抽屉 -->\n    <el-drawer\n      title=\"定时任务\"\n      :visible.sync=\"timedTaskDialogVisible\"\n      direction=\"rtl\"\n      size=\"600px\"\n      :before-close=\"closeTimedTaskDialog\"\n      custom-class=\"timed-task-drawer\"\n    >\n      <!-- 抽屉头部右侧按钮 -->\n      <div slot=\"title\" class=\"drawer-header\">\n        <span class=\"drawer-title\">定时任务</span>\n        <el-button\n          type=\"primary\"\n          size=\"mini\"\n          icon=\"el-icon-plus\"\n          @click=\"handleAddTimedTask\"\n          class=\"add-task-btn\"\n        >\n          定时任务\n        </el-button>\n      </div>\n\n      <!-- 抽屉内容 -->\n      <div class=\"drawer-content\">\n        <!-- 空状态 -->\n        <div v-if=\"timedTaskList.length === 0\" class=\"empty-state\">\n          <div class=\"empty-content\">\n            <!-- 空状态图标 -->\n            <div class=\"empty-icon\">\n              <svg width=\"120\" height=\"120\" viewBox=\"0 0 120 120\" fill=\"none\">\n                <!-- 文件夹图标 -->\n                <path d=\"M20 30h25l5-10h50v70H20V30z\" fill=\"#f0f0f0\" stroke=\"#d0d0d0\" stroke-width=\"2\"/>\n                <path d=\"M25 35h70v50H25V35z\" fill=\"#fafafa\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n                <!-- 文档图标 -->\n                <rect x=\"35\" y=\"45\" width=\"30\" height=\"25\" fill=\"#ffffff\" stroke=\"#d0d0d0\" stroke-width=\"1\" rx=\"2\"/>\n                <rect x=\"70\" y=\"50\" width=\"20\" height=\"15\" fill=\"#ffffff\" stroke=\"#d0d0d0\" stroke-width=\"1\" rx=\"2\"/>\n                <!-- 装饰线条 -->\n                <line x1=\"40\" y1=\"52\" x2=\"60\" y2=\"52\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n                <line x1=\"40\" y1=\"57\" x2=\"55\" y2=\"57\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n                <line x1=\"40\" y1=\"62\" x2=\"58\" y2=\"62\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n                <line x1=\"75\" y1=\"55\" x2=\"85\" y2=\"55\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n                <line x1=\"75\" y1=\"60\" x2=\"82\" y2=\"60\" stroke=\"#e0e0e0\" stroke-width=\"1\"/>\n              </svg>\n            </div>\n            <p class=\"empty-text\">暂无定时任务</p>\n            <el-button type=\"primary\" @click=\"handleCreateTimedTask\" class=\"create-btn\">\n              去创建\n            </el-button>\n          </div>\n        </div>\n\n        <!-- 任务列表 -->\n        <div v-else class=\"task-list\">\n          <div v-if=\"timedTaskList.length === 0\" class=\"empty-task-list\">\n            <div class=\"empty-icon\">📅</div>\n            <div class=\"empty-text\">暂无定时任务</div>\n            <el-button type=\"primary\" size=\"small\" @click=\"handleAddTimedTask\" class=\"add-task-btn\">\n              添加任务\n            </el-button>\n          </div>\n          <div v-else class=\"task-items\">\n            <div v-for=\"(task, index) in timedTaskList\" :key=\"index\" class=\"task-item\">\n              <div class=\"task-info\">\n                <div class=\"task-header\">\n                  <div class=\"task-name\">{{ task.name }}</div>\n                  <div class=\"task-status\" :class=\"{ 'status-running': task.status === 'running', 'status-pending': task.status === 'pending' }\">\n                    {{ task.status === 'running' ? '运行中' : '待运行' }}\n                  </div>\n                </div>\n                <!-- 任务描述已隐藏 -->\n                <div class=\"task-schedule\">\n                  <i class=\"el-icon-time\"></i>\n                  <span>{{ getTaskScheduleText(task) }}</span>\n                </div>\n              </div>\n              <div class=\"task-actions\">\n                <el-button type=\"text\" size=\"mini\" @click=\"previewTask(index)\" title=\"预览任务详情\">\n                  <i class=\"el-icon-view\"></i>\n                </el-button>\n                <el-button type=\"text\" size=\"mini\" @click=\"toggleTaskStatus(index)\" :title=\"task.status === 'running' ? '暂停任务' : '启动任务'\">\n                  <i :class=\"task.status === 'running' ? 'el-icon-video-pause' : 'el-icon-video-play'\"></i>\n                </el-button>\n                <el-button type=\"text\" size=\"mini\" @click=\"editTask(index)\" title=\"编辑任务\">\n                  <i class=\"el-icon-edit\"></i>\n                </el-button>\n                <el-button type=\"text\" size=\"mini\" @click=\"deleteTask(index)\" title=\"删除任务\">\n                  <i class=\"el-icon-delete\"></i>\n                </el-button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 创建/编辑任务弹窗 -->\n      <el-dialog\n        :title=\"editingTaskIndex === -1 ? '创建定时任务' : '编辑定时任务'\"\n        :visible.sync=\"createTaskDialogVisible\"\n        width=\"500px\"\n        :before-close=\"closeCreateTaskDialog\"\n        :append-to-body=\"true\"\n        class=\"create-task-dialog\"\n      >\n        <div class=\"task-form\">\n          <!-- 任务需求 -->\n          <div class=\"task-requirement-section\">\n            <div class=\"section-label\">\n              任务需求\n              <span class=\"required\">*</span>\n            </div>\n                  <div class=\"form-group\">\n              <div class=\"input-label\">需求名称</div>\n              <el-select\n                v-model=\"taskForm.requirementId\"\n                placeholder=\"请选择需求\"\n                class=\"task-name-input\"\n                style=\"width: 100%\"\n              >\n                <el-option\n                  v-for=\"requirement in requirementList\"\n                  :key=\"requirement.id\"\n                  :label=\"requirement.requirementName\"\n                  :value=\"requirement.id\"\n                />\n              </el-select>\n            </div>\n            <div class=\"form-group\">\n              <div class=\"input-label\">任务名称</div>\n              <el-input\n                v-model=\"taskForm.name\"\n                placeholder=\"请输入任务名称\"\n                class=\"task-name-input\"\n              />\n            </div>\n            <div class=\"form-group\">\n              <div class=\"input-label\">任务描述</div>\n              <el-input\n                v-model=\"taskForm.description\"\n                type=\"textarea\"\n                :rows=\"3\"\n                placeholder=\"请描述具体的任务需求，例如：基于当前分析需求自动生成AI新闻总结\"\n                class=\"task-description-input\"\n              />\n                    <div class=\"form-group\">\n              <div class=\"input-label\">推送地址</div>\n              <el-input\n                v-model=\"taskForm.pushUrl\"\n                placeholder=\"例如：https://www.baidu.com\"\n                class=\"task-name-input\"\n              />\n            </div>\n            </div>\n          </div>\n\n          <!-- 执行时间 -->\n          <div class=\"execute-time-section\">\n            <div class=\"section-label\">执行时间</div>\n            <div class=\"time-selector\">\n              <el-select v-model=\"taskForm.frequency\" placeholder=\"选择频率\" class=\"frequency-select\">\n                <el-option label=\"仅一次\" value=\"once\"></el-option>\n                <el-option label=\"每天\" value=\"daily\"></el-option>\n                <el-option label=\"每周\" value=\"weekly\"></el-option>\n                <el-option label=\"每月\" value=\"monthly\"></el-option>\n              </el-select>\n              <!-- 一次性任务：选择具体日期时间 -->\n              <el-date-picker\n                v-if=\"taskForm.frequency === 'once'\"\n                v-model=\"taskForm.executeDateTime\"\n                type=\"datetime\"\n                placeholder=\"选择执行日期和时间\"\n                format=\"yyyy-MM-dd HH:mm\"\n                value-format=\"yyyy-MM-dd HH:mm\"\n                class=\"datetime-picker\"\n                :picker-options=\"{\n                  disabledDate(time) {\n                    return time.getTime() < Date.now() - 8.64e7\n                  }\n                }\"\n              />\n              <!-- 周期性任务：选择时间 -->\n              <el-time-picker\n                v-else\n                v-model=\"taskForm.executeTime\"\n                format=\"HH:mm\"\n                value-format=\"HH:mm\"\n                placeholder=\"选择时间\"\n                class=\"time-picker\"\n              />\n            </div>\n          </div>\n        </div>\n\n        <!-- 底部按钮 -->\n        <div slot=\"footer\" class=\"dialog-footer\">\n          <el-button @click=\"modifyPlan\" class=\"modify-btn\">修改计划</el-button>\n          <el-button type=\"primary\" @click=\"saveAndRunTask\" class=\"run-btn\">保存并运行</el-button>\n          <el-button type=\"success\" @click=\"saveTaskPlan\" class=\"save-btn\">保存计划</el-button>\n        </div>\n      </el-dialog>\n    </el-drawer>\n\n    <!-- 推送报告弹窗 -->\n    <el-dialog\n      title=\"推送报告\"\n      :visible.sync=\"pushReportDialog.visible\"\n      width=\"500px\"\n      center\n      :close-on-click-modal=\"false\"\n      :close-on-press-escape=\"false\"\n      @close=\"hidePushDialog\"\n    >\n      <el-form :model=\"pushReportDialog\" label-width=\"80px\">\n        <el-form-item label=\"目标URL\" required>\n          <el-input\n            v-model=\"pushReportDialog.url\"\n            placeholder=\"请输入推送目标URL地址\"\n            clearable\n            :disabled=\"pushReportDialog.loading\"\n          >\n            <template slot=\"prepend\">https://</template>\n          </el-input>\n          <div class=\"form-tip\">\n            <i class=\"el-icon-info\"></i>\n            将推送包含报告页面链接的消息，接收方可点击链接查看完整报告<br>\n            <strong>支持所有地址格式：</strong><br>\n            • 钉钉机器人：https://oapi.dingtalk.com/robot/send?access_token=xxx<br>\n            • 企业微信：https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=xxx<br>\n            • 飞书机器人：https://open.feishu.cn/open-apis/bot/v2/hook/xxx<br>\n            • 普通HTTP接口：https://your-domain.com/api/webhook<br>\n            • 本地地址：localhost:3000/webhook 或 127.0.0.1:8080/api<br>\n            • 测试地址：httpbin.org/post\n          </div>\n        </el-form-item>\n      </el-form>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"hidePushDialog\" :disabled=\"pushReportDialog.loading\">\n          取消\n        </el-button>\n        <el-button\n          type=\"default\"\n          @click=\"savePushPlan\"\n          :disabled=\"pushReportDialog.loading\"\n        >\n          保存计划\n        </el-button>\n        <el-button\n          type=\"primary\"\n          @click=\"directPushReport\"\n          :loading=\"pushReportDialog.loading\"\n          :disabled=\"!pushReportDialog.url.trim()\"\n        >\n          直接推送\n        </el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 任务预览弹窗 -->\n    <el-dialog\n      title=\"任务详情预览\"\n      :visible.sync=\"taskPreviewDialog.visible\"\n      width=\"600px\"\n      center\n      :close-on-click-modal=\"false\"\n      :close-on-press-escape=\"false\"\n      @close=\"hideTaskPreviewDialog\"\n      class=\"task-preview-dialog\"\n    >\n      <div v-if=\"taskPreviewDialog.taskData\" class=\"task-preview-content\">\n        <!-- 基本信息 -->\n        <div class=\"preview-section\">\n          <h3 class=\"section-title\">\n            <i class=\"el-icon-info\"></i>\n            基本信息\n          </h3>\n          <div class=\"info-grid\">\n            <div class=\"info-item\">\n              <label>任务名称：</label>\n              <span>{{ taskPreviewDialog.taskData.name }}</span>\n            </div>\n            <div class=\"info-item\">\n              <label>任务状态：</label>\n              <el-tag :type=\"taskPreviewDialog.taskData.status === 'running' ? 'success' : 'info'\">\n                {{ taskPreviewDialog.taskData.status === 'running' ? '运行中' : '待运行' }}\n              </el-tag>\n            </div>\n            <div class=\"info-item\">\n              <label>需求ID：</label>\n              <span>{{ taskPreviewDialog.taskData.requirementId || '未关联' }}</span>\n            </div>\n          </div>\n        </div>\n\n        <!-- 任务描述 -->\n        <div class=\"preview-section\" v-if=\"taskPreviewDialog.taskData.description\">\n          <h3 class=\"section-title\">\n            <i class=\"el-icon-document\"></i>\n            任务描述\n          </h3>\n          <div class=\"description-content\">\n            {{ taskPreviewDialog.taskData.description }}\n          </div>\n        </div>\n\n        <!-- 执行计划 -->\n        <div class=\"preview-section\">\n          <h3 class=\"section-title\">\n            <i class=\"el-icon-time\"></i>\n            执行计划\n          </h3>\n          <div class=\"info-grid\">\n            <div class=\"info-item\">\n              <label>执行频率：</label>\n              <span>{{ getFrequencyText(taskPreviewDialog.taskData.frequency) }}</span>\n            </div>\n            <div class=\"info-item\">\n              <label>执行时间：</label>\n              <span>{{ taskPreviewDialog.taskData.executeTime }}</span>\n            </div>\n          </div>\n        </div>\n\n        <!-- 推送配置 -->\n        <div class=\"preview-section\" v-if=\"taskPreviewDialog.taskData.pushUrl\">\n          <h3 class=\"section-title\">\n            <i class=\"el-icon-s-promotion\"></i>\n            推送配置\n          </h3>\n          <div class=\"push-config\">\n            <div class=\"info-item\">\n              <label>推送地址：</label>\n              <div class=\"url-display\">\n                <span class=\"url-text\">{{ getMaskedUrl(taskPreviewDialog.taskData.pushUrl) }}</span>\n                <el-button\n                  type=\"text\"\n                  size=\"mini\"\n                  @click=\"copyToClipboard(taskPreviewDialog.taskData.pushUrl)\"\n                  title=\"复制完整地址\"\n                >\n                  <i class=\"el-icon-copy-document\"></i>\n                </el-button>\n              </div>\n            </div>\n            <div class=\"info-item\">\n              <label>推送类型：</label>\n              <el-tag size=\"small\">{{ getPushTypeText(taskPreviewDialog.taskData.pushUrl) }}</el-tag>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"hideTaskPreviewDialog\">关闭</el-button>\n        <el-button\n          type=\"primary\"\n          @click=\"editTaskFromPreview\"\n          v-if=\"taskPreviewDialog.taskData\"\n        >\n          编辑任务\n        </el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 模板选择弹窗 -->\n    <el-dialog\n      title=\"选择分析模板\"\n      :visible.sync=\"templateDialog.visible\"\n      width=\"700px\"\n      center\n      :close-on-click-modal=\"false\"\n      :close-on-press-escape=\"false\"\n      @close=\"closeTemplateDialog\"\n      class=\"template-dialog\"\n    >\n      <div class=\"template-content\">\n        <div class=\"template-list\">\n          <div\n            v-for=\"template in templateList\"\n            :key=\"template.id\"\n            class=\"template-item\"\n            :class=\"{ 'selected': templateDialog.selectedTemplate === template.id }\"\n            @click=\"templateDialog.selectedTemplate = template.id\"\n          >\n            <div class=\"template-header\">\n              <div class=\"template-title\">\n                <i class=\"el-icon-document\"></i>\n                {{ template.name }}\n              </div>\n              <div class=\"template-category\">{{ template.category }}</div>\n            </div>\n            <div class=\"template-details\">\n              <div class=\"template-field\">\n                <label>实体关键词：</label>\n                <span>{{ template.entityKeyword }}</span>\n              </div>\n              <div class=\"template-field\">\n                <label>具体需求：</label>\n                <p>{{ template.requirement }}</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"closeTemplateDialog\">取消</el-button>\n        <el-button\n          type=\"primary\"\n          @click=\"applyTemplate(templateList.find(t => t.id === templateDialog.selectedTemplate))\"\n          :disabled=\"!templateDialog.selectedTemplate\"\n        >\n          应用模板\n        </el-button>\n      </div>\n    </el-dialog>\n\n  </div>\n</template>\n\n<script>\nimport {\n  getRequirementList,\n  createRequirement,\n  updateRequirement,\n  deleteRequirement,\n  getKeywordCategories,\n  getDataSourceList,\n  createDataSource,\n  deleteDataSource as deleteDataSourceAPI,\n  getTaskStatistics,\n  getDataSourceStatistics,\n  getTimedTaskList,\n  getTimedTaskDetail,\n  createTimedTask,\n  updateTimedTask,\n  updateTaskStatus,\n  deleteTimedTask,\n  getTasksByRequirement,\n  performOnlineSearch,\n  startAnalysis,\n  pushReport,\n  getPublicReportData,\n  checkTaskExists,\n  generateRelatedKeywords,\n  createAnalysisProgressTask,\n  addProgressLog,\n  getAnalysisProgress,\n  completeAnalysisTask,\n  cancelAnalysisTask,\n  generateAndUploadReport\n} from '@/api/opinion-analysis'\n\nimport {\n  getTemplatesForSelection,\n  updateTemplateUsage\n} from '@/api/opinion-template'\n\nimport { getDashboardStatistics, getCurrentMonthAnalysisCount } from '@/api/dashboard'\n\nexport default {\n  name: 'OpinionAnalysis',\n  data() {\n    return {\n      currentStep: 1, // 当前步骤\n      requirementName: '', // 需求名称\n      entityKeyword: '', // 实体关键词\n      specificRequirement: '', // 具体需求\n      selectedKeywords: [], // 已选择的关键词\n      generatedKeywords: [], // 生成的所有关键词\n      maxKeywords: 5, // 最大选择数量\n      enableOnlineSearch: true, // 是否启用联网搜索\n      enableCustomDataSource: false, // 是否启用自定义数据源搜索（默认关闭，可选）\n      customDataSources: [], // 自定义数据源URL列表\n      showAddSourceInput: false, // 显示新增数据源表单\n      newSourceUrl: '', // 新增数据源URL\n      // 数据源列表相关状态\n      dataSourceList: [], // 数据源列表数据\n      selectedDataSources: [], // 选中的数据源\n      dataSourceListState: {\n        loading: false,\n        current_page: 1,\n        page_size: 10\n      },\n      showValidation: false, // 是否显示验证错误样式\n      timedTaskDialogVisible: false, // 定时任务抽屉显示状态\n      timedTaskList: [], // 定时任务列表\n      createTaskDialogVisible: false, // 创建任务弹窗显示状态\n      editingTaskIndex: -1, // 当前编辑的任务索引，-1表示新建任务\n      taskForm: {\n        requirementId: '', // 需求ID\n        name: '', // 任务名称\n        description: '',\n        executeTime: '16:00',\n        executeDateTime: '', // 一次性任务的执行日期时间\n        frequency: 'daily',\n        pushUrl: '' // 推送地址\n      },\n      requirementList: [], // 需求列表\n      keywordCategories: [], // 关键词分类列表\n      currentRequirementId: null, // 当前需求的ID\n      requirementSaved: false, // 需求是否已保存到数据库\n      requirementModified: false, // 需求是否被修改（需要更新）\n      reportData: { // 报告数据\n        totalArticles: 0,\n        totalKeywords: 0,\n        dataSources: 0,\n        sentiment: {\n          positive: 0,\n          neutral: 0,\n          negative: 0\n        },\n        onlineSearchCount: 0,\n        customSourceCounts: {}\n      },\n      analysisResults: null, // 完整的分析结果数据\n      // 文章列表相关状态\n      articleListState: {\n        currentPage: 1,\n        pageSize: 10,\n        searchKeyword: '',\n        selectedSource: '',\n        selectedSentiment: '',\n        expandedArticles: new Set() // 存储展开的文章ID\n      },\n      // 推送报告相关状态\n      pushReportDialog: {\n        visible: false,\n        url: '',\n        loading: false\n      },\n      // 任务预览弹窗状态\n      taskPreviewDialog: {\n        visible: false,\n        taskData: null,\n        loading: false\n      },\n      // 模板弹窗状态\n      templateDialog: {\n        visible: false,\n        selectedTemplate: null\n      },\n      // 模板数据（从数据库获取）\n      templateList: [],\n      // 分析进度相关状态\n      analysisStatus: 'idle', // 分析状态：idle-空闲，running-运行中，completed-已完成，failed-失败\n      analysisProgress: 0, // 分析进度百分比\n      analysisLogs: [], // 分析日志列表\n      autoScroll: true, // 是否自动滚动日志\n      currentTaskId: null, // 当前分析任务ID\n      websocket: null, // WebSocket连接\n      heartbeatTimer: null, // 心跳定时器\n      reconnectAttempts: 0, // 重连尝试次数\n      maxReconnectAttempts: 5, // 最大重连次数\n      reconnectTimer: null, // 重连定时器\n      statusPollingTimer: null, // 状态轮询定时器\n      // HTTP轮询相关状态\n      httpPollingTimers: new Map(), // 存储多个轮询定时器\n      pollingConfigs: new Map(), // 存储轮询配置\n      // 报告OSS相关状态\n      reportOssUrl: null, // 报告的OSS访问URL\n      reportPageId: null // 报告页面ID\n    }\n  },\n  computed: {\n    // 检查是否可以进入下一步\n    canGoToNextStep() {\n      // 检查需求名称是否填写\n      if (!this.requirementName.trim()) {\n        return false\n      }\n      \n      // 检查实体关键词是否填写\n      if (!this.entityKeyword.trim()) {\n        return false\n      }\n\n      // 检查具体需求是否填写\n      if (!this.specificRequirement.trim()) {\n        return false\n      }\n\n      // 检查是否至少选择了一个关键词\n      if (this.selectedKeywords.length === 0) {\n        return false\n      }\n\n      return true\n    },\n\n    // 获取所有文章数据\n    allArticles() {\n      if (!this.analysisResults || !this.analysisResults.analysis_results) {\n        return []\n      }\n\n      const articles = []\n      const results = this.analysisResults.analysis_results\n\n      // 添加联网搜索结果\n      if (results.online_search && results.online_search.data && results.online_search.data.articles) {\n        articles.push(...results.online_search.data.articles.map(article => ({\n          ...article,\n          sourceType: 'online_search'\n        })))\n      }\n\n      // 添加自定义数据源结果\n      if (results.custom_data_source && results.custom_data_source.data && results.custom_data_source.data.articles) {\n        articles.push(...results.custom_data_source.data.articles.map(article => ({\n          ...article,\n          sourceType: 'custom_data_source'\n        })))\n      }\n\n      return articles\n    },\n\n    // 获取所有唯一的数据源\n    uniqueSources() {\n      const sources = new Set()\n      this.allArticles.forEach(article => {\n        if (article.source) {\n          sources.add(article.source)\n        }\n      })\n      return Array.from(sources)\n    },\n\n    // 筛选后的文章列表\n    filteredArticles() {\n      let filtered = this.allArticles\n\n      // 按搜索关键词筛选\n      if (this.articleListState.searchKeyword) {\n        const keyword = this.articleListState.searchKeyword.toLowerCase()\n        filtered = filtered.filter(article =>\n          (article.title && article.title.toLowerCase().includes(keyword)) ||\n          (article.content && article.content.toLowerCase().includes(keyword))\n        )\n      }\n\n      // 按来源筛选\n      if (this.articleListState.selectedSource) {\n        filtered = filtered.filter(article => article.source === this.articleListState.selectedSource)\n      }\n\n      // 按情感筛选\n      if (this.articleListState.selectedSentiment) {\n        filtered = filtered.filter(article => article.sentiment === this.articleListState.selectedSentiment)\n      }\n\n      return filtered\n    },\n\n    // 分页后的文章列表\n    paginatedArticles() {\n      const start = (this.articleListState.currentPage - 1) * this.articleListState.pageSize\n      const end = start + this.articleListState.pageSize\n      return this.filteredArticles.slice(start, end)\n    },\n\n    // 分页后的数据源列表\n    paginatedDataSources() {\n      const start = (this.dataSourceListState.current_page - 1) * this.dataSourceListState.page_size\n      const end = start + this.dataSourceListState.page_size\n      return this.dataSourceList.slice(start, end)\n    },\n\n    // 将关键词按分类分组\n    groupedKeywords() {\n      if (this.generatedKeywords.length === 0) {\n        return {}\n      }\n\n      // 使用从API获取的分类，如果没有则使用默认分类\n      const categories = this.keywordCategories.length > 0\n        ? this.keywordCategories.map(cat => ({ name: cat.category_name, keywords: [] }))\n        : [\n            { name: '售后服务问题', keywords: [] },\n            { name: '产品质量问题', keywords: [] },\n            { name: '投诉处理结果', keywords: [] },\n            { name: '消费者不满', keywords: [] },\n            { name: '虚假宣传', keywords: [] }\n          ]\n\n      this.generatedKeywords.forEach(keyword => {\n        let assigned = false\n\n        categories.forEach(cat => {\n          if (cat.name === '售后服务问题' && (keyword.includes('售后') || keyword.includes('服务') || keyword.includes('客服'))) {\n            cat.keywords.push(keyword)\n            assigned = true\n          } else if (cat.name === '产品质量问题' && (keyword.includes('质量') || keyword.includes('爆炸') || keyword.includes('故障'))) {\n            cat.keywords.push(keyword)\n            assigned = true\n          } else if (cat.name === '投诉处理结果' && (keyword.includes('投诉') || keyword.includes('处理') || keyword.includes('对解'))) {\n            cat.keywords.push(keyword)\n            assigned = true\n          } else if (cat.name === '消费者不满' && (keyword.includes('不满') || keyword.includes('消费者'))) {\n            cat.keywords.push(keyword)\n            assigned = true\n          } else if (cat.name === '虚假宣传' && (keyword.includes('宣传') || keyword.includes('充好'))) {\n            cat.keywords.push(keyword)\n            assigned = true\n          }\n        })\n\n        if (!assigned) {\n          // 如果没有匹配的分类，添加到第一个有关键词的分类或创建新分类\n          if (categories[0].keywords.length === 0) {\n            categories[0].keywords.push(keyword)\n          } else {\n            categories.find(cat => cat.keywords.length > 0).keywords.push(keyword)\n          }\n        }\n      })\n\n      // 只返回有关键词的分类\n      const result = {}\n      categories.forEach(cat => {\n        if (cat.keywords.length > 0) {\n          result[cat.name] = cat.keywords\n        }\n      })\n\n      return result\n    }\n  },\n  watch: {\n    // 监听需求信息变化，重置保存状态\n    requirementName() {\n      this.resetRequirementSaveStatus()\n    },\n    entityKeyword() {\n      this.resetRequirementSaveStatus()\n    },\n    specificRequirement() {\n      this.resetRequirementSaveStatus()\n    },\n    // 监听筛选条件变化，重置分页到第一页\n    'articleListState.searchKeyword'() {\n      this.articleListState.currentPage = 1\n    },\n    'articleListState.selectedSource'() {\n      this.articleListState.currentPage = 1\n    },\n    'articleListState.selectedSentiment'() {\n      this.articleListState.currentPage = 1\n    }\n  },\n  mounted() {\n    // 页面初始化逻辑\n    console.log('🚀 舆情分析页面已加载')\n    this.debugLog('页面初始化', '舆情分析页面开始加载')\n\n    // 显示调试信息\n    console.log('🔧 调试模式已启用，可使用以下方法:')\n    console.log('  - this.clearDebugLogs() : 清除调试日志')\n    console.log('  - this.exportDebugLogs() : 导出调试日志')\n    console.log('  - localStorage.getItem(\"opinion_analysis_debug_logs\") : 查看调试日志')\n\n    // 加载关键词分类\n    this.loadKeywordCategories()\n\n    // 注释掉页面加载时的定时任务列表加载，改为点击定时推送按钮时才加载\n    // this.loadTimedTaskList()\n\n    // 注释掉页面加载时的数据源列表加载，改为进入第二步或启用自定义数据源时才加载\n    // this.loadDataSourceList()\n\n    // 注释掉页面加载时的模板列表加载，改为点击模板按钮时才加载\n    // this.loadTemplateList()\n\n    // 检查URL参数，如果有报告参数则自动加载\n    this.parseUrlParams()\n\n    this.debugLog('页面初始化', '舆情分析页面加载完成')\n  },\n\n  beforeDestroy() {\n    // 页面销毁前清理所有HTTP轮询\n    this.stopAllHttpPolling()\n    console.log('🧹 页面销毁，已清理所有HTTP轮询')\n  },\n  methods: {\n    // ==================== HTTP轮询相关方法 ====================\n\n    /**\n     * 启动HTTP轮询\n     * @param {string} pollingId - 轮询标识符，用于管理多个轮询任务\n     * @param {Function} requestFunction - 执行HTTP请求的函数，应返回Promise\n     * @param {Object} options - 轮询配置选项\n     * @param {number} options.interval - 轮询间隔（毫秒），默认3000ms\n     * @param {number} options.maxAttempts - 最大轮询次数，默认无限制\n     * @param {Function} options.onSuccess - 成功回调函数\n     * @param {Function} options.onError - 错误回调函数\n     * @param {Function} options.shouldStop - 停止条件判断函数，返回true时停止轮询\n     * @param {boolean} options.immediate - 是否立即执行第一次请求，默认true\n     */\n    startHttpPolling(pollingId, requestFunction, options = {}) {\n      const config = {\n        interval: 3000, // 默认3秒轮询一次\n        maxAttempts: 0, // 0表示无限制\n        onSuccess: null,\n        onError: null,\n        shouldStop: null,\n        immediate: true,\n        ...options\n      }\n\n      // 如果已存在相同ID的轮询，先停止它\n      this.stopHttpPolling(pollingId)\n\n      // 保存轮询配置\n      this.pollingConfigs.set(pollingId, {\n        ...config,\n        requestFunction,\n        attempts: 0,\n        isRunning: true\n      })\n\n      console.log(`🔄 启动HTTP轮询: ${pollingId}`, config)\n\n      // 轮询执行函数\n      const executePolling = async () => {\n        const pollingConfig = this.pollingConfigs.get(pollingId)\n        if (!pollingConfig || !pollingConfig.isRunning) {\n          return\n        }\n\n        try {\n          pollingConfig.attempts++\n          console.log(`📡 执行轮询请求: ${pollingId} (第${pollingConfig.attempts}次)`)\n\n          // 执行HTTP请求\n          const result = await requestFunction()\n\n          // 执行成功回调\n          if (config.onSuccess) {\n            config.onSuccess(result, pollingConfig.attempts)\n          }\n\n          // 检查是否应该停止轮询\n          if (config.shouldStop && config.shouldStop(result)) {\n            console.log(`✅ 轮询停止条件满足: ${pollingId}`)\n            this.stopHttpPolling(pollingId)\n            return\n          }\n\n          // 检查是否达到最大尝试次数\n          if (config.maxAttempts > 0 && pollingConfig.attempts >= config.maxAttempts) {\n            console.log(`⏰ 轮询达到最大次数: ${pollingId}`)\n            this.stopHttpPolling(pollingId)\n            return\n          }\n\n          // 设置下次轮询\n          if (pollingConfig.isRunning) {\n            const timer = setTimeout(executePolling, config.interval)\n            this.httpPollingTimers.set(pollingId, timer)\n          }\n\n        } catch (error) {\n          console.error(`❌ 轮询请求失败: ${pollingId}`, error)\n\n          // 执行错误回调\n          if (config.onError) {\n            const shouldContinue = config.onError(error, pollingConfig.attempts)\n            if (shouldContinue === false) {\n              console.log(`🛑 错误回调要求停止轮询: ${pollingId}`)\n              this.stopHttpPolling(pollingId)\n              return\n            }\n          }\n\n          // 继续下次轮询（除非明确要求停止）\n          if (pollingConfig.isRunning) {\n            const timer = setTimeout(executePolling, config.interval)\n            this.httpPollingTimers.set(pollingId, timer)\n          }\n        }\n      }\n\n      // 立即执行第一次请求或延迟执行\n      if (config.immediate) {\n        executePolling()\n      } else {\n        const timer = setTimeout(executePolling, config.interval)\n        this.httpPollingTimers.set(pollingId, timer)\n      }\n    },\n\n    /**\n     * 停止指定的HTTP轮询\n     * @param {string} pollingId - 轮询标识符\n     */\n    stopHttpPolling(pollingId) {\n      // 清除定时器\n      if (this.httpPollingTimers.has(pollingId)) {\n        clearTimeout(this.httpPollingTimers.get(pollingId))\n        this.httpPollingTimers.delete(pollingId)\n      }\n\n      // 标记轮询为停止状态\n      if (this.pollingConfigs.has(pollingId)) {\n        const config = this.pollingConfigs.get(pollingId)\n        config.isRunning = false\n        console.log(`🛑 停止HTTP轮询: ${pollingId} (共执行${config.attempts}次)`)\n      }\n    },\n\n    /**\n     * 停止所有HTTP轮询\n     */\n    stopAllHttpPolling() {\n      console.log('🛑 停止所有HTTP轮询')\n      for (const pollingId of this.httpPollingTimers.keys()) {\n        this.stopHttpPolling(pollingId)\n      }\n      this.httpPollingTimers.clear()\n      this.pollingConfigs.clear()\n    },\n\n    /**\n     * 获取轮询状态信息\n     * @param {string} pollingId - 轮询标识符\n     * @returns {Object|null} 轮询状态信息\n     */\n    getPollingStatus(pollingId) {\n      if (!this.pollingConfigs.has(pollingId)) {\n        return null\n      }\n\n      const config = this.pollingConfigs.get(pollingId)\n      return {\n        pollingId,\n        isRunning: config.isRunning,\n        attempts: config.attempts,\n        interval: config.interval,\n        maxAttempts: config.maxAttempts\n      }\n    },\n\n    /**\n     * 获取所有轮询状态\n     * @returns {Array} 所有轮询的状态信息\n     */\n    getAllPollingStatus() {\n      const statusList = []\n      for (const pollingId of this.pollingConfigs.keys()) {\n        statusList.push(this.getPollingStatus(pollingId))\n      }\n      return statusList\n    },\n\n    // ==================== 轮询应用示例方法 ====================\n\n    /**\n     * 轮询分析进度（主要通信方式，替代WebSocket）\n     */\n    startAnalysisProgressPolling() {\n      if (!this.currentTaskId) {\n        console.warn('⚠️ 没有当前任务ID，无法启动进度轮询')\n        return\n      }\n\n      console.log('🔄 启动分析进度HTTP轮询，任务ID:', this.currentTaskId)\n\n      this.startHttpPolling('analysisProgress', async () => {\n        // 调用获取分析进度的API\n        const response = await getAnalysisProgress(this.currentTaskId)\n        return response.data\n      }, {\n        interval: 1500, // 每1.5秒轮询一次，提高实时性\n        immediate: true, // 立即执行第一次请求\n        onSuccess: (data, attempts) => {\n          console.log(`📊 分析进度更新 (第${attempts}次):`, data)\n\n          // 进度条增长机制：每次轮询时自动增加1%（从当前进度开始）\n          if (this.analysisProgress < 95) { // 限制在95%以下，为最终完成留出空间\n            this.analysisProgress = Math.min(this.analysisProgress + 1, 95)\n            console.log(`🔄 进度条自动递增至: ${this.analysisProgress}%`)\n          }\n\n          // 更新进度数据（如果API返回了具体进度，优先使用API数据）\n          if (data.progress !== undefined && data.progress > this.analysisProgress) {\n            this.analysisProgress = Math.min(data.progress, 95) // 同样限制在95%\n            this.addLog('info', `分析进度: ${data.progress}%`)\n          }\n\n          if (data.status && data.status !== this.analysisStatus) {\n            this.analysisStatus = data.status\n            this.addLog('info', `状态变更: ${data.status}`)\n\n            // 状态变更时的特殊处理\n            if (data.status === 'completed') {\n              // 轮询完成处理：立即将进度条跳转到100%\n              this.analysisProgress = 100\n              this.addLog('success', '✅ 分析任务已完成！')\n              this.$message.success('分析完成！')\n              // 立即停止轮询，避免继续无意义的请求\n              this.stopHttpPolling('analysisProgress')\n              this.addLog('info', '分析完成，已停止进度轮询')\n            } else if (data.status === 'failed') {\n              this.addLog('error', '❌ 分析任务失败')\n              this.$message.error('分析失败，请重试')\n              // 失败时也停止轮询\n              this.stopHttpPolling('analysisProgress')\n              this.addLog('info', '分析失败，已停止进度轮询')\n            }\n          }\n\n          // 更新日志\n          if (data.logs && Array.isArray(data.logs)) {\n            // 只添加新的日志条目，并确保每个日志都有正确的level字段\n            const newLogs = data.logs\n              .filter(log => {\n                // 过滤空日志：检查message是否为空或只包含空白字符\n                if (!log.message || log.message.trim() === '') {\n                  return false\n                }\n                // 过滤重复日志\n                return !this.analysisLogs.some(existingLog =>\n                  existingLog.timestamp === log.timestamp &&\n                  existingLog.message === log.message\n                )\n              })\n              .map(log => ({\n                ...log,\n                level: log.level || 'info', // 确保level字段存在\n                message: log.message || '', // 确保message字段存在\n                timestamp: log.timestamp || new Date().toISOString() // 确保timestamp字段存在\n              }))\n            this.analysisLogs.push(...newLogs)\n\n            // 自动滚动到最新日志\n            if (this.autoScroll && newLogs.length > 0) {\n              this.$nextTick(() => {\n                this.scrollToBottom()\n              })\n            }\n          }\n\n          // 更新分析结果数据\n          if (data.analysis_results) {\n            this.analysisResults = data\n            this.updateReportData(data.analysis_results)\n          }\n        },\n        onError: (error, attempts) => {\n          console.error(`❌ 获取分析进度失败 (第${attempts}次):`, error)\n\n          // 添加错误日志\n          this.addLog('error', `获取进度失败: ${error.message || '网络错误'}`)\n\n          // 根据错误类型和尝试次数决定是否继续\n          if (error.response?.status === 404) {\n            this.addLog('error', '任务不存在，停止轮询')\n            this.$message.error('分析任务不存在')\n            return false // 停止轮询\n          }\n\n          if (error.response?.status === 401) {\n            this.addLog('error', '认证失败，请重新登录')\n            this.$message.error('认证失败，请重新登录')\n            return false // 停止轮询\n          }\n\n          // 如果连续失败8次，停止轮询\n          if (attempts >= 8) {\n            this.addLog('error', '连续获取进度失败，停止轮询')\n            this.$message.error('获取分析进度失败，请刷新页面重试')\n            this.analysisStatus = 'failed'\n            return false // 停止轮询\n          }\n\n          return true // 继续轮询\n        },\n        shouldStop: (data) => {\n          // 当分析完成或失败时停止轮询\n          const shouldStop = data.status === 'completed' || data.status === 'failed'\n          if (shouldStop) {\n            console.log('🛑 分析进度轮询停止，最终状态:', data.status)\n\n            // 轮询完成处理：无论当前进度是多少，立即将进度条跳转到100%\n            if (data.status === 'completed') {\n              this.analysisProgress = 100\n              console.log('✅ 轮询结束，进度条已跳转到100%')\n            }\n          }\n          return shouldStop\n        }\n      })\n    },\n\n    /**\n     * 示例：轮询任务状态\n     */\n    startTaskStatusPolling(taskId) {\n      this.startHttpPolling(`taskStatus_${taskId}`, async () => {\n        // 这里调用获取任务状态的API\n        const response = await getTimedTaskDetail(taskId)\n        return response.data\n      }, {\n        interval: 5000, // 每5秒轮询一次\n        maxAttempts: 60, // 最多轮询60次（5分钟）\n        onSuccess: (data) => {\n          console.log('任务状态更新:', data)\n          // 更新任务状态到界面\n        },\n        shouldStop: (data) => {\n          // 当任务完成时停止轮询\n          return data.status === 'completed' || data.status === 'failed'\n        }\n      })\n    },\n\n    /**\n     * 示例：轮询数据源状态\n     */\n    startDataSourcePolling() {\n      this.startHttpPolling('dataSourceStatus', async () => {\n        const response = await getDataSourceList()\n        return response.data\n      }, {\n        interval: 10000, // 每10秒轮询一次\n        onSuccess: (data) => {\n          // 更新数据源列表\n          if (data && Array.isArray(data.list)) {\n            this.dataSourceList = data.list\n          }\n        },\n        onError: (error, attempts) => {\n          console.error('获取数据源列表失败:', error)\n          if (attempts >= 3) {\n            this.$message.warning('数据源状态更新失败，请手动刷新')\n            return false // 停止轮询\n          }\n          return true // 继续轮询\n        }\n      })\n    },\n\n    /**\n     * 示例：轮询报告生成状态\n     */\n    startReportGenerationPolling(reportId) {\n      this.startHttpPolling(`reportGeneration_${reportId}`, async () => {\n        // 这里应该调用获取报告生成状态的API\n        const response = await getPublicReportData(reportId)\n        return response.data\n      }, {\n        interval: 3000, // 每3秒轮询一次\n        maxAttempts: 100, // 最多轮询100次（5分钟）\n        onSuccess: (data) => {\n          console.log('报告生成状态更新:', data)\n          if (data.status === 'completed') {\n            this.$message.success('报告生成完成！')\n            // 更新报告数据\n            this.reportData = data\n          }\n        },\n        onError: (error, attempts) => {\n          console.error('获取报告状态失败:', error)\n          if (attempts >= 5) {\n            this.$message.error('报告生成状态获取失败')\n            return false\n          }\n          return true\n        },\n        shouldStop: (data) => {\n          return data.status === 'completed' || data.status === 'failed'\n        }\n      })\n    },\n\n    /**\n     * 通用轮询方法：轮询API直到满足条件\n     * @param {string} apiFunction - API函数名\n     * @param {Array} apiParams - API参数数组\n     * @param {Function} successCondition - 成功条件判断函数\n     * @param {Object} options - 轮询选项\n     */\n    pollUntilCondition(apiFunction, apiParams = [], successCondition, options = {}) {\n      const pollingId = `generic_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`\n\n      this.startHttpPolling(pollingId, async () => {\n        // 动态调用API函数\n        const apiFunc = this[apiFunction] || window[apiFunction]\n        if (typeof apiFunc === 'function') {\n          return await apiFunc(...apiParams)\n        } else {\n          throw new Error(`API函数 ${apiFunction} 不存在`)\n        }\n      }, {\n        interval: options.interval || 2000,\n        maxAttempts: options.maxAttempts || 50,\n        onSuccess: (data) => {\n          if (options.onProgress) {\n            options.onProgress(data)\n          }\n        },\n        onError: (error, attempts) => {\n          console.error(`轮询API ${apiFunction} 失败:`, error)\n          if (options.onError) {\n            return options.onError(error, attempts)\n          }\n          return attempts < 5 // 默认失败5次后停止\n        },\n        shouldStop: successCondition\n      })\n\n      return pollingId // 返回轮询ID，便于外部控制\n    },\n\n    // 处理测试步骤切换\n    handleStepSwitch(step) {\n      const stepNumber = parseInt(step)\n      console.log(`🔄 测试切换到第${stepNumber}步`)\n\n      // 直接切换步骤，不进行验证（测试用）\n      this.currentStep = stepNumber\n\n      // 如果切换到第二步（数据概览），加载数据源列表\n      if (stepNumber === 2) {\n        this.loadDataSourceList()\n      }\n\n      // 显示切换成功消息\n      this.$message({\n        message: `已切换到第${stepNumber}步：${this.getStepName(stepNumber)}`,\n        type: 'success',\n        duration: 2000\n      })\n\n      // 记录调试日志\n      this.debugLog('步骤切换', `测试切换到第${stepNumber}步`, {\n        fromStep: this.currentStep,\n        toStep: stepNumber\n      })\n    },\n\n    // 获取步骤名称\n    getStepName(step) {\n      const stepNames = {\n        1: '舆情分析来源',\n        2: '数据概览',\n        3: '分析进度',\n        4: '报告预览'\n      }\n      return stepNames[step] || '未知步骤'\n    },\n\n    // 跳转到分析记录页面\n    goToAnalyzeRecord() {\n      this.$router.push('/analyze-record')\n    },\n\n    // 处理模板按钮点击\n    handleTemplateClick() {\n      this.templateDialog.visible = true\n      this.templateDialog.selectedTemplate = null\n      // 每次打开模板弹窗时重新加载模板数据\n      this.loadTemplateList()\n      console.log('模板弹窗已打开')\n    },\n\n    // 加载模板列表\n    async loadTemplateList() {\n      try {\n        const response = await getTemplatesForSelection()\n        if (response.code === 200) {\n          // 转换数据格式以适配现有的模板显示逻辑\n          // API返回的是下划线格式，需要正确映射字段名\n          this.templateList = response.data.map(template => ({\n            id: template.id,\n            name: template.template_name,\n            entityKeyword: template.entity_keyword,\n            requirement: template.specific_requirement,\n            category: template.template_category,\n            priority: template.priority,\n            usageCount: template.usage_count\n          }))\n          console.log('✅ 模板列表加载成功:', this.templateList.length, '个模板')\n          console.log('📋 模板数据:', this.templateList)\n        } else {\n          console.error('❌ 加载模板列表失败:', response.msg)\n          this.$message.error('加载模板列表失败: ' + response.msg)\n        }\n      } catch (error) {\n        console.error('❌ 加载模板列表异常:', error)\n        this.$message.error('加载模板列表失败，请稍后重试')\n      }\n    },\n\n    // 关闭模板弹窗\n    closeTemplateDialog() {\n      this.templateDialog.visible = false\n      this.templateDialog.selectedTemplate = null\n    },\n\n    // 应用模板\n    async applyTemplate(template) {\n      if (!template) {\n        this.$message.error('请选择一个模板')\n        return\n      }\n\n      try {\n        // 应用模板数据到表单\n        this.requirementName = template.name\n        this.entityKeyword = template.entityKeyword\n        this.specificRequirement = template.requirement\n\n        // 更新模板使用次数\n        await updateTemplateUsage(template.id, 1)\n\n        this.closeTemplateDialog()\n        this.$message.success('模板已应用')\n\n        console.log('✅ 模板应用成功:', template.name)\n      } catch (error) {\n        console.error('❌ 应用模板失败:', error)\n        // 即使更新使用次数失败，也要应用模板\n        this.closeTemplateDialog()\n        this.$message.success('模板已应用')\n      }\n    },\n\n    // 调试日志辅助方法\n    debugLog(tag, message, data = null) {\n      const timestamp = new Date().toISOString()\n      const logMessage = `[${timestamp}] ${tag}: ${message}`\n      console.log(logMessage)\n      if (data) {\n        console.log(`[${timestamp}] ${tag} - 数据:`, data)\n      }\n\n      // 可选：将日志保存到localStorage用于调试\n      try {\n        const logs = JSON.parse(localStorage.getItem('opinion_analysis_debug_logs') || '[]')\n        logs.push({\n          timestamp,\n          tag,\n          message,\n          data: data ? JSON.stringify(data, null, 2) : null\n        })\n        // 只保留最近100条日志\n        if (logs.length > 100) {\n          logs.splice(0, logs.length - 100)\n        }\n        localStorage.setItem('opinion_analysis_debug_logs', JSON.stringify(logs))\n      } catch (e) {\n        console.warn('保存调试日志失败:', e)\n      }\n    },\n\n    // 生成随机字符后缀，确保需求名称唯一性\n    generateRandomSuffix() {\n      // 生成时间戳（格式：YYYYMMDDHHMMSS）\n      const now = new Date()\n      const timestamp = now.getFullYear().toString() +\n        (now.getMonth() + 1).toString().padStart(2, '0') +\n        now.getDate().toString().padStart(2, '0') +\n        now.getHours().toString().padStart(2, '0') +\n        now.getMinutes().toString().padStart(2, '0') +\n        now.getSeconds().toString().padStart(2, '0')\n\n      // 生成随机字符串（6位字母数字组合）\n      const chars = 'abcdefghijklmnopqrstuvwxyz0123456789'\n      let randomStr = ''\n      for (let i = 0; i < 6; i++) {\n        randomStr += chars.charAt(Math.floor(Math.random() * chars.length))\n      }\n\n      return `_${timestamp}_${randomStr}`\n    },\n\n    // 为需求名称添加随机后缀\n    addRandomSuffixToRequirementName(originalName) {\n      const suffix = this.generateRandomSuffix()\n      const finalName = originalName + suffix\n\n      console.log('🎯 [需求名称] 原始名称:', originalName)\n      console.log('🎯 [需求名称] 生成后缀:', suffix)\n      console.log('🎯 [需求名称] 最终名称:', finalName)\n\n      return finalName\n    },\n\n    // 清除调试日志\n    clearDebugLogs() {\n      localStorage.removeItem('opinion_analysis_debug_logs')\n      console.log('🧹 调试日志已清除')\n    },\n\n    // 导出调试日志\n    exportDebugLogs() {\n      try {\n        const logs = JSON.parse(localStorage.getItem('opinion_analysis_debug_logs') || '[]')\n        const logText = logs.map(log =>\n          `[${log.timestamp}] ${log.tag}: ${log.message}${log.data ? '\\n数据: ' + log.data : ''}`\n        ).join('\\n\\n')\n\n        const blob = new Blob([logText], { type: 'text/plain' })\n        const url = URL.createObjectURL(blob)\n        const a = document.createElement('a')\n        a.href = url\n        a.download = `opinion_analysis_debug_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`\n        a.click()\n        URL.revokeObjectURL(url)\n\n        console.log('📄 调试日志已导出')\n      } catch (e) {\n        console.error('导出调试日志失败:', e)\n      }\n    },\n\n    // ==================== 分析进度相关方法 ====================\n\n    // 获取分析状态文本\n    getAnalysisStatusText() {\n      const statusMap = {\n        idle: '等待开始',\n        running: '分析中',\n        generating_report: '正在生成报告',\n        completed: '已完成',\n        failed: '分析失败'\n      }\n\n      // 特殊逻辑：如果进度条已达到100%但分析结果尚未完全生成，显示\"正在生成报告\"\n      if (this.analysisStatus === 'running' && this.analysisProgress >= 100) {\n        return '正在生成报告'\n      }\n\n      return statusMap[this.analysisStatus] || '未知状态'\n    },\n\n    // 格式化日志时间\n    formatLogTime(timestamp) {\n      if (!timestamp) return ''\n      const date = new Date(timestamp)\n      return date.toLocaleTimeString('zh-CN', {\n        hour12: false,\n        hour: '2-digit',\n        minute: '2-digit',\n        second: '2-digit'\n      })\n    },\n\n    // 清空日志\n    clearLogs() {\n      this.analysisLogs = []\n      this.$message.success('日志已清空')\n    },\n\n    // 切换自动滚动\n    toggleAutoScroll() {\n      this.autoScroll = !this.autoScroll\n      if (this.autoScroll) {\n        this.$nextTick(() => {\n          this.scrollToBottom()\n        })\n      }\n    },\n\n    // 滚动到日志底部\n    scrollToBottom() {\n      if (this.$refs.logsContainer) {\n        this.$refs.logsContainer.scrollTop = this.$refs.logsContainer.scrollHeight\n      }\n    },\n\n    // 添加日志\n    addLog(level, message) {\n      const log = {\n        timestamp: new Date().toISOString(),\n        level: level || 'info', // 确保level不为空\n        message: message || '' // 确保message不为空\n      }\n      this.analysisLogs.push(log)\n\n      // 限制日志数量，避免内存溢出\n      if (this.analysisLogs.length > 1000) {\n        this.analysisLogs.splice(0, this.analysisLogs.length - 1000)\n      }\n\n      // 自动滚动到底部\n      if (this.autoScroll) {\n        this.$nextTick(() => {\n          this.scrollToBottom()\n        })\n      }\n    },\n\n    // 分析完成后自动生成报告并上传到OSS\n    async generateAndUploadReportAfterAnalysis(analysisData) {\n      try {\n        this.addLog('info', '正在生成分析报告并上传到OSS...')\n\n        // 确保有需求ID\n        if (!this.currentRequirementId) {\n          await this.ensureRequirementExists()\n          if (!this.currentRequirementId) {\n            throw new Error('无法获取需求ID，报告生成失败')\n          }\n        }\n\n        // 准备报告数据\n        const reportData = {\n          requirement_id: this.currentRequirementId,\n          requirement_name: this.requirementName,\n          entity_keyword: this.entityKeyword,\n          specific_requirement: this.specificRequirement,\n          selected_keywords: this.selectedKeywords,\n          analysis_results: analysisData.analysis_results || {},\n          report_data: {\n            totalArticles: this.reportData.totalArticles,\n            totalKeywords: this.reportData.totalKeywords,\n            dataSources: this.reportData.dataSources,\n            sentiment: this.reportData.sentiment,\n            onlineSearchCount: this.reportData.onlineSearchCount,\n            customSourceCounts: this.reportData.customSourceCounts\n          },\n          enable_online_search: this.enableOnlineSearch,\n          enable_custom_data_source: this.enableCustomDataSource,\n          custom_data_sources: this.customDataSources\n        }\n\n        // 调用后端API生成报告并上传到OSS\n        const response = await generateAndUploadReport(reportData)\n\n        if (response.success && response.data) {\n          const { report_oss_url, page_id } = response.data\n          this.addLog('success', `报告已生成并上传到OSS: ${report_oss_url}`)\n\n          // 保存OSS URL到当前组件状态，供后续使用\n          this.reportOssUrl = report_oss_url\n          this.reportPageId = page_id\n\n          // 保存分析任务到数据库，包含OSS URL\n          await this.saveTaskForPush('', 'immediate')\n\n          return { success: true, oss_url: report_oss_url, page_id }\n        } else {\n          throw new Error(response.msg || '报告生成失败')\n        }\n      } catch (error) {\n        console.error('生成报告并上传到OSS失败:', error)\n        this.addLog('error', '报告生成失败: ' + error.message)\n        this.$message.warning('报告生成失败，但仍可查看本地报告')\n        return { success: false, error: error.message }\n      }\n    },\n\n    // 跳转到报告预览\n    async goToReportPreview() {\n      try {\n        // 在跳转到报告预览页面前，先保存分析任务到数据库\n        this.addLog('info', '正在保存分析报告任务...')\n\n        // 使用immediate类型保存分析报告任务，但不提供push_url\n        const taskResult = await this.saveTaskForPush('', 'immediate')\n        if (taskResult.success) {\n          if (taskResult.exists) {\n            this.addLog('info', '分析报告任务已存在')\n          } else {\n            this.addLog('success', '分析报告任务保存成功')\n            this.$message.success('分析报告已保存')\n          }\n        } else {\n          this.addLog('warning', '分析报告任务保存失败，但仍可查看报告')\n          this.$message.warning('任务保存失败，但仍可查看报告')\n        }\n\n        // 跳转到报告预览页面\n        this.currentStep = 4\n      } catch (error) {\n        console.error('保存分析报告任务失败:', error)\n        this.addLog('error', '分析报告任务保存失败: ' + error.message)\n        this.$message.warning('任务保存失败，但仍可查看报告')\n\n        // 即使保存失败，仍然允许用户查看报告\n        this.currentStep = 4\n      }\n    },\n\n    // 重试分析\n    retryAnalysis() {\n      this.disconnectWebSocket()\n      this.stopStatusPolling()\n      this.analysisStatus = 'idle'\n      this.analysisProgress = 0\n      this.analysisLogs = []\n      this.currentTaskId = null\n      this.currentStep = 2\n    },\n\n    // 取消分析\n    async cancelAnalysis() {\n      try {\n        if (this.currentTaskId) {\n          await cancelAnalysisTask(this.currentTaskId)\n          this.addLog('warning', '分析任务已取消')\n        }\n\n        // 停止HTTP轮询\n        this.stopHttpPolling('analysisProgress')\n        this.addLog('info', '已停止分析进度轮询')\n\n        this.analysisStatus = 'idle'\n        this.analysisProgress = 0\n        this.currentTaskId = null\n        this.$message.info('分析已取消')\n        this.currentStep = 2\n      } catch (error) {\n        console.error('取消分析失败:', error)\n        this.$message.error('取消分析失败')\n      }\n    },\n\n    // ==================== WebSocket相关方法 ====================\n\n    // 获取WebSocket连接URL\n    getWebSocketUrl(taskId) {\n      // 根据环境动态配置WebSocket地址\n      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'\n\n      // 获取WebSocket主机地址\n      let host\n      if (process.env.VUE_APP_WS_HOST) {\n        // 使用环境变量配置的主机地址\n        host = process.env.VUE_APP_WS_HOST\n      } else {\n        // 回退到当前域名\n        host = window.location.host\n      }\n\n      // 构建完整的WebSocket URL\n      const wsUrl = `${protocol}//${host}/ws/analysis-progress/${taskId}`\n      return wsUrl\n    },\n\n    // 连接WebSocket\n    connectWebSocket(taskId) {\n      if (this.websocket) {\n        this.websocket.close()\n      }\n\n      // 动态构建WebSocket URL\n      const wsUrl = this.getWebSocketUrl(taskId)\n      console.log('连接WebSocket:', wsUrl)\n\n      try {\n        this.websocket = new WebSocket(wsUrl)\n\n        this.websocket.onopen = () => {\n          console.log('WebSocket连接已建立:', taskId)\n          this.addLog('info', 'WebSocket连接已建立')\n\n          // 重置重连计数\n          this.reconnectAttempts = 0\n          this.stopReconnect()\n\n          // 启动心跳机制\n          this.startHeartbeat()\n        }\n\n        this.websocket.onmessage = (event) => {\n          try {\n            const data = JSON.parse(event.data)\n            this.handleWebSocketMessage(data)\n          } catch (error) {\n            console.error('解析WebSocket消息失败:', error)\n          }\n        }\n\n        this.websocket.onclose = (event) => {\n          console.log('WebSocket连接已关闭', event.code, event.reason)\n          this.websocket = null\n\n          // 如果不是正常关闭且分析正在进行，尝试重连\n          if (event.code !== 1000 && this.analysisStatus === 'running' && this.reconnectAttempts < this.maxReconnectAttempts) {\n            this.attemptReconnect(taskId)\n          }\n        }\n\n        this.websocket.onerror = (error) => {\n          console.error('WebSocket连接错误:', error)\n          this.addLog('error', `WebSocket连接错误: ${error.message || '未知错误'}`)\n\n          // 尝试重连\n          if (this.analysisStatus === 'running' && this.reconnectAttempts < this.maxReconnectAttempts) {\n            this.attemptReconnect(taskId)\n          }\n        }\n\n      } catch (error) {\n        console.error('创建WebSocket连接失败:', error)\n        this.addLog('error', '创建WebSocket连接失败: ' + error.message)\n      }\n    },\n\n    // 断开WebSocket连接\n    disconnectWebSocket() {\n      this.stopHeartbeat()\n      this.stopReconnect()\n      if (this.websocket) {\n        this.websocket.close()\n        this.websocket = null\n      }\n      this.reconnectAttempts = 0\n    },\n\n    // 尝试重连\n    attemptReconnect(taskId) {\n      if (this.reconnectTimer) {\n        return // 已经在重连中\n      }\n\n      this.reconnectAttempts++\n      const delay = Math.min(1000 * Math.pow(1.5, this.reconnectAttempts - 1), 15000) // 指数退避，最大30秒\n\n      this.addLog('warning', `WebSocket连接断开，${delay/1000}秒后尝试第${this.reconnectAttempts}次重连...`)\n\n      this.reconnectTimer = setTimeout(() => {\n        this.reconnectTimer = null\n        console.log(`尝试第${this.reconnectAttempts}次重连...`)\n        this.connectWebSocket(taskId)\n      }, delay)\n    },\n\n    // 停止重连\n    stopReconnect() {\n      if (this.reconnectTimer) {\n        clearTimeout(this.reconnectTimer)\n        this.reconnectTimer = null\n      }\n    },\n\n    // 启动心跳机制\n    startHeartbeat() {\n      this.stopHeartbeat()\n      this.heartbeatTimer = setInterval(() => {\n        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {\n          this.websocket.send(JSON.stringify({ type: 'ping' }))\n        }\n      }, 30000) // 每30秒发送一次心跳\n    },\n\n    // 停止心跳机制\n    stopHeartbeat() {\n      if (this.heartbeatTimer) {\n        clearInterval(this.heartbeatTimer)\n        this.heartbeatTimer = null\n      }\n    },\n\n    // 处理WebSocket消息\n    handleWebSocketMessage(data) {\n      console.log('收到WebSocket消息:', data)\n\n      switch (data.type) {\n        case 'connection_established':\n          this.addLog('info', data.message)\n          break\n\n        case 'progress_update':\n          this.handleProgressUpdate(data.data)\n          break\n\n        case 'log_message':\n          this.handleLogMessage(data.data)\n          break\n\n        case 'task_status_change':\n          this.handleTaskStatusChange(data.status, data.data)\n          break\n\n        case 'initial_progress':\n          this.handleInitialProgress(data.data)\n          break\n\n        case 'pong':\n          // 心跳响应，保持连接活跃\n          console.log('收到心跳响应:', data.timestamp)\n          break\n\n        case 'error':\n          this.addLog('error', data.message)\n          break\n\n        default:\n          console.log('未知的WebSocket消息类型:', data.type)\n      }\n    },\n\n    // 处理进度更新\n    handleProgressUpdate(data) {\n      if (data.progress_percentage !== undefined) {\n        this.analysisProgress = data.progress_percentage\n      }\n    },\n\n    // 处理日志消息\n    handleLogMessage(data) {\n      this.addLog(data.log_level, data.log_message)\n    },\n\n    // 处理任务状态变更\n    handleTaskStatusChange(status, data) {\n      this.analysisStatus = status\n\n      if (status === 'completed') {\n        this.analysisProgress = 100\n        this.addLog('success', '分析任务已完成！')\n        this.$message.success('分析完成！可以查看报告了')\n\n        // 移除自动跳转逻辑，让用户手动点击\"查看报告\"按钮\n        // setTimeout(() => {\n        //   this.goToReportPreview()\n        // }, 2000)\n      } else if (status === 'failed') {\n        this.addLog('error', '分析任务失败: ' + (data.error_message || '未知错误'))\n        this.$message.error('分析失败，请重试')\n      } else if (status === 'cancelled') {\n        this.addLog('warning', '分析任务已取消')\n        this.$message.info('分析已取消')\n      }\n    },\n\n    // 处理初始进度\n    handleInitialProgress(data) {\n      if (data.task) {\n        this.analysisStatus = data.task.task_status\n        this.analysisProgress = data.task.progress_percentage || 0\n      }\n\n      if (data.logs && data.logs.length > 0) {\n        this.analysisLogs = data.logs.map(log => ({\n          timestamp: log.create_time,\n          level: log.log_level,\n          message: log.log_message\n        }))\n      }\n    },\n\n    // ==================== 状态轮询相关方法 ====================\n\n    // 启动状态轮询\n    startStatusPolling() {\n      this.stopStatusPolling()\n      this.statusPollingTimer = setInterval(async () => {\n        if (this.currentTaskId && this.analysisStatus === 'running') {\n          await this.checkTaskStatus()\n        }\n      }, 5000) // 每5秒检查一次状态\n    },\n\n    // 停止状态轮询\n    stopStatusPolling() {\n      if (this.statusPollingTimer) {\n        clearInterval(this.statusPollingTimer)\n        this.statusPollingTimer = null\n      }\n    },\n\n    // 检查任务状态\n    async checkTaskStatus() {\n      try {\n        const response = await getAnalysisProgress(this.currentTaskId)\n        if (response.code === 200 && response.data) {\n          const taskData = response.data\n\n          // 更新进度\n          if (taskData.progress_percentage !== undefined) {\n            this.analysisProgress = taskData.progress_percentage\n          }\n\n          // 检查任务状态\n          if (taskData.task_status === 'completed' && this.analysisStatus !== 'completed') {\n            this.analysisStatus = 'completed'\n            this.analysisProgress = 100\n            this.addLog('success', '分析任务已完成！')\n            this.$message.success('分析完成！可以查看报告了')\n\n            // 停止轮询\n            this.stopStatusPolling()\n\n            // 移除自动跳转逻辑，让用户手动点击\"查看报告\"按钮\n            // setTimeout(() => {\n            //   this.goToReportPreview()\n            // }, 2000)\n          } else if (taskData.task_status === 'failed' && this.analysisStatus !== 'failed') {\n            this.analysisStatus = 'failed'\n            this.addLog('error', '分析任务失败')\n            this.$message.error('分析失败，请重试')\n            this.stopStatusPolling()\n          }\n        }\n      } catch (error) {\n        console.warn('检查任务状态失败:', error)\n        // 不显示错误消息，避免干扰用户\n      }\n    },\n\n    // ==================== 原有方法 ====================\n\n    // 切换关键词选择状态\n    toggleKeyword(keyword) {\n      const index = this.selectedKeywords.indexOf(keyword)\n      if (index > -1) {\n        // 如果已选中，则取消选择\n        this.selectedKeywords.splice(index, 1)\n      } else {\n        // 如果未选中，检查是否超过最大数量\n        if (this.selectedKeywords.length < this.maxKeywords) {\n          this.selectedKeywords.push(keyword)\n        } else {\n          this.$message.warning(`最多只能选择${this.maxKeywords}个关键词`)\n        }\n      }\n    },\n\n    // 检查关键词是否已选中\n    isKeywordSelected(keyword) {\n      return this.selectedKeywords.includes(keyword)\n    },\n\n    // 切换分类选择状态\n    toggleCategorySelection(categoryName, categoryKeywords) {\n      // 检查该分类下的所有关键词是否都已选中\n      const allSelected = categoryKeywords.every(keyword => this.isKeywordSelected(keyword))\n\n      if (allSelected) {\n        // 如果都已选中，则取消选择该分类下的所有关键词\n        categoryKeywords.forEach(keyword => {\n          const index = this.selectedKeywords.indexOf(keyword)\n          if (index > -1) {\n            this.selectedKeywords.splice(index, 1)\n          }\n        })\n        this.$message.info(`已取消选择\"${categoryName}\"分类下的所有关键词`)\n      } else {\n        // 如果没有全部选中，则选择该分类下的所有关键词\n        categoryKeywords.forEach(keyword => {\n          if (!this.isKeywordSelected(keyword) && this.selectedKeywords.length < this.maxKeywords) {\n            this.selectedKeywords.push(keyword)\n          }\n        })\n\n        // 检查是否因为数量限制而无法全部选择\n        const notSelected = categoryKeywords.filter(keyword => !this.isKeywordSelected(keyword))\n        if (notSelected.length > 0) {\n          this.$message.warning(`由于数量限制，无法选择\"${categoryName}\"分类下的所有关键词`)\n        } else {\n          this.$message.success(`已选择\"${categoryName}\"分类下的所有关键词`)\n        }\n      }\n    },\n\n\n    // 前往下一步\n    goToNextStep() {\n      // 显示验证样式\n      this.showValidation = true\n\n      // 验证表单是否填写完整\n      if (!this.entityKeyword.trim()) {\n        this.$message.warning('请填写实体关键词')\n        return\n      }\n\n      if (!this.specificRequirement.trim()) {\n        this.$message.warning('请填写具体需求')\n        return\n      }\n\n      if (this.selectedKeywords.length === 0) {\n        this.$message.warning('请至少选择一个关键词')\n        return\n      }\n\n      // 验证通过，隐藏验证样式并进入下一步\n      this.showValidation = false\n      if (this.currentStep < 3) {\n        this.currentStep++\n\n        // 如果进入第二步（数据概览），加载数据源列表\n        if (this.currentStep === 2) {\n          this.loadDataSourceList()\n        }\n      }\n    },\n\n    // 返回上一步\n    goToPreviousStep() {\n      if (this.currentStep > 1) {\n        this.currentStep--\n      }\n    },\n\n    // 检查套餐次数限制\n    async checkPackageLimits() {\n      try {\n        // 获取当前用户ID\n        const currentUserId = this.$store.getters.id || this.$store.state.user.id\n\n        if (!currentUserId) {\n          this.$message.error('无法获取用户信息，请重新登录')\n          return { canProceed: false }\n        }\n\n        // 获取用户统计数据\n        const response = await getDashboardStatistics(currentUserId)\n\n        if (response.code !== 200) {\n          this.$message.error('获取套餐信息失败，请稍后重试')\n          return { canProceed: false }\n        }\n\n        const userStats = response.data\n        const packageLimit = userStats.package_limit || 0\n        const remainingCount = userStats.remaining_count || 0\n        const totalAnalysis = userStats.total_analysis || 0\n\n        // 检查是否为无限制套餐\n        const isUnlimitedPackage = packageLimit === -1\n\n        if (isUnlimitedPackage) {\n          console.log('用户拥有无限制套餐，允许继续操作')\n          return { canProceed: true }\n        }\n\n        // 检查是否没有剩余次数\n        if (remainingCount <= 0) {\n          this.$confirm(\n            '您的套餐次数已用完，无法生成关键词。请升级套餐后继续使用。',\n            '套餐次数不足',\n            {\n              confirmButtonText: '升级套餐',\n              cancelButtonText: '取消',\n              type: 'warning',\n              showClose: false\n            }\n          ).then(() => {\n            this.upgradePackage()\n          }).catch(() => {\n            // 用户取消，不做任何操作\n          })\n          return { canProceed: false }\n        }\n\n        // 检查是否剩余次数较少（少于5次或少于总数的20%）\n        const isLowRemaining = remainingCount <= 5 ||\n                              (packageLimit > 0 && (remainingCount / packageLimit) <= 0.2)\n\n        if (isLowRemaining) {\n          try {\n            await this.$confirm(\n              `您的套餐剩余次数较少（${remainingCount}次），是否继续生成关键词？`,\n              '次数提醒',\n              {\n                confirmButtonText: '继续生成',\n                cancelButtonText: '升级套餐',\n                type: 'warning'\n              }\n            )\n            // 用户选择继续，允许操作\n            return { canProceed: true }\n          } catch {\n            // 用户选择升级套餐\n            this.upgradePackage()\n            return { canProceed: false }\n          }\n        }\n\n        // 次数充足，允许继续\n        console.log(`套餐检查通过，剩余次数: ${remainingCount}`)\n        return { canProceed: true }\n\n      } catch (error) {\n        console.error('套餐检查失败:', error)\n        this.$message.error('套餐检查失败，请稍后重试')\n        return { canProceed: false }\n      }\n    },\n\n    // 升级套餐方法\n    upgradePackage() {\n      // 跳转到套餐升级页面\n      this.$router.push('/set-meal/set-meal')\n    },\n\n    // 扣减套餐使用次数\n    async deductPackageUsage(operationType = '关键词生成') {\n      try {\n        console.log(`开始扣减套餐次数，操作类型: ${operationType}`)\n\n        // 这里可以调用后端API来扣减次数\n        // 目前先通过创建一个opinion_requirement记录来实现扣减\n        // 因为后端统计逻辑是基于opinion_requirement表的记录数\n\n        // 注意：实际的扣减逻辑应该在后端的关键词生成API中处理\n        // 这里只是一个前端的提示，真正的扣减应该在后端API调用成功后自动进行\n\n        console.log(`套餐次数扣减完成，操作类型: ${operationType}`)\n\n      } catch (error) {\n        console.error('扣减套餐次数失败:', error)\n        // 扣减失败不影响用户体验，只记录日志\n      }\n    },\n\n    // 开始分析\n    async startAnalysis() {\n      try {\n        // 首先检查套餐次数限制\n        const packageCheckResult = await this.checkPackageLimits()\n        if (!packageCheckResult.canProceed) {\n          return // 如果不能继续，直接返回\n        }\n\n        // 验证基本表单信息\n        if (!this.requirementName.trim()) {\n          this.$message.error('请填写需求名称')\n          return\n        }\n\n        if (!this.entityKeyword.trim()) {\n          this.$message.error('请填写实体关键词')\n          return\n        }\n\n        if (!this.specificRequirement.trim()) {\n          this.$message.error('请填写具体需求')\n          return\n        }\n\n        if (this.selectedKeywords.length === 0) {\n          this.$message.error('请至少选择一个关键词')\n          return\n        }\n\n        // 验证至少启用联网搜索（自定义数据源为可选）\n        if (!this.enableOnlineSearch) {\n          // 如果没有启用联网搜索，但启用了自定义数据源且有配置的数据源，则允许继续\n          if (this.enableCustomDataSource && this.customDataSources.length > 0) {\n            // 允许继续，使用自定义数据源\n          } else {\n            this.$message.error('请至少启用联网搜索或配置自定义数据源')\n            return\n          }\n        }\n\n        // 如果选择了自定义数据源，检查是否有配置的数据源（仅警告，不阻止）\n        if (this.enableCustomDataSource && this.customDataSources.length === 0) {\n          this.$message.warning('未配置自定义数据源，将仅使用联网搜索')\n          // 不return，允许继续执行\n        }\n\n        // 立即跳转到第三步分析进度页面\n        this.currentStep = 3\n        this.analysisStatus = 'running'\n        this.analysisProgress = 0 // 重置进度条为0%\n        this.analysisLogs = []\n\n        // 添加初始日志\n        this.addLog('info', '开始启动AI分析引擎...')\n        this.addLog('info', '正在验证分析参数...')\n        this.addLog('info', '参数验证通过，开始分析任务...')\n\n        // 如果没有需求ID，先尝试创建需求\n        if (!this.currentRequirementId) {\n          this.addLog('info', '正在创建临时需求记录...')\n          await this.createTemporaryRequirement()\n        }\n\n        // 首先创建分析任务\n        const taskData = {\n          requirement_id: this.currentRequirementId || 0, // 如果仍然没有ID，使用0作为临时值\n          user_id: 1, // 默认用户ID\n          task_name: `${this.requirementName} - 分析任务`,\n          analysis_config: {\n            entity_keyword: this.entityKeyword,\n            specific_requirement: this.specificRequirement,\n            selected_keywords: this.selectedKeywords,\n            enable_online_search: this.enableOnlineSearch,\n            enable_custom_data_source: this.enableCustomDataSource,\n            custom_data_sources: this.customDataSources.map(url => ({\n              url: url,\n              name: this.extractDomainName(url)\n            }))\n          }\n        }\n\n        this.addLog('info', '正在创建分析任务...')\n\n        // 创建分析任务\n        const taskResponse = await createAnalysisProgressTask(taskData)\n\n        if (taskResponse.code === 200) {\n          this.currentTaskId = taskResponse.data.task_id\n          this.addLog('info', `分析任务已创建，任务ID: ${this.currentTaskId}`)\n\n          // 使用HTTP轮询监控分析进度（替代WebSocket）\n          this.startAnalysisProgressPolling()\n\n          // 添加日志提示使用HTTP轮询\n          this.addLog('info', '已启动HTTP轮询监控分析进度...')\n\n          // 异步执行分析\n          this.performAsyncAnalysis()\n        } else {\n          throw new Error(taskResponse.msg || '创建分析任务失败')\n        }\n\n      } catch (error) {\n        console.error('分析启动失败:', error)\n        this.analysisStatus = 'failed'\n        this.addLog('error', '分析启动失败: ' + error.message)\n        this.$message.error('分析启动失败，请稍后重试')\n      }\n    },\n\n    // 创建临时需求记录\n    async createTemporaryRequirement() {\n      try {\n        // 为需求名称添加随机后缀以确保唯一性\n        const originalRequirementName = this.requirementName\n        const finalRequirementName = this.addRandomSuffixToRequirementName(originalRequirementName)\n\n        const requirementData = {\n          requirement_name: finalRequirementName,\n          entity_keyword: this.entityKeyword,\n          specific_requirement: this.specificRequirement,\n          priority: 'medium'\n        }\n\n        this.addLog('info', '正在保存需求信息...')\n        console.log('🚀 [临时需求] 原始需求名称:', originalRequirementName)\n        console.log('🚀 [临时需求] 最终需求名称:', finalRequirementName)\n\n        const response = await createRequirement(requirementData)\n\n        if (response.success) {\n          // 更新界面显示的需求名称\n          this.requirementName = finalRequirementName\n          this.currentRequirementId = response.data.id\n          this.requirementSaved = true\n          this.addLog('info', `需求信息保存成功，最终名称：${finalRequirementName}`)\n        } else {\n          // 如果创建失败（比如名称重复），尝试查找已存在的需求\n          if (response.msg && response.msg.includes('已存在')) {\n            this.addLog('info', '需求已存在，正在查找已有记录...')\n            await this.findExistingRequirementId()\n            if (this.currentRequirementId) {\n              this.addLog('info', '找到已存在的需求记录')\n            }\n          } else {\n            this.addLog('warning', '需求保存失败，将使用临时数据进行分析')\n          }\n        }\n      } catch (error) {\n        console.warn('创建临时需求失败:', error)\n        this.addLog('warning', '需求保存失败，将使用临时数据进行分析')\n        // 即使创建需求失败，也继续分析流程\n      }\n    },\n\n    // 执行异步分析\n    async performAsyncAnalysis() {\n      try {\n        this.addLog('info', '正在连接AI分析引擎...')\n\n        // 构建原有的分析请求参数\n        const analysisData = {\n          requirement_id: this.currentRequirementId || 0, // 如果没有ID，使用0作为临时值\n          entity_keyword: this.entityKeyword,\n          specific_requirement: this.specificRequirement,\n          selected_keywords: this.selectedKeywords,\n          enable_online_search: this.enableOnlineSearch,\n          enable_custom_data_source: this.enableCustomDataSource,\n          custom_data_sources: this.customDataSources.map(url => ({\n            url: url,\n            name: this.extractDomainName(url)\n          }))\n        }\n\n        // 调用原有的分析接口\n        const response = await startAnalysis(analysisData)\n\n        if (response.code === 200) {\n          this.addLog('success', '分析引擎启动成功！')\n\n          // 更新报告数据\n          this.updateReportData(response.data)\n          this.showAnalysisResults(response.data)\n\n          // 完成分析任务\n          if (this.currentTaskId) {\n            await completeAnalysisTask(this.currentTaskId, {\n              total_articles: response.data.analysis_results ?\n                Object.values(response.data.analysis_results).reduce((total, result) => {\n                  return total + (result.data?.articles?.length || 0)\n                }, 0) : 0,\n              analysis_results: response.data.analysis_results\n            })\n          }\n\n          // 手动触发分析完成状态更新\n          this.analysisStatus = 'completed'\n          this.analysisProgress = 100\n          this.addLog('success', '分析任务已完成！')\n\n          // 自动生成报告并上传到OSS\n          await this.generateAndUploadReportAfterAnalysis(response.data)\n\n          this.$message.success('分析完成！报告已生成并上传')\n\n          // 显式停止分析进度轮询\n          this.stopHttpPolling('analysisProgress')\n          this.addLog('info', '报告生成完成，已停止轮询')\n\n          // 移除自动跳转逻辑，让用户手动点击\"查看报告\"按钮\n          // setTimeout(() => {\n          //   this.goToReportPreview()\n          // }, 2000)\n\n        } else {\n          this.analysisStatus = 'failed'\n          this.addLog('error', '分析启动失败: ' + response.msg)\n\n          // 标记任务失败\n          if (this.currentTaskId) {\n            await this.failCurrentTask(response.msg)\n          }\n        }\n\n      } catch (error) {\n        this.analysisStatus = 'failed'\n        this.addLog('error', '分析过程中发生错误: ' + error.message)\n\n        // 标记任务失败\n        if (this.currentTaskId) {\n          await this.failCurrentTask(error.message)\n        }\n      }\n    },\n\n    // 标记当前任务失败\n    async failCurrentTask(errorMessage) {\n      try {\n        // 这里应该调用失败接口，但当前后端没有提供，所以使用取消接口\n        await cancelAnalysisTask(this.currentTaskId)\n      } catch (error) {\n        console.error('标记任务失败时出错:', error)\n      }\n    },\n\n    // 启动进度模拟（实际项目中应该通过WebSocket接收真实进度）\n    startProgressSimulation() {\n      const updateProgress = () => {\n        if (this.analysisStatus === 'running' && this.analysisProgress < 90) {\n          this.analysisProgress += Math.random() * 10\n          if (this.analysisProgress > 90) {\n            this.analysisProgress = 90\n          }\n          setTimeout(updateProgress, 1000 + Math.random() * 2000)\n        }\n      }\n      updateProgress()\n    },\n\n    // 显示分析结果\n    showAnalysisResults(results) {\n      console.log('分析结果:', results)\n\n      // 可以在这里添加结果展示逻辑\n      // 比如跳转到结果页面或显示结果弹窗\n      this.$notify({\n        title: '分析完成',\n        message: `分析任务已完成，需求ID: ${results.requirement_id}`,\n        type: 'success',\n        duration: 5000\n      })\n    },\n\n    // 处理联网搜索结果\n    handleOnlineSearchResults(searchResults) {\n      console.log('联网搜索结果:', searchResults)\n\n      if (searchResults.success && searchResults.data) {\n        const data = searchResults.data\n\n        // 显示搜索统计信息\n        let message = '联网搜索完成！\\n'\n        if (data.articles && data.articles.length > 0) {\n          message += `获取到 ${data.articles.length} 条相关信息\\n`\n        }\n        if (searchResults.saved_count !== undefined) {\n          message += `已保存 ${searchResults.saved_count} 条数据到数据库\\n`\n        }\n        if (data.sentiment_analysis) {\n          const sentiment = data.sentiment_analysis\n          message += `情感分析：正面 ${sentiment.positive}%，中性 ${sentiment.neutral}%，负面 ${sentiment.negative}%`\n        }\n\n        this.$notify({\n          title: '联网搜索结果',\n          message: message,\n          type: 'info',\n          duration: 8000\n        })\n      }\n    },\n\n    // 切换联网搜索\n    toggleOnlineSearch() {\n      this.enableOnlineSearch = !this.enableOnlineSearch\n    },\n\n    // 切换自定义数据源搜索\n    toggleCustomDataSource() {\n      this.enableCustomDataSource = !this.enableCustomDataSource\n\n      // 如果启用了自定义数据源，加载数据源列表\n      if (this.enableCustomDataSource) {\n        this.loadDataSourceList()\n      }\n    },\n\n    // 显示新增数据源表单\n    showAddSourceForm() {\n      this.showAddSourceInput = true\n      this.newSourceUrl = ''\n    },\n\n    // 隐藏新增数据源表单\n    hideAddSourceForm() {\n      this.showAddSourceInput = false\n      this.newSourceUrl = ''\n    },\n\n    // 确认新增数据源\n    async confirmAddSource() {\n      if (!this.newSourceUrl.trim()) {\n        this.$message.warning('请输入数据源网址')\n        return\n      }\n\n      // 简单的URL格式验证\n      const urlPattern = /^https?:\\/\\/.+/\n      if (!urlPattern.test(this.newSourceUrl.trim())) {\n        this.$message.warning('请输入有效的网址格式')\n        return\n      }\n\n      // 检查是否已存在相同的数据源\n      const trimmedUrl = this.newSourceUrl.trim()\n      if (this.customDataSources.includes(trimmedUrl)) {\n        this.$message.warning('该数据源已存在')\n        return\n      }\n\n      // 检查是否有当前需求ID\n      if (!this.currentRequirementId) {\n        this.$message.warning('请先保存需求信息')\n        return\n      }\n\n      try {\n        // 调用API保存数据源到数据库\n        const dataSourceData = {\n          requirement_id: this.currentRequirementId,\n          source_type: 'custom',\n          source_name: this.extractDomainName(trimmedUrl),\n          source_url: trimmedUrl,\n          remark: '用户自定义数据源'\n        }\n\n        const response = await createDataSource(dataSourceData)\n\n        if (response.success) {\n          // 将新的数据源添加到自定义数据源列表中\n          this.customDataSources.push(trimmedUrl)\n          // 自动启用自定义数据源搜索\n          this.enableCustomDataSource = true\n\n          this.$message.success('数据源添加成功')\n          // 清空输入框，但保持表单显示，允许继续添加\n          this.newSourceUrl = ''\n        } else {\n          this.$message.error('数据源添加失败：' + response.msg)\n        }\n      } catch (error) {\n        console.error('添加数据源失败:', error)\n        this.$message.error('数据源添加失败，请稍后重试')\n      }\n    },\n\n    // 从URL中提取域名作为数据源名称\n    extractDomainName(url) {\n      try {\n        const urlObj = new URL(url)\n        return urlObj.hostname.replace('www.', '')\n      } catch (error) {\n        // 如果URL解析失败，返回原始URL\n        return url\n      }\n    },\n\n    // 删除自定义数据源\n    removeCustomSource(index) {\n      // 从自定义数据源列表中移除\n      this.customDataSources.splice(index, 1)\n\n      // 如果没有自定义数据源了，自动关闭自定义数据源搜索\n      if (this.customDataSources.length === 0) {\n        this.enableCustomDataSource = false\n      }\n\n      this.$message.success('数据源删除成功')\n    },\n\n    // 刷新数据源列表\n    async refreshDataSourceList() {\n      try {\n        this.dataSourceListState.loading = true\n        await this.loadDataSourceList()\n        this.$message.success('数据源列表刷新成功')\n      } catch (error) {\n        console.error('刷新数据源列表失败:', error)\n        this.$message.error('刷新数据源列表失败')\n      } finally {\n        this.dataSourceListState.loading = false\n      }\n    },\n\n    // 加载数据源列表\n    async loadDataSourceList() {\n      try {\n        this.dataSourceListState.loading = true\n\n        const response = await getDataSourceList({\n          page_num: 1,\n          page_size: 1000 // 获取所有数据，前端分页\n        })\n\n        if (response.code === 200) {\n          // 处理分页数据结构 - records字段在响应根级别\n          if (response.records) {\n            this.dataSourceList = response.records\n          } else if (response.data && response.data.records) {\n            this.dataSourceList = response.data.records\n          } else if (response.data && response.data.rows) {\n            this.dataSourceList = response.data.rows\n          } else if (response.data && Array.isArray(response.data)) {\n            this.dataSourceList = response.data\n          } else {\n            this.dataSourceList = []\n          }\n        } else {\n          this.dataSourceList = []\n        }\n\n        console.log('加载数据源列表成功:', this.dataSourceList)\n        console.log('分页数据源列表:', this.paginatedDataSources)\n      } catch (error) {\n        console.error('加载数据源列表失败:', error)\n        this.dataSourceList = []\n        this.$message.error('加载数据源列表失败: ' + error.message)\n      } finally {\n        this.dataSourceListState.loading = false\n      }\n    },\n\n    // 处理数据源分页变化\n    handleDataSourcePageChange(page) {\n      this.dataSourceListState.current_page = page\n    },\n\n    // 处理数据源每页大小变化\n    handleDataSourceSizeChange(size) {\n      this.dataSourceListState.page_size = size\n      this.dataSourceListState.current_page = 1\n    },\n\n    // 处理数据源选择变化\n    handleDataSourceSelectionChange(selection) {\n      this.selectedDataSources = selection\n      console.log('选中的数据源:', selection)\n    },\n\n    // 删除数据源\n    async deleteDataSource(row) {\n      try {\n        await this.$confirm(`确定要删除数据源 \"${row.sourceUrl}\" 吗？`, '提示', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        })\n\n        // 调用删除API - 使用导入的API方法\n        const response = await deleteDataSourceAPI(row.id)\n\n        if (response.code === 200) {\n          this.$message.success('删除成功')\n          // 重新加载数据源列表\n          await this.loadDataSourceList()\n        } else {\n          this.$message.error(response.msg || '删除失败')\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('删除数据源失败:', error)\n          this.$message.error('删除失败: ' + (error.message || '未知错误'))\n        }\n      }\n    },\n\n\n\n    // 生成关联词\n    async generateRelatedWords() {\n      // 首先检查套餐次数限制\n      const packageCheckResult = await this.checkPackageLimits()\n      if (!packageCheckResult.canProceed) {\n        return // 如果不能继续，直接返回\n      }\n\n      // 检查是否填写了实体关键词和具体需求\n      if (!this.entityKeyword.trim()) {\n        this.$message.warning('请先填写实体关键词')\n        return\n      }\n\n      if (!this.specificRequirement.trim()) {\n        this.$message.warning('请先填写具体需求')\n        return\n      }\n\n      if (!this.requirementName.trim()) {\n        this.$message.warning('请先填写需求名称')\n        return\n      }\n\n      // 先尝试保存或更新需求到数据库\n      this.$message.info('正在检查需求并生成关联词...')\n\n      try {\n        let response\n\n        // 检查是否需要更新已存在的需求\n        if (this.currentRequirementId && this.requirementModified) {\n          console.log('🔄 [生成关联词] 更新已存在的需求，ID:', this.currentRequirementId)\n\n          const updateData = {\n            entity_keyword: this.entityKeyword,\n            specific_requirement: this.specificRequirement,\n            priority: 'medium'\n          }\n\n          response = await updateRequirement(this.currentRequirementId, updateData)\n\n          if (response.success) {\n            this.requirementModified = false\n            console.log('✅ [生成关联词] 需求更新成功')\n          }\n        } else if (!this.currentRequirementId) {\n          // 创建新需求\n          console.log('🚀 [生成关联词] 创建新需求')\n\n          // 为需求名称添加随机后缀以确保唯一性\n          const originalRequirementName = this.requirementName\n          const finalRequirementName = this.addRandomSuffixToRequirementName(originalRequirementName)\n\n          const requirementData = {\n            requirement_name: finalRequirementName,\n            entity_keyword: this.entityKeyword,\n            specific_requirement: this.specificRequirement,\n            priority: 'medium'\n          }\n\n          console.log('📝 [生成关联词] 原始需求名称:', originalRequirementName)\n          console.log('📝 [生成关联词] 最终需求名称:', finalRequirementName)\n          console.log('📝 [生成关联词] 请求数据:', requirementData)\n\n          response = await createRequirement(requirementData)\n\n          if (response.success) {\n            // 更新界面显示的需求名称为最终保存的名称\n            this.requirementName = finalRequirementName\n            this.currentRequirementId = response.data.id\n            this.requirementSaved = true\n            this.requirementModified = false\n            console.log('🆔 [生成关联词] 保存的需求ID:', this.currentRequirementId)\n          }\n        } else {\n          // 需求已存在且未修改，直接使用\n          console.log('✅ [生成关联词] 使用已存在的需求，ID:', this.currentRequirementId)\n          response = { success: true }\n        }\n\n        console.log('📥 [生成关联词] API响应:', response)\n        console.log('📊 [生成关联词] 响应类型:', typeof response)\n        console.log('✅ [生成关联词] 响应成功状态:', response?.success)\n        console.log('💬 [生成关联词] 响应消息:', response?.msg)\n\n        if (response.success) {\n          console.log('✅ [生成关联词] 需求操作成功')\n\n          if (this.currentRequirementId && this.requirementSaved) {\n            this.$message.success('需求信息已更新')\n          } else {\n            this.$message.success(`需求保存成功，最终名称：${this.requirementName}`)\n          }\n\n          console.log('🆔 [生成关联词] 当前需求ID:', this.currentRequirementId)\n          console.log('🎯 [生成关联词] 界面需求名称:', this.requirementName)\n        } else {\n          console.log('❌ [生成关联词] 需求创建失败，开始检查错误类型')\n          console.log('🔍 [生成关联词] 错误消息:', response.msg)\n\n          // 检查是否是需求名称重复的错误\n          const isDuplicateError = response.msg && response.msg.includes('已存在')\n          console.log('🔄 [生成关联词] 是否为重复需求错误:', isDuplicateError)\n\n          if (isDuplicateError) {\n            console.log('🎯 [生成关联词] 检测到重复需求，跳过保存直接生成关联词')\n            // 需求已存在，给用户提示并尝试获取已存在需求的ID\n            this.$message.info('需求已存在，直接生成关联词')\n            this.requirementSaved = true\n\n            // 尝试通过需求名称查找已存在的需求ID\n            try {\n              console.log('🔍 [生成关联词] 开始查找已存在需求的ID')\n              await this.findExistingRequirementId()\n              console.log('✅ [生成关联词] 成功找到已存在需求ID:', this.currentRequirementId)\n            } catch (findError) {\n              console.warn('⚠️ [生成关联词] 查找已存在需求ID失败:', findError)\n              // 即使查找失败，也继续生成关联词\n            }\n          } else {\n            console.log('💥 [生成关联词] 其他类型错误，停止执行')\n            this.$message.error('需求保存失败：' + response.msg)\n            return\n          }\n        }\n      } catch (error) {\n        console.log('💥 [生成关联词] 捕获到异常，开始详细分析')\n        console.error('🔍 [生成关联词] 错误对象:', error)\n        console.log('📝 [生成关联词] 错误消息:', error.message)\n        console.log('🌐 [生成关联词] 错误响应:', error.response)\n        console.log('📊 [生成关联词] 错误状态码:', error.response?.status)\n        console.log('💬 [生成关联词] 错误响应数据:', error.response?.data)\n\n        // 检查是否是需求名称重复的错误（可能被axios拦截器处理）\n        // 检查多种可能的错误消息格式\n        const errorMessage = error.message || ''\n        const responseMsg = error.response?.data?.msg || ''\n\n        console.log('🔍 [生成关联词] 分析错误消息:')\n        console.log('  - error.message:', errorMessage)\n        console.log('  - response.data.msg:', responseMsg)\n\n        // 多重检测逻辑\n        const isExistError = errorMessage.includes('已存在') ||\n                           errorMessage.includes('already exists') ||\n                           responseMsg.includes('已存在') ||\n                           responseMsg.includes('already exists')\n\n        // 特殊情况：错误消息为空但可能是重复需求错误\n        // 基于以下线索判断：1. HTTP状态码500 2. 错误消息包含\"创建舆情需求失败\"\n        const isPossibleDuplicateError = !isExistError &&\n                                       (errorMessage.includes('创建舆情需求失败') ||\n                                        responseMsg.includes('创建舆情需求失败')) &&\n                                       (error.response?.status === 500 || errorMessage.includes('500'))\n\n        console.log('🎯 [生成关联词] 错误类型判断:')\n        console.log('  - 明确的重复错误:', isExistError)\n        console.log('  - 可能的重复错误:', isPossibleDuplicateError)\n\n        if (isExistError || isPossibleDuplicateError) {\n          const errorType = isExistError ? '明确检测到重复需求' : '推测为重复需求错误'\n          console.log(`🎯 [生成关联词] ${errorType}，跳过保存直接生成关联词`)\n\n          // 需求已存在，给用户提示并尝试获取已存在需求的ID\n          this.$message.info('需求已存在，直接生成关联词')\n          this.requirementSaved = true\n\n          // 尝试通过需求名称查找已存在的需求ID\n          try {\n            console.log('🔍 [生成关联词] 开始查找已存在需求的ID')\n            await this.findExistingRequirementId()\n            console.log('✅ [生成关联词] 成功找到已存在需求ID:', this.currentRequirementId)\n          } catch (findError) {\n            console.warn('⚠️ [生成关联词] 查找已存在需求ID失败:', findError)\n            // 即使查找失败，也继续生成关联词\n          }\n        } else {\n          console.log('💥 [生成关联词] 其他类型错误，停止执行')\n          this.$message.error('需求保存失败，请重试')\n          return\n        }\n      }\n\n      // 保存成功或需求已存在后，生成关联词\n      console.log('🎯 [生成关联词] 开始执行关联词生成')\n      console.log('📊 [生成关联词] 当前状态:')\n      console.log('  - requirementSaved:', this.requirementSaved)\n      console.log('  - currentRequirementId:', this.currentRequirementId)\n      console.log('  - requirementName:', this.requirementName)\n\n      this.generateKeywordsOnly()\n    },\n\n    // 查找已存在需求的ID\n    async findExistingRequirementId() {\n      console.log('🔍 [查找需求ID] 开始查找已存在需求')\n      console.log('📝 [查找需求ID] 查找条件 - 需求名称:', this.requirementName)\n\n      try {\n        const queryParams = {\n          page: 1,\n          size: 100,\n          requirement_name: this.requirementName\n        }\n\n        console.log('🚀 [查找需求ID] 调用需求列表API，参数:', queryParams)\n        const response = await getRequirementList(queryParams)\n\n        console.log('📥 [查找需求ID] API响应:', response)\n        console.log('✅ [查找需求ID] 响应成功状态:', response?.success)\n        console.log('📊 [查找需求ID] 数据行数:', response?.data?.rows?.length)\n\n        if (response.success && response.data && response.data.rows) {\n          console.log('🔍 [查找需求ID] 在返回的需求列表中查找匹配项')\n          console.log('📋 [查找需求ID] 需求列表:', response.data.rows.map(req => ({\n            id: req.id,\n            name: req.requirementName\n          })))\n\n          const existingRequirement = response.data.rows.find(\n            req => req.requirementName === this.requirementName\n          )\n\n          if (existingRequirement) {\n            this.currentRequirementId = existingRequirement.id\n            console.log('✅ [查找需求ID] 找到匹配的需求:', existingRequirement)\n            console.log('🆔 [查找需求ID] 设置需求ID:', this.currentRequirementId)\n          } else {\n            console.log('❌ [查找需求ID] 未找到匹配的需求')\n          }\n        } else {\n          console.log('❌ [查找需求ID] API响应格式异常或无数据')\n        }\n      } catch (error) {\n        console.error('💥 [查找需求ID] 查找失败:', error)\n        throw error\n      }\n    },\n\n    // 重新生成关联词（不保存到数据库）\n    regenerateKeywords() {\n      // 检查是否填写了实体关键词和具体需求\n      if (!this.entityKeyword.trim()) {\n        this.$message.warning('请先填写实体关键词')\n        return\n      }\n\n      if (!this.specificRequirement.trim()) {\n        this.$message.warning('请先填写具体需求')\n        return\n      }\n\n      // 只生成关联词，不保存需求到数据库\n      this.generateKeywordsOnly()\n    },\n\n    // 纯生成关联词方法（使用AI接口）\n    async generateKeywordsOnly() {\n      // 验证必要参数\n      if (!this.specificRequirement.trim()) {\n        this.$message.warning('请先填写具体需求内容')\n        return\n      }\n\n      if (this.specificRequirement.trim().length < 5) {\n        this.$message.warning('需求内容过短，请提供更详细的需求描述')\n        return\n      }\n\n      // 显示加载状态\n      const loadingMessage = this.$message({\n        message: '正在调用AI生成关联词，请稍候...',\n        type: 'info',\n        duration: 0, // 不自动关闭\n        showClose: false\n      })\n\n      try {\n        console.log('🚀 [AI关联词] 开始调用AI生成关联词接口')\n        console.log('📝 [AI关联词] 需求内容:', this.specificRequirement)\n\n        // 调用AI关联词生成接口\n        const requestData = {\n          requirement_content: this.specificRequirement.trim(),\n          max_count: 20 // 生成更多关联词供用户选择\n        }\n\n        console.log('📤 [AI关联词] 请求参数:', requestData)\n        const response = await generateRelatedKeywords(requestData)\n        console.log('📥 [AI关联词] API响应:', response)\n\n        // 关闭加载消息\n        if (loadingMessage) {\n          loadingMessage.close()\n        }\n\n        if (response.code === 200 && response.data) {\n          const { keywords } = response.data\n\n          // 验证返回的关联词数据\n          if (Array.isArray(keywords) && keywords.length > 0) {\n            // 数据验证和去重处理\n            const validKeywords = this.processAIKeywords(keywords)\n\n            if (validKeywords.length > 0) {\n              // 保存所有生成的关键词\n              this.generatedKeywords = [...validKeywords]\n\n              // 默认选中前几个关键词（不超过最大数量）\n              this.selectedKeywords = []\n              validKeywords.forEach(word => {\n                if (this.selectedKeywords.length < this.maxKeywords) {\n                  this.selectedKeywords.push(word)\n                }\n              })\n\n              this.$message.success(`AI成功生成 ${validKeywords.length} 个关联词`)\n              console.log('✅ [AI关联词] 关联词生成成功:', validKeywords)\n\n              // 关键词生成成功后，扣减套餐次数\n              await this.deductPackageUsage('关键词生成')\n            } else {\n              console.warn('⚠️ [AI关联词] 生成的关联词经过验证后为空，使用降级策略')\n              this.fallbackToDefaultKeywords()\n            }\n          } else {\n            console.warn('⚠️ [AI关联词] API返回数据格式异常，使用降级策略')\n            this.fallbackToDefaultKeywords()\n          }\n        } else {\n          console.error('❌ [AI关联词] API调用失败:', response.msg)\n          this.$message.error('AI生成关联词失败：' + (response.msg || '未知错误'))\n          this.fallbackToDefaultKeywords()\n        }\n\n      } catch (error) {\n        // 关闭加载消息\n        if (loadingMessage) {\n          loadingMessage.close()\n        }\n\n        console.error('💥 [AI关联词] 调用异常:', error)\n\n        // 根据错误类型提供不同的处理\n        if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {\n          this.$message.error('AI服务响应超时，正在使用备用方案生成关联词...')\n        } else if (error.response && error.response.status === 401) {\n          this.$message.error('认证失败，请重新登录后重试')\n          return\n        } else {\n          this.$message.error('AI服务暂时不可用，正在使用备用方案生成关联词...')\n        }\n\n        // 使用降级策略\n        this.fallbackToDefaultKeywords()\n      }\n    },\n\n    // 处理AI返回的关联词数据\n    processAIKeywords(keywords) {\n      console.log('🔧 [关联词处理] 开始处理AI返回的关联词:', keywords)\n\n      const processed = []\n      const seen = new Set()\n\n      keywords.forEach(keyword => {\n        if (typeof keyword === 'string') {\n          const cleaned = keyword.trim()\n\n          // 基本验证：长度、去重、有效性\n          if (cleaned &&\n              cleaned.length >= 2 &&\n              cleaned.length <= 20 &&\n              !seen.has(cleaned) &&\n              this.isValidKeyword(cleaned)) {\n            processed.push(cleaned)\n            seen.add(cleaned)\n          }\n        }\n      })\n\n      console.log('✅ [关联词处理] 处理完成，有效关联词数量:', processed.length)\n      return processed\n    },\n\n    // 验证关联词是否有效\n    isValidKeyword(keyword) {\n      // 过滤无效的关联词\n      const invalidPatterns = [\n        /^\\d+$/, // 纯数字\n        /^[a-zA-Z]+$/, // 纯英文\n        /^[^\\u4e00-\\u9fa5\\w\\s]+$/, // 只包含特殊字符\n        /^(的|了|是|在|有|和|与|或|但|然而|因此|所以)$/ // 常见停用词\n      ]\n\n      return !invalidPatterns.some(pattern => pattern.test(keyword))\n    },\n\n    // 降级策略：使用默认关联词\n    fallbackToDefaultKeywords() {\n      console.log('🔄 [降级策略] 使用默认关联词生成逻辑')\n\n      // 基于实体关键词和需求内容生成基础关联词\n      const entityKeyword = this.entityKeyword.trim()\n      const baseKeywords = [\n        `${entityKeyword} 售后服务`,\n        `${entityKeyword} 客服态度`,\n        `${entityKeyword} 质量问题`,\n        `${entityKeyword} 投诉处理`,\n        `${entityKeyword} 用户体验`,\n        `${entityKeyword} 产品缺陷`,\n        `${entityKeyword} 维修服务`,\n        `${entityKeyword} 退换货`,\n        `${entityKeyword} 品牌形象`,\n        `${entityKeyword} 消费者反馈`\n      ]\n\n      // 保存生成的关键词\n      this.generatedKeywords = [...baseKeywords]\n\n      // 默认选中前几个关键词\n      this.selectedKeywords = []\n      baseKeywords.forEach(word => {\n        if (this.selectedKeywords.length < this.maxKeywords) {\n          this.selectedKeywords.push(word)\n        }\n      })\n\n      this.$message.info('已使用备用方案生成关联词')\n      console.log('✅ [降级策略] 默认关联词生成完成:', baseKeywords)\n    },\n\n    // 处理定时推送按钮点击\n    handleTimedPush() {\n      this.timedTaskDialogVisible = true\n      // 打开定时推送弹窗时加载定时任务列表\n      this.loadTimedTaskList()\n\n      // 启动定时任务状态轮询，定期更新任务列表\n      this.startTimedTaskListPolling()\n    },\n\n    // 启动定时任务列表轮询\n    startTimedTaskListPolling() {\n      this.startHttpPolling('timedTaskList', async () => {\n        const response = await getTimedTaskList()\n        return response.data\n      }, {\n        interval: 10000, // 每10秒轮询一次\n        onSuccess: (data) => {\n          if (data && Array.isArray(data.list)) {\n            // 更新任务列表，保持界面数据最新\n            this.timedTaskList = data.list\n            console.log('定时任务列表已更新:', data.list.length, '个任务')\n          }\n        },\n        onError: (error, attempts) => {\n          console.error('获取定时任务列表失败:', error)\n          // 如果连续失败3次，停止轮询并提示用户\n          if (attempts >= 3) {\n            this.$message.warning('定时任务状态更新失败，请手动刷新')\n            return false // 停止轮询\n          }\n          return true // 继续轮询\n        },\n        shouldStop: () => {\n          // 当定时任务弹窗关闭时停止轮询\n          return !this.timedTaskDialogVisible\n        }\n      })\n    },\n\n    // 关闭定时任务弹窗\n    closeTimedTaskDialog() {\n      this.timedTaskDialogVisible = false\n      // 停止定时任务列表轮询\n      this.stopHttpPolling('timedTaskList')\n    },\n\n    // 处理创建定时任务\n    handleCreateTimedTask() {\n      this.resetTaskForm()\n      this.editingTaskIndex = -1 // 设置为新建模式\n      this.loadRequirementList() // 加载需求列表\n      this.createTaskDialogVisible = true\n    },\n\n    // 处理添加定时任务按钮\n    handleAddTimedTask() {\n      this.resetTaskForm()\n      this.editingTaskIndex = -1 // 设置为新建模式\n      this.loadRequirementList() // 加载需求列表\n      this.createTaskDialogVisible = true\n    },\n\n    // 加载需求列表\n    async loadRequirementList() {\n      try {\n        const response = await getRequirementList({\n          page: 1,\n          size: 100\n        })\n\n        if (response.success) {\n          // 处理分页数据\n          if (response.data && response.data.rows) {\n            this.requirementList = response.data.rows.map(item => ({\n              id: item.id,\n              requirementName: item.requirementName\n            }))\n          } else if (Array.isArray(response.data)) {\n            this.requirementList = response.data.map(item => ({\n              id: item.id,\n              requirementName: item.requirementName\n            }))\n          } else {\n            this.requirementList = []\n          }\n        } else {\n          console.error('获取需求列表失败:', response.msg)\n          this.$message.error(response.msg || '获取需求列表失败')\n          // 使用模拟数据作为后备\n          this.requirementList = [\n            { id: 1, requirementName: '老板电器舆情监控' },\n            { id: 2, requirementName: '品牌声誉分析' },\n            { id: 3, requirementName: '竞品对比分析' },\n            { id: 4, requirementName: '用户反馈监控' }\n          ]\n        }\n      } catch (error) {\n        console.error('加载需求列表失败:', error)\n        this.$message.error('加载需求列表失败')\n        // 使用模拟数据作为后备\n        this.requirementList = [\n          { id: 1, requirementName: '老板电器舆情监控' },\n          { id: 2, requirementName: '品牌声誉分析' },\n          { id: 3, requirementName: '竞品对比分析' },\n          { id: 4, requirementName: '用户反馈监控' }\n        ]\n      }\n    },\n\n    // 加载关键词分类\n    async loadKeywordCategories() {\n      try {\n        const response = await getKeywordCategories()\n\n        if (response.success && Array.isArray(response.data)) {\n          this.keywordCategories = response.data\n          console.log('关键词分类加载成功:', this.keywordCategories)\n        } else {\n          console.error('获取关键词分类失败:', response.msg)\n          // 使用默认分类作为后备\n          this.keywordCategories = []\n        }\n      } catch (error) {\n        console.error('加载关键词分类失败:', error)\n        // 使用默认分类作为后备\n        this.keywordCategories = []\n      }\n    },\n\n    // 加载定时任务列表\n    async loadTimedTaskList() {\n      try {\n        const response = await getTimedTaskList()\n\n        if (response.success) {\n          // 处理分页数据 - 后端返回的是PageResponseModel格式\n          if (response.records && Array.isArray(response.records)) {\n            this.timedTaskList = response.records.map(task => ({\n              id: task.id,\n              requirementId: task.requirementId,\n              name: task.taskName,\n              description: task.taskDescription,\n              executeTime: task.executeTime,\n              frequency: task.frequency,\n              status: task.status === 'running' ? 'running' : 'pending',\n              pushUrl: task.pushUrl\n            }))\n          } else if (response.data && response.data.records && Array.isArray(response.data.records)) {\n            // 兼容嵌套在data中的情况\n            this.timedTaskList = response.data.records.map(task => ({\n              id: task.id,\n              requirementId: task.requirementId,\n              name: task.taskName,\n              description: task.taskDescription,\n              executeTime: task.executeTime,\n              frequency: task.frequency,\n              status: task.status === 'running' ? 'running' : 'pending',\n              pushUrl: task.pushUrl\n            }))\n          }\n          console.log('定时任务列表加载成功:', this.timedTaskList)\n        } else {\n          console.error('获取定时任务列表失败:', response.msg)\n          this.timedTaskList = []\n        }\n      } catch (error) {\n        console.error('加载定时任务列表失败:', error)\n        this.timedTaskList = []\n      }\n    },\n\n    // 关闭创建任务弹窗\n    closeCreateTaskDialog() {\n      this.createTaskDialogVisible = false\n      this.resetTaskForm()\n      this.editingTaskIndex = -1 // 重置编辑状态\n    },\n\n    // 保存并运行任务\n    async saveAndRunTask() {\n      if (!this.validateTaskForm()) {\n        return\n      }\n\n      try {\n        if (this.editingTaskIndex === -1) {\n          // 创建新任务\n          const taskData = {\n            requirement_id: this.taskForm.requirementId,\n            task_name: this.taskForm.name,\n            task_type: 'scheduled',\n            schedule_type: this.taskForm.frequency,\n            schedule_config: {\n              execute_time: this.taskForm.executeTime\n            },\n            task_description: this.taskForm.description,\n            push_url: this.taskForm.pushUrl,\n            priority: 'medium'\n          }\n\n          const response = await createTimedTask(taskData)\n          if (response.success) {\n            this.$message.success('任务已保存并开始运行')\n            // 重新加载任务列表\n            await this.loadTimedTaskList()\n            // 更新任务状态为运行中\n            if (response.data && response.data.id) {\n              await updateTaskStatus(response.data.id, 'running')\n              await this.loadTimedTaskList()\n            }\n          } else {\n            this.$message.error('创建任务失败：' + response.msg)\n            return\n          }\n        } else {\n          // 更新现有任务\n          const task = this.timedTaskList[this.editingTaskIndex]\n          const taskData = {\n            task_name: this.taskForm.name,\n            task_description: this.taskForm.description,\n            schedule_config: {\n              execute_time: this.taskForm.executeTime\n            },\n            schedule_type: this.taskForm.frequency,\n            push_url: this.taskForm.pushUrl\n          }\n\n          const response = await updateTimedTask(task.id, taskData)\n          if (response.success) {\n            // 更新任务状态为运行中\n            await updateTaskStatus(task.id, 'running')\n            this.$message.success('任务已更新并开始运行')\n            // 重新加载任务列表\n            await this.loadTimedTaskList()\n          } else {\n            this.$message.error('更新任务失败：' + response.msg)\n            return\n          }\n        }\n\n        this.createTaskDialogVisible = false\n        this.resetTaskForm()\n        this.editingTaskIndex = -1 // 重置编辑状态\n      } catch (error) {\n        console.error('保存任务失败:', error)\n        this.$message.error('保存任务失败，请重试')\n      }\n    },\n\n    // 保存任务计划\n    async saveTaskPlan() {\n      if (!this.validateTaskForm()) {\n        return\n      }\n\n      try {\n        if (this.editingTaskIndex === -1) {\n          // 创建新任务\n          const taskData = {\n            requirement_id: this.taskForm.requirementId,\n            task_name: this.taskForm.name,\n            task_type: 'scheduled',\n            schedule_type: this.taskForm.frequency,\n            schedule_config: {\n              execute_time: this.taskForm.executeTime\n            },\n            task_description: this.taskForm.description,\n            push_url: this.taskForm.pushUrl,\n            priority: 'medium'\n          }\n\n          const response = await createTimedTask(taskData)\n          if (response.success) {\n            this.$message.success('任务计划已保存')\n            // 重新加载任务列表\n            await this.loadTimedTaskList()\n          } else {\n            this.$message.error('保存任务计划失败：' + response.msg)\n            return\n          }\n        } else {\n          // 更新现有任务\n          const task = this.timedTaskList[this.editingTaskIndex]\n          const taskData = {\n            task_name: this.taskForm.name,\n            task_description: this.taskForm.description,\n            schedule_config: {\n              execute_time: this.taskForm.executeTime\n            },\n            schedule_type: this.taskForm.frequency,\n            push_url: this.taskForm.pushUrl\n          }\n\n          const response = await updateTimedTask(task.id, taskData)\n          if (response.success) {\n            this.$message.success('任务计划已更新')\n            // 重新加载任务列表\n            await this.loadTimedTaskList()\n          } else {\n            this.$message.error('更新任务计划失败：' + response.msg)\n            return\n          }\n        }\n\n        this.createTaskDialogVisible = false\n        this.resetTaskForm()\n        this.editingTaskIndex = -1 // 重置编辑状态\n      } catch (error) {\n        console.error('保存任务计划失败:', error)\n        this.$message.error('保存任务计划失败，请重试')\n      }\n    },\n\n    // 修改计划\n    modifyPlan() {\n      this.$message.info('修改计划功能开发中...')\n      // TODO: 实现修改计划逻辑\n    },\n\n    // 验证任务表单\n    validateTaskForm() {\n      if (!this.taskForm.requirementId) {\n        this.$message.warning('请选择需求名称')\n        return false\n      }\n\n      if (!this.taskForm.name.trim()) {\n        this.$message.warning('请输入任务名称')\n        return false\n      }\n\n      if (!this.taskForm.description.trim()) {\n        this.$message.warning('请输入任务描述')\n        return false\n      }\n\n      // 验证执行时间\n      if (this.taskForm.frequency === 'once') {\n        if (!this.taskForm.executeDateTime) {\n          this.$message.warning('请选择执行日期和时间')\n          return false\n        }\n        // 检查是否是未来时间\n        const executeTime = new Date(this.taskForm.executeDateTime)\n        if (executeTime <= new Date()) {\n          this.$message.warning('执行时间必须是未来时间')\n          return false\n        }\n      } else {\n        if (!this.taskForm.executeTime) {\n          this.$message.warning('请选择执行时间')\n          return false\n        }\n      }\n\n      return true\n    },\n\n    // 重置任务表单\n    resetTaskForm() {\n      this.taskForm = {\n        requirementId: '',\n        name: '',\n        description: '',\n        executeTime: '16:00',\n        executeDateTime: '',\n        frequency: 'daily',\n        pushUrl: ''\n      }\n    },\n\n    // 编辑任务\n    editTask(index) {\n      const task = this.timedTaskList[index]\n\n      // 将任务数据填充到表单中\n      this.taskForm = {\n        requirementId: task.requirementId || '',\n        name: task.name,\n        description: task.description,\n        executeTime: task.executeTime,\n        executeDateTime: task.executeDateTime || '',\n        frequency: task.frequency,\n        pushUrl: task.pushUrl || ''\n      }\n\n      // 加载需求列表\n      this.loadRequirementList()\n\n      // 打开编辑弹窗\n      this.createTaskDialogVisible = true\n\n      // 保存当前编辑的任务索引，用于后续更新\n      this.editingTaskIndex = index\n    },\n\n    // 删除任务\n    deleteTask(index) {\n      this.$confirm('确定要删除该任务吗？', '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(async () => {\n        try {\n          const task = this.timedTaskList[index]\n          const response = await deleteTimedTask(task.id)\n\n          if (response.success) {\n            this.$message.success('删除成功')\n            // 重新加载任务列表\n            await this.loadTimedTaskList()\n          } else {\n            this.$message.error('删除失败：' + response.msg)\n          }\n        } catch (error) {\n          console.error('删除任务失败:', error)\n          this.$message.error('删除任务失败，请重试')\n        }\n      }).catch(() => {\n        // 取消删除操作\n      })\n    },\n\n    // 预览任务详情\n    previewTask(index) {\n      const task = this.timedTaskList[index]\n      this.taskPreviewDialog.taskData = { ...task }\n      this.taskPreviewDialog.visible = true\n    },\n\n    // 隐藏任务预览弹窗\n    hideTaskPreviewDialog() {\n      this.taskPreviewDialog.visible = false\n      this.taskPreviewDialog.taskData = null\n    },\n\n    // 从预览弹窗编辑任务\n    editTaskFromPreview() {\n      if (this.taskPreviewDialog.taskData) {\n        // 找到任务在列表中的索引\n        const taskIndex = this.timedTaskList.findIndex(task =>\n          task.id === this.taskPreviewDialog.taskData.id\n        )\n        if (taskIndex !== -1) {\n          this.hideTaskPreviewDialog()\n          this.editTask(taskIndex)\n        }\n      }\n    },\n\n    // 获取频率文本\n    getFrequencyText(frequency) {\n      const frequencyMap = {\n        'once': '仅一次',\n        'daily': '每天',\n        'weekly': '每周',\n        'monthly': '每月'\n      }\n      return frequencyMap[frequency] || frequency\n    },\n\n    // 获取任务调度文本\n    getTaskScheduleText(task) {\n      if (task.frequency === 'once') {\n        // 一次性任务显示具体执行时间\n        return `仅一次 ${task.executeDateTime || task.executeTime}`\n      } else {\n        // 周期性任务显示频率和时间\n        const frequencyText = this.getFrequencyText(task.frequency)\n        return `${frequencyText} ${task.executeTime}`\n      }\n    },\n\n    // 获取推送类型文本\n    getPushTypeText(url) {\n      if (!url) return '未配置'\n\n      if (url.includes('oapi.dingtalk.com/robot/send')) {\n        return '钉钉机器人'\n      } else if (url.includes('qyapi.weixin.qq.com/cgi-bin/webhook/send')) {\n        return '企业微信机器人'\n      } else if (url.includes('open.feishu.cn/open-apis/bot/v2/hook/')) {\n        return '飞书机器人'\n      } else if (url.includes('localhost') || url.includes('127.0.0.1')) {\n        return '本地接口'\n      } else {\n        return 'HTTP接口'\n      }\n    },\n\n    // 获取脱敏的URL显示\n    getMaskedUrl(url) {\n      if (!url) return ''\n\n      try {\n        const urlObj = new URL(url)\n        const domain = urlObj.hostname\n        const path = urlObj.pathname\n\n        // 对于机器人URL，隐藏token部分\n        if (url.includes('access_token=') || url.includes('key=')) {\n          return `${urlObj.protocol}//${domain}${path}?***`\n        }\n\n        // 对于普通URL，显示域名和路径\n        return `${urlObj.protocol}//${domain}${path}`\n      } catch (error) {\n        // 如果URL解析失败，显示前50个字符\n        return url.length > 50 ? url.substring(0, 50) + '...' : url\n      }\n    },\n\n    // 复制到剪贴板\n    copyToClipboard(text) {\n      const textArea = document.createElement('textarea')\n      textArea.value = text\n      textArea.style.position = 'fixed'\n      textArea.style.opacity = '0'\n      document.body.appendChild(textArea)\n      textArea.select()\n      try {\n        document.execCommand('copy')\n        this.$message.success('地址已复制到剪贴板')\n      } catch (err) {\n        this.$message.error('复制失败，请手动复制')\n      } finally {\n        document.body.removeChild(textArea)\n      }\n    },\n\n    // 预览任务详情\n    previewTask(index) {\n      const task = this.timedTaskList[index]\n      this.taskPreviewDialog.taskData = { ...task }\n      this.taskPreviewDialog.visible = true\n    },\n\n    // 隐藏任务预览弹窗\n    hideTaskPreviewDialog() {\n      this.taskPreviewDialog.visible = false\n      this.taskPreviewDialog.taskData = null\n    },\n\n    // 从预览弹窗编辑任务\n    editTaskFromPreview() {\n      if (this.taskPreviewDialog.taskData) {\n        // 找到任务在列表中的索引\n        const taskIndex = this.timedTaskList.findIndex(task =>\n          task.id === this.taskPreviewDialog.taskData.id\n        )\n        if (taskIndex !== -1) {\n          this.hideTaskPreviewDialog()\n          this.editTask(taskIndex)\n        }\n      }\n    },\n\n    // 获取频率文本\n    getFrequencyText(frequency) {\n      const frequencyMap = {\n        'daily': '每天',\n        'weekly': '每周',\n        'monthly': '每月'\n      }\n      return frequencyMap[frequency] || frequency\n    },\n\n    // 获取推送类型文本\n    getPushTypeText(url) {\n      if (!url) return '未配置'\n\n      if (url.includes('oapi.dingtalk.com/robot/send')) {\n        return '钉钉机器人'\n      } else if (url.includes('qyapi.weixin.qq.com/cgi-bin/webhook/send')) {\n        return '企业微信机器人'\n      } else if (url.includes('open.feishu.cn/open-apis/bot/v2/hook/')) {\n        return '飞书机器人'\n      } else if (url.includes('localhost') || url.includes('127.0.0.1')) {\n        return '本地接口'\n      } else {\n        return 'HTTP接口'\n      }\n    },\n\n    // 获取脱敏的URL显示\n    getMaskedUrl(url) {\n      if (!url) return ''\n\n      try {\n        const urlObj = new URL(url)\n        const domain = urlObj.hostname\n        const path = urlObj.pathname\n\n        // 对于机器人URL，隐藏token部分\n        if (url.includes('access_token=') || url.includes('key=')) {\n          return `${urlObj.protocol}//${domain}${path}?***`\n        }\n\n        // 对于普通URL，显示域名和路径\n        return `${urlObj.protocol}//${domain}${path}`\n      } catch (error) {\n        // 如果URL解析失败，显示前50个字符\n        return url.length > 50 ? url.substring(0, 50) + '...' : url\n      }\n    },\n\n    // 复制到剪贴板\n    copyToClipboard(text) {\n      const textArea = document.createElement('textarea')\n      textArea.value = text\n      textArea.style.position = 'fixed'\n      textArea.style.opacity = '0'\n      document.body.appendChild(textArea)\n      textArea.select()\n      try {\n        document.execCommand('copy')\n        this.$message.success('地址已复制到剪贴板')\n      } catch (err) {\n        this.$message.error('复制失败，请手动复制')\n      } finally {\n        document.body.removeChild(textArea)\n      }\n    },\n\n    // 切换任务状态\n    async toggleTaskStatus(index) {\n      try {\n        const task = this.timedTaskList[index]\n        const newStatus = task.status === 'running' ? 'pending' : 'running'\n\n        const response = await updateTaskStatus(task.id, newStatus)\n\n        if (response.success) {\n          // 显示状态变更提示\n          if (newStatus === 'running') {\n            this.$message.success(`任务「${task.name}」已启动`)\n          } else {\n            this.$message.info(`任务「${task.name}」已暂停`)\n          }\n          // 重新加载任务列表\n          await this.loadTimedTaskList()\n        } else {\n          this.$message.error('状态更新失败：' + response.msg)\n        }\n      } catch (error) {\n        console.error('切换任务状态失败:', error)\n        this.$message.error('状态更新失败，请重试')\n      }\n    },\n\n    // 更新报告数据\n    updateReportData(analysisData) {\n      try {\n        console.log('开始更新报告数据，原始数据:', analysisData)\n\n        // 保存完整的分析结果\n        this.analysisResults = analysisData\n\n        // 重置报告数据\n        this.reportData = {\n          totalArticles: 0,\n          totalKeywords: this.selectedKeywords.length,\n          dataSources: (this.enableOnlineSearch ? 1 : 0) + this.customDataSources.length,\n          sentiment: {\n            positive: 0,\n            neutral: 0,\n            negative: 0\n          },\n          onlineSearchCount: 0,\n          customSourceCounts: {}\n        }\n\n        // 重置文章列表状态\n        this.articleListState.currentPage = 1\n        this.articleListState.searchKeyword = ''\n        this.articleListState.selectedSource = ''\n        this.articleListState.selectedSentiment = ''\n        this.articleListState.expandedArticles.clear()\n\n        // 处理分析结果\n        if (analysisData.analysis_results) {\n          const results = analysisData.analysis_results\n\n          // 处理联网搜索结果\n          if (results.online_search && results.online_search.data) {\n            const onlineData = results.online_search.data\n            console.log('联网搜索数据:', onlineData)\n\n            if (onlineData.articles && Array.isArray(onlineData.articles)) {\n              this.reportData.totalArticles += onlineData.articles.length\n              this.reportData.onlineSearchCount = onlineData.articles.length\n\n              console.log(`联网搜索获取到 ${onlineData.articles.length} 篇文章`)\n            }\n\n            // 更新情感分析数据\n            if (onlineData.sentiment_analysis) {\n              console.log('联网搜索情感分析数据:', onlineData.sentiment_analysis)\n              this.reportData.sentiment = {\n                positive: onlineData.sentiment_analysis.positive || 0,\n                neutral: onlineData.sentiment_analysis.neutral || 0,\n                negative: onlineData.sentiment_analysis.negative || 0\n              }\n            } else {\n              // 如果没有整体情感分析，从文章中计算\n              if (onlineData.articles && Array.isArray(onlineData.articles)) {\n                this.reportData.sentiment = this.calculateSentimentFromArticles(onlineData.articles)\n              }\n            }\n          }\n\n          // 处理自定义数据源结果\n          if (results.custom_data_source && results.custom_data_source.data) {\n            const customData = results.custom_data_source.data\n            console.log('自定义数据源数据:', customData)\n\n            if (customData.articles && Array.isArray(customData.articles)) {\n              this.reportData.totalArticles += customData.articles.length\n\n              // 统计各数据源的文章数量\n              customData.articles.forEach(article => {\n                const source = article.source || '未知来源'\n                this.reportData.customSourceCounts[source] = (this.reportData.customSourceCounts[source] || 0) + 1\n              })\n\n              // 合并情感分析结果\n              const customSentiment = this.calculateSentimentFromArticles(customData.articles)\n              this.reportData.sentiment = this.mergeSentimentData(this.reportData.sentiment, customSentiment, customData.articles.length)\n            }\n          }\n        }\n\n        console.log('报告数据更新完成:', this.reportData)\n      } catch (error) {\n        console.error('更新报告数据失败:', error)\n        this.$message.error('报告数据更新失败，请重试')\n      }\n    },\n\n    // 格式化分析时间\n    formatAnalysisTime(date) {\n      const now = new Date(date)\n      return now.toLocaleString('zh-CN', {\n        year: 'numeric',\n        month: '2-digit',\n        day: '2-digit',\n        hour: '2-digit',\n        minute: '2-digit'\n      })\n    },\n\n    // 从文章列表计算情感分析百分比\n    calculateSentimentFromArticles(articles) {\n      if (!articles || !Array.isArray(articles) || articles.length === 0) {\n        return { positive: 0, neutral: 0, negative: 0 }\n      }\n\n      const sentimentCounts = { positive: 0, neutral: 0, negative: 0 }\n\n      articles.forEach(article => {\n        const sentiment = article.sentiment || 'neutral'\n        if (sentimentCounts.hasOwnProperty(sentiment)) {\n          sentimentCounts[sentiment]++\n        } else {\n          sentimentCounts.neutral++\n        }\n      })\n\n      const total = articles.length\n      return {\n        positive: Math.round((sentimentCounts.positive / total) * 100),\n        neutral: Math.round((sentimentCounts.neutral / total) * 100),\n        negative: Math.round((sentimentCounts.negative / total) * 100)\n      }\n    },\n\n    // 合并多个数据源的情感分析结果\n    mergeSentimentData(sentiment1, sentiment2, weight2) {\n      const total1 = this.reportData.onlineSearchCount || 0\n      const total2 = weight2 || 0\n      const totalArticles = total1 + total2\n\n      if (totalArticles === 0) {\n        return { positive: 0, neutral: 0, negative: 0 }\n      }\n\n      return {\n        positive: Math.round(((sentiment1.positive * total1) + (sentiment2.positive * total2)) / totalArticles),\n        neutral: Math.round(((sentiment1.neutral * total1) + (sentiment2.neutral * total2)) / totalArticles),\n        negative: Math.round(((sentiment1.negative * total1) + (sentiment2.negative * total2)) / totalArticles)\n      }\n    },\n\n    // 文章展开/收起切换\n    toggleArticleExpand(index) {\n      const articleKey = `page-${this.articleListState.currentPage}-item-${index}`\n      if (this.articleListState.expandedArticles.has(articleKey)) {\n        this.articleListState.expandedArticles.delete(articleKey)\n      } else {\n        this.articleListState.expandedArticles.add(articleKey)\n      }\n    },\n\n    // 检查文章是否已展开\n    isArticleExpanded(index) {\n      const articleKey = `page-${this.articleListState.currentPage}-item-${index}`\n      return this.articleListState.expandedArticles.has(articleKey)\n    },\n\n    // 获取内容摘要（前200字符）\n    getContentSummary(content) {\n      if (!content) return '暂无内容'\n      return content.length > 200 ? content.substring(0, 200) + '...' : content\n    },\n\n    // 获取情感标签文本\n    getSentimentLabel(sentiment) {\n      const labels = {\n        positive: '正面',\n        neutral: '中性',\n        negative: '负面'\n      }\n      return labels[sentiment] || '未知'\n    },\n\n    // 格式化发布时间\n    formatPublishTime(publishTime) {\n      if (!publishTime) return '未知时间'\n      try {\n        const date = new Date(publishTime)\n        return date.toLocaleString('zh-CN', {\n          year: 'numeric',\n          month: '2-digit',\n          day: '2-digit',\n          hour: '2-digit',\n          minute: '2-digit'\n        })\n      } catch (error) {\n        return publishTime\n      }\n    },\n\n    // 复制文章内容\n    copyArticleContent(article) {\n      const content = `标题：${article.title}\\n来源：${article.source}\\n时间：${this.formatPublishTime(article.publish_time)}\\n情感：${this.getSentimentLabel(article.sentiment)}\\n内容：${article.content}`\n\n      if (navigator.clipboard) {\n        navigator.clipboard.writeText(content).then(() => {\n          this.$message.success('文章内容已复制到剪贴板')\n        }).catch(() => {\n          this.fallbackCopyText(content)\n        })\n      } else {\n        this.fallbackCopyText(content)\n      }\n    },\n\n    // 备用复制方法\n    fallbackCopyText(text) {\n      const textArea = document.createElement('textarea')\n      textArea.value = text\n      document.body.appendChild(textArea)\n      textArea.select()\n      try {\n        document.execCommand('copy')\n        this.$message.success('文章内容已复制到剪贴板')\n      } catch (err) {\n        this.$message.error('复制失败，请手动复制')\n      }\n      document.body.removeChild(textArea)\n    },\n\n    // 获取URL提示信息\n    getUrlTooltip(url) {\n      if (!url) return ''\n\n      try {\n        const urlObj = new URL(url)\n        return `访问 ${urlObj.hostname}`\n      } catch (error) {\n        return `访问链接: ${url}`\n      }\n    },\n\n    // 打开原文链接 - 直接使用AI返回的原始URL\n    openArticleUrl(url) {\n      if (url && url.trim()) {\n        window.open(url, '_blank')\n        this.$message.success('正在打开原文链接...')\n      } else {\n        this.$message.warning('暂无原文链接')\n      }\n    },\n\n    // 处理分页变化\n    handlePageChange(page) {\n      this.articleListState.currentPage = page\n      // 清空当前页的展开状态\n      const keysToDelete = []\n      this.articleListState.expandedArticles.forEach(key => {\n        if (key.startsWith(`page-${page}-`)) {\n          keysToDelete.push(key)\n        }\n      })\n      keysToDelete.forEach(key => this.articleListState.expandedArticles.delete(key))\n    },\n\n    // 重置需求保存状态\n    resetRequirementSaveStatus() {\n      // 如果需求已保存，标记为已修改而不是重置ID\n      if (this.requirementSaved && this.currentRequirementId) {\n        this.requirementModified = true\n        console.log('需求信息已变更，标记为需要更新')\n      } else if (!this.currentRequirementId) {\n        // 只有在没有需求ID时才重置状态\n        this.requirementSaved = false\n        this.requirementModified = false\n        console.log('需求信息已变更，重置保存状态')\n      }\n    },\n\n    // 确保需求存在（恢复或创建）\n    async ensureRequirementExists() {\n      try {\n        // 如果需求信息不完整，无法创建\n        if (!this.requirementName.trim() || !this.entityKeyword.trim() || !this.specificRequirement.trim()) {\n          console.log('需求信息不完整，无法创建需求')\n          return false\n        }\n\n        // 尝试根据需求名称查找已存在的需求\n        try {\n          const response = await getRequirementList({\n            requirement_name: this.requirementName,\n            page: 1,\n            page_size: 1\n          })\n\n          if (response.success && response.data && response.data.rows && response.data.rows.length > 0) {\n            const existingRequirement = response.data.rows[0]\n            this.currentRequirementId = existingRequirement.id\n            this.requirementSaved = true\n            this.requirementModified = false\n            console.log('找到已存在的需求，ID:', this.currentRequirementId)\n            return true\n          }\n        } catch (error) {\n          console.warn('查找已存在需求失败:', error)\n        }\n\n        // 如果没找到，创建新需求\n        console.log('未找到已存在需求，创建新需求...')\n        await this.createTemporaryRequirement()\n        return !!this.currentRequirementId\n      } catch (error) {\n        console.error('确保需求存在失败:', error)\n        return false\n      }\n    },\n\n    // 保存推送任务到数据库（带重复检查）\n    async saveTaskForPush(pushUrl, taskType = 'immediate') {\n      try {\n        // 检查是否有需求ID，如果没有则尝试恢复\n        if (!this.currentRequirementId) {\n          console.log('当前没有需求ID，尝试恢复或创建需求...')\n          await this.ensureRequirementExists()\n\n          // 如果仍然没有需求ID，抛出错误\n          if (!this.currentRequirementId) {\n            throw new Error('无法获取或创建需求信息，请重新填写需求')\n          }\n        }\n\n        // 第一步：检查任务是否已存在\n        console.log('检查任务是否已存在...')\n\n        // 对于查看报告的保存操作（没有push_url），跳过重复检查，直接创建任务\n        if (!pushUrl) {\n          console.log('查看报告任务，跳过重复检查')\n        } else {\n          // 只对推送类型的任务进行重复检查\n          const checkParams = {\n            requirement_id: this.currentRequirementId,\n            task_type: taskType,\n            push_url: pushUrl\n          }\n\n          try {\n            const checkResponse = await checkTaskExists(checkParams)\n            console.log('任务存在性检查结果:', checkResponse)\n\n            if (checkResponse.success && checkResponse.data.exists) {\n              // 任务已存在，跳过保存\n              console.log('任务已存在，跳过保存步骤')\n              return {\n                success: true,\n                taskId: checkResponse.data.task_id,\n                exists: true,\n                existingTask: {\n                  id: checkResponse.data.task_id,\n                  name: checkResponse.data.task_name,\n                  createTime: checkResponse.data.create_time\n                },\n                message: '任务已存在，直接推送'\n              }\n            }\n          } catch (error) {\n            console.log('任务存在性检查失败，继续创建新任务:', error.message)\n            // 如果检查失败，继续创建新任务\n          }\n        }\n\n        // 第二步：任务不存在，创建新任务\n        console.log('任务不存在，创建新任务...')\n\n        // 根据任务类型设置不同的任务名称和描述\n        let taskName, taskDescription\n        if (taskType === 'immediate') {\n          // 如果没有push_url，说明是查看报告的保存操作\n          if (!pushUrl) {\n            taskName = `${this.requirementName} - 分析报告`\n            taskDescription = `舆情分析报告查看任务`\n          } else {\n            taskName = `${this.requirementName} - 立即推送`\n            taskDescription = `舆情分析报告推送任务`\n          }\n        } else {\n          taskName = `${this.requirementName} - 推送计划`\n          taskDescription = `舆情分析报告推送任务`\n        }\n\n        const taskData = {\n          requirement_id: this.currentRequirementId,\n          task_name: taskName,\n          task_type: taskType,\n          task_description: taskDescription,\n          push_url: pushUrl || '', // report_view类型不需要push_url\n          priority: 'high',\n          schedule_type: taskType === 'immediate' ? 'once' : 'manual',\n          schedule_config: taskType === 'immediate' ?\n            { execute_time: new Date().toISOString() } :\n            { execute_time: null },\n          report_oss_url: this.reportOssUrl || null // 包含报告OSS URL\n        }\n\n        console.log('保存推送任务:', taskData)\n        const response = await createTimedTask(taskData)\n\n        if (response.success) {\n          console.log('推送任务保存成功，ID:', response.data.id)\n          return {\n            success: true,\n            taskId: response.data.id,\n            exists: false,\n            message: '任务创建成功'\n          }\n        } else {\n          throw new Error(response.msg || '任务保存失败')\n        }\n      } catch (error) {\n        console.error('保存推送任务失败:', error)\n        throw error\n      }\n    },\n\n\n\n    // 显示推送弹窗\n    showPushDialog() {\n      this.pushReportDialog.visible = true\n      this.pushReportDialog.url = ''\n      this.pushReportDialog.loading = false\n    },\n\n    // 隐藏推送弹窗\n    hidePushDialog() {\n      this.pushReportDialog.visible = false\n      this.pushReportDialog.url = ''\n      this.pushReportDialog.loading = false\n    },\n\n    // 验证推送URL格式（宽松验证，支持所有地址）\n    validatePushUrl(url) {\n      if (!url || !url.trim()) {\n        this.$message.error('请输入推送目标URL地址')\n        return false\n      }\n\n      let fullUrl = url.trim()\n\n      // 智能协议补全\n      if (!fullUrl.match(/^[a-zA-Z][a-zA-Z0-9+.-]*:/)) {\n        // 如果没有协议，根据地址特征智能添加\n        if (fullUrl.includes('localhost') || fullUrl.match(/^\\d+\\.\\d+\\.\\d+\\.\\d+/) || fullUrl.startsWith('127.0.0.1')) {\n          // 本地地址默认使用http\n          fullUrl = 'http://' + fullUrl\n        } else {\n          // 其他地址默认使用https\n          fullUrl = 'https://' + fullUrl\n        }\n      }\n\n      // 宽松的URL格式验证\n      try {\n        // 基本格式检查\n        if (fullUrl.includes(' ')) {\n          this.$message.error('URL地址不能包含空格')\n          return false\n        }\n\n        // 尝试创建URL对象进行基本验证\n        new URL(fullUrl)\n\n        // 特殊地址格式提示\n        if (fullUrl.includes('oapi.dingtalk.com/robot/send')) {\n          console.log('检测到钉钉机器人URL，将使用钉钉消息格式')\n          if (!fullUrl.includes('access_token=')) {\n            console.warn('钉钉机器人URL建议包含access_token参数')\n          }\n        } else if (fullUrl.includes('qyapi.weixin.qq.com/cgi-bin/webhook/send')) {\n          console.log('检测到企业微信机器人URL')\n        } else if (fullUrl.includes('open.feishu.cn/open-apis/bot/v2/hook/')) {\n          console.log('检测到飞书机器人URL')\n        }\n\n        return fullUrl\n      } catch (error) {\n        // 如果URL对象创建失败，进行更宽松的检查\n        console.warn('URL格式验证失败，尝试宽松验证:', error)\n\n        // 检查是否包含基本的URL结构\n        if (fullUrl.includes('://') && fullUrl.length > 10) {\n          console.log('使用宽松验证通过URL:', fullUrl)\n          return fullUrl\n        }\n\n        this.$message.error('URL格式可能不正确，但仍将尝试推送。如果推送失败，请检查URL格式')\n        return fullUrl\n      }\n    },\n\n    // 直接推送报告\n    async directPushReport() {\n      try {\n        // 验证URL\n        const validUrl = this.validatePushUrl(this.pushReportDialog.url)\n        if (!validUrl) {\n          return\n        }\n\n        // 检查是否有报告数据\n        if (!this.reportData || this.reportData.totalArticles === 0) {\n          this.$message.error('暂无报告数据可推送，请先完成分析')\n          return\n        }\n\n        // 设置loading状态\n        this.pushReportDialog.loading = true\n\n        // 移除任务保存逻辑，只执行推送功能\n        console.log('开始推送报告，不保存任务到数据库')\n\n        // 生成报告页面链接\n        const reportPageUrl = this.generateReportPageUrl()\n\n        // 准备推送数据\n        const pushData = {\n          reportData: {\n            ...this.reportData,\n            requirementName: this.requirementName,\n            entityKeyword: this.entityKeyword,\n            specificRequirement: this.specificRequirement,\n            selectedKeywords: this.selectedKeywords,\n            reportPageUrl: reportPageUrl, // 添加报告页面链接\n            pushTime: new Date().toISOString()\n          },\n          analysisResults: this.analysisResults,\n          requirementInfo: {\n            name: this.requirementName,\n            entityKeyword: this.entityKeyword,\n            specificRequirement: this.specificRequirement,\n            selectedKeywords: this.selectedKeywords\n          }\n        }\n\n        console.log('开始推送报告到:', validUrl)\n        console.log('推送数据:', pushData)\n\n        // 调用后端推送API\n        const pushRequestData = {\n          target_url: validUrl,\n          report_data: pushData.reportData,\n          analysis_results: pushData.analysisResults,\n          requirement_id: this.currentRequirementId,\n          push_type: 'immediate'\n        }\n\n        console.log('调用后端推送API:', pushRequestData)\n        const response = await pushReport(pushRequestData)\n\n        console.log('推送API响应:', response)\n\n        // 验证推送结果\n        if (response.success && response.data && response.data.success) {\n          // 推送成功\n          const pushResult = response.data\n          console.log('推送成功，推送ID:', pushResult.push_id)\n\n          // 显示详细成功信息\n          this.$message.success(pushResult.message || '报告推送成功！')\n\n          // 如果有响应状态码，也显示\n          if (pushResult.response_status) {\n            console.log(`目标服务器响应状态: ${pushResult.response_status}`)\n          }\n        } else {\n          // 推送失败\n          const errorMsg = response.data?.error_details || response.msg || '推送失败'\n          throw new Error(errorMsg)\n        }\n\n        // 关闭弹窗\n        this.hidePushDialog()\n\n      } catch (error) {\n        console.error('推送报告失败:', error)\n\n        // 错误处理\n        if (error.message && error.message.includes('timeout')) {\n          this.$message.error('推送超时，请检查目标URL是否可访问')\n        } else if (error.response && error.response.status) {\n          this.$message.error(`推送失败：${error.response.status} ${error.response.statusText || ''}`)\n        } else if (error.message) {\n          this.$message.error('推送失败：' + error.message)\n        } else {\n          this.$message.error('推送失败，请重试')\n        }\n      } finally {\n        this.pushReportDialog.loading = false\n      }\n    },\n\n    // 保存推送计划\n    async savePushPlan() {\n      try {\n        // 验证URL\n        const validUrl = this.validatePushUrl(this.pushReportDialog.url)\n        if (!validUrl) {\n          return\n        }\n\n        // 保存推送计划任务到数据库（带重复检查）\n        try {\n          const taskResult = await this.saveTaskForPush(validUrl, 'scheduled')\n          if (!taskResult.success) {\n            this.$message.error('推送计划保存失败')\n            return\n          }\n\n          // 处理任务存在性检查结果\n          if (taskResult.exists) {\n            console.log('推送计划任务已存在:', taskResult.existingTask)\n            this.$message.info(`推送计划已存在（${taskResult.existingTask.name}）`)\n          } else {\n            console.log('推送计划任务保存成功，任务ID:', taskResult.taskId)\n            this.$message.success('推送计划保存成功！')\n          }\n        } catch (error) {\n          this.$message.error('推送计划保存失败：' + error.message)\n          return\n        }\n\n        // 关闭弹窗\n        this.hidePushDialog()\n\n      } catch (error) {\n        console.error('保存推送计划失败:', error)\n        this.$message.error('保存推送计划失败：' + error.message)\n      }\n    },\n\n    // 生成报告页面链接\n    generateReportPageUrl() {\n      try {\n        // 获取当前页面的基础URL\n        const baseUrl = window.location.origin\n        const currentPath = window.location.pathname\n\n        // 构建报告页面链接，包含当前的分析参数\n        const reportParams = new URLSearchParams({\n          step: '3', // 直接跳转到第三步报告预览\n          requirementId: this.currentRequirementId || '', // 添加需求ID用于获取真实数据\n          requirementName: this.requirementName || '',\n          entityKeyword: this.entityKeyword || '',\n          specificRequirement: this.specificRequirement || '',\n          selectedKeywords: JSON.stringify(this.selectedKeywords || []),\n          timestamp: Date.now() // 添加时间戳确保链接唯一性\n        })\n\n        const reportUrl = `${baseUrl}${currentPath}?${reportParams.toString()}`\n        console.log('生成的报告页面链接:', reportUrl)\n\n        return reportUrl\n      } catch (error) {\n        console.error('生成报告页面链接失败:', error)\n        // 如果生成失败，返回当前页面链接\n        return window.location.href\n      }\n    },\n\n    // 解析URL参数\n    parseUrlParams() {\n      try {\n        const urlParams = new URLSearchParams(window.location.search)\n\n        // 检查是否有报告相关参数\n        const step = urlParams.get('step')\n        const requirementId = urlParams.get('requirementId')\n        const requirementName = urlParams.get('requirementName')\n        const entityKeyword = urlParams.get('entityKeyword')\n        const specificRequirement = urlParams.get('specificRequirement')\n        const selectedKeywords = urlParams.get('selectedKeywords')\n\n        if (step && (requirementId || (requirementName && entityKeyword))) {\n          console.log('检测到报告链接参数，自动加载报告')\n\n          // 设置表单数据\n          if (requirementId) {\n            this.currentRequirementId = parseInt(requirementId)\n          }\n          this.requirementName = requirementName || ''\n          this.entityKeyword = entityKeyword || ''\n          this.specificRequirement = specificRequirement || ''\n\n          // 解析选中的关键词\n          if (selectedKeywords) {\n            try {\n              this.selectedKeywords = JSON.parse(selectedKeywords)\n            } catch (e) {\n              console.warn('解析关键词参数失败:', e)\n              this.selectedKeywords = []\n            }\n          }\n\n          // 跳转到指定步骤\n          const targetStep = parseInt(step)\n          if (targetStep >= 1 && targetStep <= 3) {\n            this.currentStep = targetStep\n\n            // 如果是第三步，显示提示信息\n            if (targetStep === 3) {\n              this.$message.info('正在加载报告预览，请稍候...')\n              // 这里可以添加模拟的报告数据或者重新执行分析\n              this.loadReportFromParams()\n            }\n          }\n        }\n      } catch (error) {\n        console.error('解析URL参数失败:', error)\n      }\n    },\n\n    // 从URL参数加载报告数据\n    async loadReportFromParams() {\n      try {\n        // 检查是否有需求ID参数\n        const urlParams = new URLSearchParams(window.location.search)\n        const requirementId = urlParams.get('requirementId')\n\n        if (requirementId) {\n          // 调用公开API获取报告数据\n          const response = await getPublicReportData(requirementId)\n\n          if (response.success && response.data) {\n            const data = response.data\n\n            // 更新基础信息\n            this.requirementName = data.requirement_name || ''\n            this.entityKeyword = data.entity_keyword || ''\n            this.specificRequirement = data.specific_requirement || ''\n            this.currentRequirementId = data.requirement_id\n\n            // 更新报告数据\n            this.reportData = {\n              totalArticles: data.analysis_results?.total_articles || 0,\n              totalKeywords: this.selectedKeywords.length,\n              dataSources: 1,\n              sentiment: {\n                positive: data.analysis_results?.sentiment?.positive || 0,\n                neutral: data.analysis_results?.sentiment?.neutral || 0,\n                negative: data.analysis_results?.sentiment?.negative || 0\n              },\n              onlineSearchCount: data.analysis_results?.online_search?.length || 0,\n              customSourceCounts: {}\n            }\n\n            // 处理在线搜索结果\n            if (data.analysis_results?.online_search) {\n              this.handleOnlineSearchResults(data.analysis_results.online_search)\n            }\n\n            this.$message.success('报告数据加载成功')\n          } else {\n            throw new Error(response.msg || '获取报告数据失败')\n          }\n        } else {\n          // 没有需求ID，使用默认数据\n          this.reportData = {\n            totalArticles: 0,\n            totalKeywords: this.selectedKeywords.length,\n            dataSources: 1,\n            sentiment: {\n              positive: 0,\n              neutral: 0,\n              negative: 0\n            },\n            onlineSearchCount: 0,\n            customSourceCounts: {}\n          }\n        }\n      } catch (error) {\n        console.error('加载报告数据失败:', error)\n        this.$message.warning('加载报告数据失败，显示默认数据')\n\n        // 使用默认数据\n        this.reportData = {\n          totalArticles: 0,\n          totalKeywords: this.selectedKeywords.length,\n          dataSources: 1,\n          sentiment: {\n            positive: 0,\n            neutral: 0,\n            negative: 0\n          },\n          onlineSearchCount: 0,\n          customSourceCounts: {}\n        }\n      }\n\n      // 提示用户可以重新执行分析获取最新数据\n      this.$message.warning('这是通过链接访问的报告，数据可能不是最新的。建议重新执行分析获取最新结果。')\n    },\n\n    // 获取关键词频率（基于实际数据）\n    getKeywordFrequency(keyword) {\n      // TODO: 这里可以根据实际分析结果返回关键词频率\n      // 暂时返回模拟数据，后续可以从reportData中计算真实频率\n      return Math.floor(Math.random() * 50) + 10\n    },\n\n    // 获取数据源统计数量\n    getSourceCount(source) {\n      if (this.reportData.customSourceCounts) {\n        return this.reportData.customSourceCounts[source] || 0\n      }\n      return 0\n    },\n\n\n\n    // 提取域名\n    extractDomainName(url) {\n      try {\n        const urlObj = new URL(url)\n        return urlObj.hostname.replace('www.', '')\n      } catch (error) {\n        // 如果不是有效URL，直接返回原字符串的前20个字符\n        return url.length > 20 ? url.substring(0, 20) + '...' : url\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.opinion-analysis {\n  padding: 0;\n  background-color: #f8f9fa;\n  min-height: 100vh;\n}\n\n// 步骤指示器样式\n.steps-container {\n  background: white;\n  padding: 20px 24px;\n  border-bottom: 1px solid #e8e8e8;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n\n  .left-actions {\n    flex: 0 0 auto;\n\n    .timed-push-btn {\n      font-size: 14px;\n      padding: 8px 16px;\n      border-radius: 6px;\n      font-weight: 500;\n      transition: all 0.3s ease;\n\n      &:hover {\n        transform: translateY(-1px);\n        box-shadow: 0 2px 8px rgba(84, 112, 198, 0.3);\n      }\n\n      &:active {\n        transform: translateY(0);\n      }\n    }\n  }\n\n  .steps-wrapper {\n    flex: 1;\n    display: flex;\n    justify-content: center;\n    gap: 60px;\n  }\n\n  .right-placeholder {\n    flex: 0 0 auto;\n    width: 88px; // 与左侧按钮宽度保持平衡\n  }\n\n  .right-actions {\n    flex: 0 0 auto;\n    display: flex;\n    align-items: center;\n    gap: 12px;\n\n    .step-switch-dropdown {\n      .step-switch-btn {\n        font-size: 13px;\n        padding: 6px 12px;\n        border-radius: 6px;\n        font-weight: 500;\n        background: #f39c12;\n        border-color: #f39c12;\n        color: white;\n        transition: all 0.3s ease;\n        box-shadow: 0 2px 4px rgba(243, 156, 18, 0.2);\n\n        &:hover {\n          background: #e67e22;\n          border-color: #e67e22;\n          transform: translateY(-1px);\n          box-shadow: 0 4px 8px rgba(243, 156, 18, 0.3);\n        }\n\n        &:active {\n          transform: translateY(0);\n        }\n\n        .el-icon--right {\n          margin-left: 4px;\n        }\n      }\n    }\n\n    .analyze-record-btn,\n    .timed-push-btn {\n      font-size: 13px;\n      padding: 6px 12px;\n      border-radius: 6px;\n      font-weight: 500;\n      transition: all 0.3s ease;\n\n      &:hover {\n        transform: translateY(-1px);\n        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n      }\n\n      &:active {\n        transform: translateY(0);\n      }\n    }\n  }\n\n  .step-item {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n    color: #999;\n    font-size: 14px;\n\n    &.active {\n      color: #5470c6;\n      font-weight: 500;\n\n      .step-number {\n        background: #5470c6;\n        color: white;\n      }\n    }\n\n    .step-number {\n      width: 24px;\n      height: 24px;\n      border-radius: 50%;\n      background: #e8e8e8;\n      color: #999;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 12px;\n      font-weight: 500;\n    }\n  }\n}\n\n.main-content {\n  max-width: 800px;\n  margin: 0 auto;\n  padding: 40px 24px;\n}\n\n// 报告预览区域\n.report-preview {\n  background: white;\n  border-radius: 8px;\n  padding: 32px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n  margin-bottom: 32px;\n\n  .section-title {\n    font-size: 18px;\n    font-weight: 600;\n    color: #333;\n    margin: 0 0 24px 0;\n  }\n}\n\n// 报告概览卡片\n.report-overview {\n  margin-bottom: 24px;\n\n  .overview-card {\n    background: #f8f9fa;\n    border-radius: 8px;\n    padding: 20px;\n    border: 1px solid #e8e8e8;\n\n    .card-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 16px;\n\n      h3 {\n        font-size: 16px;\n        font-weight: 600;\n        color: #333;\n        margin: 0;\n      }\n\n      .analysis-time {\n        font-size: 13px;\n        color: #666;\n      }\n    }\n\n    .overview-stats {\n      display: flex;\n      gap: 32px;\n\n      .stat-item {\n        text-align: center;\n\n        .stat-number {\n          font-size: 24px;\n          font-weight: 600;\n          color: #5470c6;\n          margin-bottom: 4px;\n        }\n\n        .stat-label {\n          font-size: 13px;\n          color: #666;\n        }\n      }\n    }\n  }\n}\n\n// 分析卡片通用样式\n.analysis-card {\n  background: white;\n  border-radius: 8px;\n  padding: 20px;\n  border: 1px solid #e8e8e8;\n  margin-bottom: 16px;\n\n  h3 {\n    font-size: 16px;\n    font-weight: 600;\n    color: #333;\n    margin: 0 0 16px 0;\n  }\n}\n\n// 情感分析\n.sentiment-analysis {\n  margin-bottom: 24px;\n\n  .sentiment-chart {\n    .sentiment-item {\n      display: flex;\n      align-items: center;\n      margin-bottom: 12px;\n\n      &:last-child {\n        margin-bottom: 0;\n      }\n\n      .sentiment-bar {\n        flex: 1;\n        height: 20px;\n        background: #f0f0f0;\n        border-radius: 10px;\n        overflow: hidden;\n        margin-right: 12px;\n\n        .bar-fill {\n          height: 100%;\n          border-radius: 10px;\n          transition: width 0.3s ease;\n        }\n      }\n\n      &.positive .bar-fill {\n        background: linear-gradient(90deg, #52c41a, #73d13d);\n      }\n\n      &.neutral .bar-fill {\n        background: linear-gradient(90deg, #faad14, #ffc53d);\n      }\n\n      &.negative .bar-fill {\n        background: linear-gradient(90deg, #ff4d4f, #ff7875);\n      }\n\n      .sentiment-info {\n        display: flex;\n        align-items: center;\n        gap: 8px;\n        min-width: 80px;\n\n        .sentiment-label {\n          font-size: 14px;\n          color: #666;\n        }\n\n        .sentiment-value {\n          font-size: 14px;\n          font-weight: 600;\n          color: #333;\n        }\n      }\n    }\n  }\n}\n\n// 关键词分析\n.keyword-analysis {\n  margin-bottom: 24px;\n\n  .selected-keywords-display {\n    .keyword-list {\n      display: flex;\n      flex-wrap: wrap;\n      gap: 12px;\n\n      .keyword-item {\n        display: flex;\n        align-items: center;\n        background: #f0f7ff;\n        border: 1px solid #d6e4ff;\n        border-radius: 16px;\n        padding: 6px 12px;\n        font-size: 13px;\n\n        .keyword-text {\n          color: #1890ff;\n          margin-right: 6px;\n        }\n\n        .keyword-frequency {\n          background: #1890ff;\n          color: white;\n          border-radius: 8px;\n          padding: 2px 6px;\n          font-size: 11px;\n          font-weight: 500;\n        }\n      }\n    }\n  }\n}\n\n// 数据来源统计\n.data-source-stats {\n  margin-bottom: 24px;\n\n  .source-list {\n    .source-item {\n      display: flex;\n      align-items: center;\n      padding: 12px 0;\n      border-bottom: 1px solid #f0f0f0;\n\n      &:last-child {\n        border-bottom: none;\n      }\n\n      .source-icon {\n        width: 32px;\n        height: 32px;\n        border-radius: 6px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        margin-right: 12px;\n\n        &.online {\n          background: #e6f4ff;\n          color: #1890ff;\n        }\n\n        &.custom {\n          background: #f6ffed;\n          color: #52c41a;\n        }\n\n        i {\n          font-size: 16px;\n        }\n      }\n\n      .source-info {\n        flex: 1;\n\n        .source-name {\n          font-size: 14px;\n          font-weight: 500;\n          color: #333;\n          margin-bottom: 2px;\n        }\n\n        .source-desc {\n          font-size: 12px;\n          color: #999;\n        }\n      }\n\n      .source-count {\n        font-size: 14px;\n        font-weight: 600;\n        color: #5470c6;\n      }\n    }\n  }\n}\n\n// 详细数据样式\n.detailed-data {\n  margin-bottom: 24px;\n\n  .card-header-with-controls {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 20px;\n\n    h3 {\n      margin: 0;\n    }\n\n    .data-controls {\n      display: flex;\n      align-items: center;\n    }\n  }\n\n  .article-list {\n    .article-item {\n      background: #fafafa;\n      border: 1px solid #f0f0f0;\n      border-radius: 8px;\n      margin-bottom: 12px;\n      overflow: hidden;\n      transition: all 0.3s ease;\n\n      &:hover {\n        border-color: #d9d9d9;\n        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n      }\n\n      .article-header {\n        padding: 16px;\n        background: white;\n\n        .article-title-row {\n          display: flex;\n          justify-content: space-between;\n          align-items: flex-start;\n          margin-bottom: 8px;\n\n          .article-title {\n            flex: 1;\n            margin: 0;\n            font-size: 16px;\n            font-weight: 500;\n            color: #262626;\n            cursor: pointer;\n            line-height: 1.4;\n            margin-right: 12px;\n            transition: color 0.3s ease;\n\n            &:hover {\n              color: #1890ff;\n            }\n\n            i {\n              margin-left: 8px;\n              font-size: 14px;\n              color: #8c8c8c;\n            }\n          }\n\n          .article-meta {\n            display: flex;\n            align-items: center;\n            gap: 8px;\n            flex-shrink: 0;\n\n            .article-source {\n              font-size: 12px;\n              color: #8c8c8c;\n              background: #f5f5f5;\n              padding: 2px 8px;\n              border-radius: 4px;\n            }\n\n            .sentiment-tag {\n              font-size: 12px;\n              padding: 2px 8px;\n              border-radius: 4px;\n              font-weight: 500;\n\n              &.positive {\n                background: #f6ffed;\n                color: #52c41a;\n                border: 1px solid #b7eb8f;\n              }\n\n              &.neutral {\n                background: #f5f5f5;\n                color: #8c8c8c;\n                border: 1px solid #d9d9d9;\n              }\n\n              &.negative {\n                background: #fff2f0;\n                color: #ff4d4f;\n                border: 1px solid #ffccc7;\n              }\n            }\n          }\n        }\n\n        .article-info {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n\n          .publish-time {\n            font-size: 12px;\n            color: #8c8c8c;\n          }\n\n          .article-actions {\n            display: flex;\n            gap: 8px;\n          }\n        }\n      }\n\n      .article-content {\n        padding: 0 16px 16px;\n\n        .content-summary {\n          margin: 0;\n          color: #595959;\n          line-height: 1.6;\n          font-size: 14px;\n        }\n\n        &.expanded {\n          background: #fafafa;\n          border-top: 1px solid #f0f0f0;\n          padding: 16px;\n\n          .content-full {\n            margin: 0;\n            color: #262626;\n            line-height: 1.6;\n            font-size: 14px;\n            white-space: pre-wrap;\n          }\n        }\n      }\n    }\n  }\n\n  .pagination-wrapper {\n    display: flex;\n    justify-content: center;\n    margin-top: 20px;\n    padding-top: 20px;\n    border-top: 1px solid #f0f0f0;\n  }\n}\n\n// 报告操作\n.report-actions {\n  margin-top: 32px;\n  text-align: center;\n\n  .action-buttons {\n    display: flex;\n    justify-content: center;\n    gap: 16px;\n\n    .el-button {\n      padding: 12px 24px;\n      font-size: 14px;\n      border-radius: 6px;\n    }\n  }\n}\n\n// 分析来源区域\n.analysis-source {\n  background: white;\n  border-radius: 8px;\n  padding: 32px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n  margin-bottom: 32px;\n\n  .section-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 24px;\n\n    .section-title {\n      font-size: 18px;\n      font-weight: 600;\n      color: #333;\n      margin: 0;\n    }\n\n    .template-btn {\n      font-size: 14px;\n      padding: 6px 16px;\n    }\n  }\n\n  .section-title {\n    font-size: 18px;\n    font-weight: 600;\n    color: #333;\n    margin: 0 0 24px 0;\n  }\n}\n\n// 输入区域样式\n.input-section {\n  margin-bottom: 24px;\n\n  .input-label {\n    font-size: 14px;\n    color: #333;\n    margin-bottom: 8px;\n    font-weight: 500;\n\n    .required {\n      color: #ff4d4f;\n      margin-left: 2px;\n    }\n\n    .keyword-count {\n      color: #999;\n      font-weight: normal;\n      margin-left: 8px;\n      font-size: 13px;\n\n      &.max-reached {\n        color: #ff4d4f;\n        font-weight: 500;\n      }\n    }\n  }\n\n  .entity-input {\n    :deep(.el-input__inner) {\n      border-radius: 6px;\n      border: 1px solid #d9d9d9;\n      padding: 12px 16px;\n      font-size: 14px;\n\n      &::placeholder {\n        color: #bfbfbf;\n      }\n\n      &:focus {\n        border-color: #5470c6;\n        box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);\n      }\n    }\n\n    &.error {\n      :deep(.el-input__inner) {\n        border-color: #ff4d4f;\n\n        &:focus {\n          border-color: #ff4d4f;\n          box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.1);\n        }\n      }\n    }\n  }\n}\n\n// 任务列表样式\n.task-list {\n  padding: 24px;\n\n  .empty-task-list {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n    padding: 40px 0;\n\n    .empty-icon {\n      font-size: 48px;\n      margin-bottom: 16px;\n      color: #d9d9d9;\n    }\n\n    .empty-text {\n      font-size: 16px;\n      color: #999;\n      margin-bottom: 24px;\n    }\n\n    .add-task-btn {\n      padding: 8px 20px;\n    }\n  }\n\n  .task-items {\n    display: flex;\n    flex-direction: column;\n    gap: 16px;\n  }\n\n  .task-item {\n    background: white;\n    border-radius: 8px;\n    padding: 16px;\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n    display: flex;\n    justify-content: space-between;\n    transition: all 0.3s ease;\n\n    &:hover {\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n      transform: translateY(-2px);\n    }\n\n    .task-info {\n       flex: 1;\n\n       .task-header {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        margin-bottom: 8px;\n       }\n\n       .task-name {\n        font-size: 16px;\n        font-weight: 500;\n        color: #333;\n       }\n\n       .task-status {\n        font-size: 12px;\n        padding: 2px 8px;\n        border-radius: 10px;\n        font-weight: 500;\n\n        &.status-running {\n          background-color: rgba(82, 196, 26, 0.1);\n          color: #52c41a;\n        }\n\n        &.status-pending {\n          background-color: rgba(250, 173, 20, 0.1);\n          color: #faad14;\n        }\n       }\n\n       /* 任务描述样式已移除，因为不再显示任务描述 */\n\n       .task-schedule {\n        font-size: 13px;\n        color: #999;\n        display: flex;\n        align-items: center;\n        gap: 4px;\n        margin-top: 8px;\n\n        i {\n          font-size: 14px;\n        }\n       }\n     }\n\n    .task-actions {\n      display: flex;\n      align-items: flex-start;\n      gap: 8px;\n\n      .el-button {\n        padding: 4px;\n\n        i {\n          font-size: 16px;\n        }\n      }\n    }\n  }\n}\n\n.requirement-textarea {\n  :deep(.el-textarea__inner) {\n    border-radius: 6px;\n    border: 1px solid #d9d9d9;\n    padding: 12px 16px;\n    font-size: 14px;\n    line-height: 1.6;\n    resize: vertical;\n\n    &::placeholder {\n      color: #bfbfbf;\n    }\n\n    &:focus {\n      border-color: #5470c6;\n      box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);\n    }\n  }\n\n  &.error {\n    :deep(.el-textarea__inner) {\n      border-color: #ff4d4f;\n\n      &:focus {\n        border-color: #ff4d4f;\n        box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.1);\n      }\n    }\n  }\n}\n\n// 选择关联词区域\n.related-words-section {\n  .section-header {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    margin-bottom: 16px;\n\n    .header-left {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n\n      .section-label {\n        font-size: 14px;\n        color: #333;\n        font-weight: 500;\n      }\n\n      .word-count {\n        font-size: 14px;\n        color: #999;\n        font-weight: normal;\n        margin-left: 8px;\n        transition: color 0.3s ease;\n\n        &.max-reached {\n          color: #ff4d4f;\n          font-weight: 500;\n        }\n      }\n    }\n\n    .regenerate-btn {\n      font-size: 13px;\n      color: #5470c6;\n      padding: 4px 8px;\n      border-radius: 4px;\n      transition: all 0.3s ease;\n\n      &:hover {\n        background-color: #f0f7ff;\n        color: #4096ff;\n      }\n\n      i {\n        margin-right: 4px;\n        font-size: 12px;\n      }\n    }\n  }\n\n  .words-container {\n    text-align: center;\n\n    .generate-word-btn {\n      display: inline-flex;\n      align-items: center;\n      justify-content: center;\n      gap: 6px;\n      padding: 8px 16px;\n      background: #f0f7ff;\n      color: #5470c6;\n      border: 1px dashed #5470c6;\n      border-radius: 4px;\n      font-size: 14px;\n      cursor: pointer;\n      transition: all 0.3s ease;\n      margin-bottom: 12px;\n\n      &:hover {\n        background: #e6f4ff;\n        border-color: #4096ff;\n      }\n\n      i {\n        font-size: 12px;\n      }\n    }\n\n    .word-description {\n      font-size: 12px;\n      color: #999;\n      line-height: 1.5;\n    }\n  }\n}\n\n// 关键词文本框包装器\n.keywords-textbox-wrapper {\n  border: 1px solid #d9d9d9;\n  border-radius: 6px;\n  padding: 16px;\n  background: #fff;\n  min-height: 120px;\n  transition: border-color 0.3s ease, box-shadow 0.3s ease;\n\n  &:hover {\n    border-color: #5470c6;\n  }\n\n  &:focus-within {\n    border-color: #5470c6;\n    box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);\n  }\n}\n\n// 生成的关键词显示区域\n.generated-keywords-display {\n  margin-bottom: 16px;\n\n  .keyword-category {\n    display: flex;\n    align-items: flex-start;\n    gap: 16px;\n    margin-bottom: 16px;\n\n    &:last-child {\n      margin-bottom: 0;\n    }\n\n    .category-button {\n      min-width: 100px;\n      margin-right: 16px;\n      margin-bottom: 8px;\n      font-size: 13px;\n      border-radius: 16px;\n      transition: all 0.3s ease;\n\n      &:hover {\n        transform: translateY(-1px);\n        box-shadow: 0 2px 8px rgba(84, 112, 198, 0.3);\n      }\n\n      &:active {\n        transform: translateY(0);\n      }\n    }\n\n    .keyword-tags {\n      flex: 1;\n      display: flex;\n      flex-wrap: wrap;\n      gap: 8px;\n\n      .keyword-tag {\n        font-size: 13px;\n        padding: 6px 12px;\n        border-radius: 16px;\n        cursor: pointer;\n        transition: all 0.3s ease;\n        border: 1px solid #d9d9d9;\n        background: #fff;\n        color: #666;\n        display: inline-flex;\n        align-items: center;\n        justify-content: center;\n        text-align: center;\n        user-select: none;\n\n        &:hover {\n          border-color: #5470c6;\n          color: #5470c6;\n        }\n\n        &.selected {\n          background: #5470c6;\n          color: white;\n          border-color: #5470c6;\n        }\n      }\n    }\n  }\n}\n\n\n\n// 关键词选择区域\n.keywords-selection-section {\n  .keywords-grid {\n    display: flex;\n    flex-direction: column;\n    gap: 20px;\n  }\n\n  .keyword-category {\n    display: flex;\n    align-items: flex-start;\n    gap: 16px;\n\n    .category-label {\n      font-size: 14px;\n      color: #333;\n      font-weight: 500;\n      min-width: 80px;\n      padding-top: 6px;\n    }\n\n    .keyword-tags {\n      flex: 1;\n      display: flex;\n      flex-wrap: wrap;\n      gap: 8px;\n\n      .keyword-tag {\n        font-size: 13px;\n        padding: 6px 12px;\n        border-radius: 16px;\n        cursor: pointer;\n        transition: all 0.3s ease;\n        border: 1px solid #d9d9d9;\n        background: #fff;\n        color: #666;\n        display: inline-flex;\n        align-items: center;\n        justify-content: center;\n        text-align: center;\n        user-select: none;\n\n        &:hover {\n          border-color: #5470c6;\n          color: #5470c6;\n        }\n\n        &.selected {\n          background: #5470c6;\n          color: white;\n          border-color: #5470c6;\n        }\n\n        &.highlight {\n          background: #333;\n          color: white;\n          border-color: #333;\n          position: relative;\n          cursor: default;\n\n          &::after {\n            content: '';\n            position: absolute;\n            bottom: -8px;\n            left: 50%;\n            transform: translateX(-50%);\n            width: 0;\n            height: 0;\n            border-left: 6px solid transparent;\n            border-right: 6px solid transparent;\n            border-top: 6px solid #333;\n          }\n        }\n      }\n    }\n  }\n}\n\n// 第二步：数据概览样式\n.data-overview {\n  background: white;\n  border-radius: 8px;\n  padding: 32px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n  margin-bottom: 32px;\n\n  .section-title {\n    font-size: 18px;\n    font-weight: 600;\n    color: #333;\n    margin: 0 0 24px 0;\n  }\n}\n\n.data-source-section {\n  .source-option {\n    display: flex;\n    align-items: flex-start;\n    gap: 16px;\n    padding: 20px;\n    border: 1px solid #e8e8e8;\n    border-radius: 8px;\n    margin-bottom: 16px;\n    cursor: pointer;\n    transition: all 0.3s ease;\n\n    &:hover {\n      border-color: #5470c6;\n      background: #f8f9fa;\n    }\n\n    .source-checkbox {\n      :deep(.el-checkbox__input) {\n        .el-checkbox__inner {\n          width: 18px;\n          height: 18px;\n          border-radius: 4px;\n          border: 2px solid #d9d9d9;\n\n          &::after {\n            width: 5px;\n            height: 9px;\n            left: 5px;\n            top: 1px;\n          }\n        }\n\n        &.is-checked .el-checkbox__inner {\n          background-color: #5470c6;\n          border-color: #5470c6;\n        }\n      }\n    }\n\n    .source-icon {\n      width: 40px;\n      height: 40px;\n      background: #f0f7ff;\n      border-radius: 8px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n\n      i {\n        font-size: 20px;\n        color: #5470c6;\n      }\n    }\n\n    .source-content {\n      flex: 1;\n\n      h3 {\n        font-size: 16px;\n        font-weight: 500;\n        color: #333;\n        margin: 0 0 4px 0;\n        word-break: break-all;\n        line-height: 1.4;\n      }\n\n      .source-description {\n        margin: 0;\n        font-size: 14px;\n        color: #666;\n        line-height: 1.4;\n      }\n\n      .source-count {\n        color: #409eff;\n        font-weight: 500;\n      }\n    }\n\n    .source-actions {\n      display: flex;\n      align-items: center;\n\n      .el-icon-delete {\n        font-size: 16px;\n        color: #999;\n        cursor: pointer;\n        transition: color 0.3s ease;\n\n        &:hover {\n          color: #ff4d4f;\n        }\n      }\n    }\n  }\n\n  .add-source-btn {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    gap: 8px;\n    padding: 16px;\n    border: 2px dashed #d9d9d9;\n    border-radius: 8px;\n    color: #999;\n    cursor: pointer;\n    transition: all 0.3s ease;\n    font-size: 14px;\n\n    &:hover {\n      border-color: #5470c6;\n      color: #5470c6;\n      background: #f8f9fa;\n    }\n\n    i {\n      font-size: 16px;\n    }\n  }\n}\n\n// 底部按钮区域\n.bottom-actions {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  gap: 16px;\n  padding-top: 24px;\n\n  .el-button {\n    padding: 12px 32px;\n    font-size: 16px;\n\n    &:disabled {\n      background-color: #f5f5f5;\n      border-color: #d9d9d9;\n      color: #bfbfbf;\n      cursor: not-allowed;\n    }\n  }\n}\n\n// 新增数据源表单样式\n.add-source-form {\n  background: white;\n  border: 1px solid #e8e8e8;\n  border-radius: 8px;\n  padding: 20px;\n  margin-top: 16px;\n\n  .form-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 20px;\n    padding-bottom: 12px;\n    border-bottom: 1px solid #f0f0f0;\n\n    h3 {\n      font-size: 16px;\n      font-weight: 600;\n      color: #333;\n      margin: 0;\n    }\n\n    .el-icon-close {\n      font-size: 18px;\n      color: #999;\n      cursor: pointer;\n      transition: color 0.3s ease;\n\n      &:hover {\n        color: #666;\n      }\n    }\n  }\n\n  .form-item {\n    .form-label {\n      display: block;\n      font-size: 14px;\n      color: #333;\n      font-weight: 500;\n      margin-bottom: 8px;\n\n      .required {\n        color: #ff4d4f;\n        margin-left: 2px;\n      }\n    }\n\n    .input-group {\n      display: flex;\n      gap: 12px;\n      align-items: flex-start;\n\n      .source-url-input {\n        flex: 1;\n\n        :deep(.el-input__inner) {\n          border-radius: 6px;\n          border: 1px solid #d9d9d9;\n          padding: 12px 16px;\n          font-size: 14px;\n\n          &::placeholder {\n            color: #bfbfbf;\n          }\n\n          &:focus {\n            border-color: #5470c6;\n            box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);\n          }\n        }\n      }\n\n      .el-button {\n        padding: 12px 24px;\n        font-size: 14px;\n        border-radius: 6px;\n        white-space: nowrap;\n      }\n    }\n  }\n}\n\n// 响应式设计\n@media (max-width: 768px) {\n  .steps-container {\n    padding: 16px 12px;\n    flex-direction: column;\n    gap: 16px;\n\n    .left-actions {\n      align-self: flex-start;\n\n      .timed-push-btn {\n        font-size: 13px;\n        padding: 6px 12px;\n      }\n    }\n\n    .steps-wrapper {\n      gap: 30px;\n    }\n\n    .right-placeholder {\n      display: none;\n    }\n\n    .step-item {\n      font-size: 13px;\n    }\n  }\n\n  .main-content {\n    padding: 24px 16px;\n  }\n\n  .analysis-source {\n    padding: 24px 20px;\n  }\n\n  .document-content {\n    padding: 12px;\n    min-height: 100px;\n  }\n}\n\n// 定时任务抽屉样式\n.timed-task-drawer {\n  :deep(.el-drawer__header) {\n    padding: 20px 24px 16px;\n    border-bottom: 1px solid #f0f0f0;\n    margin-bottom: 0;\n  }\n\n  :deep(.el-drawer__body) {\n    padding: 0;\n  }\n}\n\n.drawer-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n\n  .drawer-title {\n    font-size: 18px;\n    font-weight: 600;\n    color: #333;\n  }\n\n  .add-task-btn {\n    font-size: 14px;\n    padding: 6px 12px;\n    border-radius: 4px;\n\n    .el-icon-plus {\n      margin-right: 4px;\n    }\n  }\n}\n\n.drawer-content {\n  padding: 24px;\n  min-height: 400px;\n}\n\n.empty-state {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 400px;\n}\n\n.empty-content {\n  text-align: center;\n\n  .empty-icon {\n    margin-bottom: 24px;\n    display: flex;\n    justify-content: center;\n\n    svg {\n      opacity: 0.6;\n    }\n  }\n\n  .empty-text {\n    font-size: 16px;\n    color: #909399;\n    margin: 0 0 24px 0;\n    font-weight: 500;\n  }\n\n  .create-btn {\n    padding: 10px 24px;\n    font-size: 14px;\n    border-radius: 6px;\n    font-weight: 500;\n  }\n}\n\n// 创建任务弹窗样式\n.create-task-dialog {\n  :deep(.el-dialog) {\n    border-radius: 8px;\n  }\n\n  :deep(.el-dialog__header) {\n    padding: 20px 24px 16px;\n    border-bottom: 1px solid #f0f0f0;\n  }\n\n  :deep(.el-dialog__body) {\n    padding: 24px;\n  }\n\n  :deep(.el-dialog__footer) {\n    padding: 16px 24px 24px;\n    border-top: 1px solid #f0f0f0;\n  }\n}\n\n.task-form {\n  .task-requirement-section {\n    margin-bottom: 24px;\n\n    .section-label {\n      font-size: 14px;\n      font-weight: 500;\n      color: #333;\n      margin-bottom: 16px;\n\n      .required {\n        color: #ff4d4f;\n        margin-left: 2px;\n      }\n    }\n\n    .form-group {\n      margin-bottom: 16px;\n\n      &:last-child {\n        margin-bottom: 0;\n      }\n\n      .input-label {\n        font-size: 13px;\n        color: #666;\n        margin-bottom: 6px;\n        font-weight: 500;\n      }\n\n      .task-name-input {\n        :deep(.el-input__inner) {\n          border-radius: 6px;\n          border: 1px solid #d9d9d9;\n          padding: 10px 12px;\n          font-size: 14px;\n\n          &::placeholder {\n            color: #bfbfbf;\n          }\n\n          &:focus {\n            border-color: #5470c6;\n            box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);\n          }\n        }\n      }\n\n      .task-description-input {\n        :deep(.el-textarea__inner) {\n          border-radius: 6px;\n          border: 1px solid #d9d9d9;\n          padding: 10px 12px;\n          font-size: 14px;\n          line-height: 1.5;\n          resize: vertical;\n\n          &::placeholder {\n            color: #bfbfbf;\n          }\n\n          &:focus {\n            border-color: #5470c6;\n            box-shadow: 0 0 0 2px rgba(84, 112, 198, 0.1);\n          }\n        }\n      }\n    }\n  }\n\n  .execute-time-section {\n    .section-label {\n      font-size: 14px;\n      font-weight: 500;\n      color: #333;\n      margin-bottom: 12px;\n    }\n\n    .time-selector {\n      display: flex;\n      gap: 12px;\n      align-items: center;\n\n      .frequency-select {\n        width: 120px;\n      }\n\n      .time-picker {\n        width: 140px;\n      }\n\n      .datetime-picker {\n        width: 200px;\n      }\n    }\n  }\n}\n\n.dialog-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 12px;\n\n  .modify-btn {\n    color: #666;\n    border-color: #d9d9d9;\n\n    &:hover {\n      color: #5470c6;\n      border-color: #5470c6;\n    }\n  }\n\n  .run-btn {\n    background: #5470c6;\n    border-color: #5470c6;\n\n    &:hover {\n      background: #4096ff;\n      border-color: #4096ff;\n    }\n  }\n\n  .save-btn {\n    background: #52c41a;\n    border-color: #52c41a;\n\n    &:hover {\n      background: #73d13d;\n      border-color: #73d13d;\n    }\n  }\n}\n\n// 自定义数据源管理区域样式\n.custom-sources-management {\n  margin-top: 16px;\n  padding: 16px;\n  background: #f8f9fa;\n  border-radius: 8px;\n  border: 1px solid #e9ecef;\n\n  .management-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 16px;\n\n    h4 {\n      margin: 0;\n      font-size: 14px;\n      font-weight: 600;\n      color: #333;\n    }\n  }\n\n  .existing-sources {\n    .source-item {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      padding: 12px;\n      background: white;\n      border: 1px solid #e8e8e8;\n      border-radius: 6px;\n      margin-bottom: 8px;\n\n      .source-info {\n        display: flex;\n        align-items: center;\n        gap: 8px;\n        flex: 1;\n\n        i {\n          color: #409eff;\n        }\n\n        .source-name {\n          font-weight: 500;\n          color: #333;\n        }\n\n        .source-url {\n          color: #666;\n          font-size: 12px;\n          margin-left: 8px;\n        }\n      }\n\n      .source-actions {\n        i {\n          cursor: pointer;\n          color: #f56c6c;\n          font-size: 16px;\n          padding: 4px;\n\n          &:hover {\n            color: #f78989;\n          }\n        }\n      }\n    }\n  }\n\n  .no-sources {\n    text-align: center;\n    padding: 20px;\n    color: #999;\n    font-size: 14px;\n  }\n}\n\n// 数据源列表区域样式\n.data-source-list-section {\n  margin-top: 16px;\n  padding: 16px;\n  background: #f8f9fa;\n  border-radius: 8px;\n  border: 1px solid #e9ecef;\n\n  .list-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 16px;\n\n    h4 {\n      margin: 0;\n      font-size: 14px;\n      font-weight: 600;\n      color: #333;\n    }\n\n    .header-controls {\n      display: flex;\n      gap: 8px;\n      align-items: center;\n    }\n  }\n\n  .data-source-table {\n    background: white;\n    border-radius: 6px;\n    overflow: hidden;\n\n    .url-cell {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n\n      i {\n        color: #409eff;\n        font-size: 14px;\n        flex-shrink: 0;\n      }\n\n      .url-text {\n        color: #333;\n        font-size: 13px;\n        flex: 1;\n      }\n    }\n\n    .pagination-wrapper {\n      padding: 16px;\n      text-align: right;\n      background: #fafafa;\n      border-top: 1px solid #e8e8e8;\n    }\n  }\n}\n\n// 任务列表样式（待实现）\n// .task-list {\n//   // TODO: 任务列表样式\n// }\n\n// 推送报告弹窗样式\n.form-tip {\n  margin-top: 8px;\n  font-size: 12px;\n  color: #909399;\n  line-height: 1.4;\n\n  i {\n    margin-right: 4px;\n    color: #409eff;\n  }\n}\n\n.dialog-footer {\n  text-align: right;\n\n  .el-button {\n    margin-left: 8px;\n  }\n}\n\n// 推送按钮图标样式\n.el-button .el-icon-s-promotion {\n  margin-right: 6px;\n}\n\n// 任务预览弹窗样式\n.task-preview-dialog {\n  :deep(.el-dialog) {\n    border-radius: 8px;\n  }\n\n  :deep(.el-dialog__header) {\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    color: white;\n    padding: 20px 24px;\n    border-radius: 8px 8px 0 0;\n\n    .el-dialog__title {\n      color: white;\n      font-weight: 600;\n      font-size: 16px;\n    }\n\n    .el-dialog__close {\n      color: white;\n      font-size: 18px;\n\n      &:hover {\n        color: #f0f0f0;\n      }\n    }\n  }\n\n  :deep(.el-dialog__body) {\n    padding: 24px;\n    max-height: 60vh;\n    overflow-y: auto;\n  }\n\n  :deep(.el-dialog__footer) {\n    padding: 16px 24px 24px;\n    border-top: 1px solid #f0f0f0;\n  }\n}\n\n.task-preview-content {\n  .preview-section {\n    margin-bottom: 24px;\n\n    &:last-child {\n      margin-bottom: 0;\n    }\n\n    .section-title {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      font-size: 16px;\n      font-weight: 600;\n      color: #333;\n      margin-bottom: 16px;\n      padding-bottom: 8px;\n      border-bottom: 2px solid #f0f0f0;\n\n      i {\n        color: #667eea;\n        font-size: 18px;\n      }\n    }\n\n    .info-grid {\n      display: grid;\n      grid-template-columns: 1fr 1fr;\n      gap: 16px;\n\n      @media (max-width: 768px) {\n        grid-template-columns: 1fr;\n      }\n    }\n\n    .info-item {\n      display: flex;\n      align-items: flex-start;\n      gap: 8px;\n\n      label {\n        font-weight: 600;\n        color: #666;\n        min-width: 80px;\n        flex-shrink: 0;\n      }\n\n      span {\n        color: #333;\n        word-break: break-all;\n      }\n\n      .el-tag {\n        margin: 0;\n      }\n    }\n\n    .description-content {\n      background: #f8f9fa;\n      border: 1px solid #e9ecef;\n      border-radius: 6px;\n      padding: 16px;\n      color: #333;\n      line-height: 1.6;\n      white-space: pre-wrap;\n    }\n\n    .push-config {\n      .url-display {\n        display: flex;\n        align-items: center;\n        gap: 8px;\n        background: #f8f9fa;\n        border: 1px solid #e9ecef;\n        border-radius: 6px;\n        padding: 12px;\n\n        .url-text {\n          flex: 1;\n          color: #333;\n          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;\n          font-size: 13px;\n          word-break: break-all;\n        }\n\n        .el-button {\n          padding: 4px 8px;\n\n          i {\n            color: #667eea;\n          }\n\n          &:hover i {\n            color: #5a67d8;\n          }\n        }\n      }\n    }\n  }\n}\n\n/* ==================== 分析进度页面样式 ==================== */\n.analysis-progress {\n  .progress-overview {\n    margin-bottom: 24px;\n\n    .status-card {\n      background: #fff;\n      border-radius: 8px;\n      padding: 24px;\n      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n\n      .status-header {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        margin-bottom: 16px;\n\n        h3 {\n          margin: 0;\n          font-size: 16px;\n          font-weight: 600;\n        }\n\n        .status-indicator {\n          display: flex;\n          align-items: center;\n          gap: 8px;\n\n          .status-dot {\n            width: 8px;\n            height: 8px;\n            border-radius: 50%;\n            background: #ccc;\n\n            &.running {\n              background: #1890ff;\n              animation: pulse 1.5s infinite;\n            }\n\n            &.completed {\n              background: #52c41a;\n            }\n\n            &.failed {\n              background: #ff4d4f;\n            }\n          }\n\n          .status-text {\n            font-size: 14px;\n            color: #666;\n          }\n        }\n\n        &.running .status-text {\n          color: #1890ff;\n        }\n\n        &.completed .status-text {\n          color: #52c41a;\n        }\n\n        &.failed .status-text {\n          color: #ff4d4f;\n        }\n      }\n\n      .progress-bar-container {\n        display: flex;\n        align-items: center;\n        gap: 12px;\n\n        .progress-bar {\n          flex: 1;\n          height: 8px;\n          background: #f5f5f5;\n          border-radius: 4px;\n          overflow: hidden;\n\n          .progress-fill {\n            height: 100%;\n            background: linear-gradient(90deg, #1890ff, #40a9ff);\n            border-radius: 4px;\n            transition: width 0.3s ease;\n          }\n        }\n\n        .progress-text {\n          font-size: 14px;\n          font-weight: 600;\n          color: #1890ff;\n          min-width: 40px;\n        }\n      }\n    }\n  }\n\n  .real-time-logs {\n    .logs-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 16px;\n\n      h3 {\n        margin: 0;\n        font-size: 16px;\n        font-weight: 600;\n      }\n\n      .logs-controls {\n        display: flex;\n        gap: 8px;\n      }\n    }\n\n    .logs-container {\n      background: #1e1e1e;\n      border-radius: 8px;\n      padding: 16px;\n      height: 400px;\n      overflow-y: auto;\n      font-family: 'Consolas', 'Monaco', 'Courier New', monospace;\n\n      .no-logs {\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        justify-content: center;\n        height: 100%;\n        color: #888;\n\n        i {\n          font-size: 24px;\n          margin-bottom: 8px;\n        }\n      }\n\n      .logs-list {\n        .log-item {\n          display: flex;\n          gap: 12px;\n          margin-bottom: 8px;\n          font-size: 13px;\n          line-height: 1.4;\n\n          .log-time {\n            color: #888;\n            min-width: 80px;\n          }\n\n          .log-level {\n            min-width: 60px;\n            font-weight: 600;\n          }\n\n          .log-message {\n            flex: 1;\n            color: #fff;\n          }\n\n          &.info .log-level {\n            color: #40a9ff;\n          }\n\n          &.success .log-level {\n            color: #52c41a;\n          }\n\n          &.warning .log-level {\n            color: #faad14;\n          }\n\n          &.error .log-level {\n            color: #ff4d4f;\n          }\n        }\n      }\n\n      /* 滚动条样式 */\n      &::-webkit-scrollbar {\n        width: 6px;\n      }\n\n      &::-webkit-scrollbar-track {\n        background: #2a2a2a;\n        border-radius: 3px;\n      }\n\n      &::-webkit-scrollbar-thumb {\n        background: #555;\n        border-radius: 3px;\n\n        &:hover {\n          background: #777;\n        }\n      }\n    }\n  }\n\n  .analysis-completed,\n  .analysis-failed {\n    text-align: center;\n    margin-top: 24px;\n    padding: 24px;\n    background: #fff;\n    border-radius: 8px;\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n\n    .completion-message,\n    .failure-message {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: 8px;\n      margin-bottom: 16px;\n      font-size: 16px;\n      font-weight: 600;\n\n      i {\n        font-size: 20px;\n      }\n    }\n\n    .completion-message {\n      color: #52c41a;\n    }\n\n    .failure-message {\n      color: #ff4d4f;\n    }\n  }\n}\n\n@keyframes pulse {\n  0% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.5;\n  }\n  100% {\n    opacity: 1;\n  }\n}\n\n// 模板弹窗样式\n.template-dialog {\n  :deep(.el-dialog) {\n    border-radius: 8px;\n  }\n\n  :deep(.el-dialog__header) {\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    color: white;\n    padding: 20px 24px;\n    border-radius: 8px 8px 0 0;\n\n    .el-dialog__title {\n      color: white;\n      font-weight: 600;\n      font-size: 16px;\n    }\n\n    .el-dialog__close {\n      color: white;\n      font-size: 18px;\n\n      &:hover {\n        color: #f0f0f0;\n      }\n    }\n  }\n\n  :deep(.el-dialog__body) {\n    padding: 0;\n    max-height: 60vh;\n    overflow-y: auto;\n  }\n\n  :deep(.el-dialog__footer) {\n    padding: 16px 24px 24px;\n    border-top: 1px solid #f0f0f0;\n  }\n}\n\n.template-content {\n  .template-list {\n    padding: 24px;\n  }\n\n  .template-item {\n    border: 2px solid #e8e8e8;\n    border-radius: 8px;\n    padding: 20px;\n    margin-bottom: 16px;\n    cursor: pointer;\n    transition: all 0.3s ease;\n\n    &:hover {\n      border-color: #409eff;\n      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);\n    }\n\n    &.selected {\n      border-color: #409eff;\n      background-color: #f0f7ff;\n      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);\n    }\n\n    &:last-child {\n      margin-bottom: 0;\n    }\n\n    .template-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 12px;\n\n      .template-title {\n        display: flex;\n        align-items: center;\n        font-size: 16px;\n        font-weight: 600;\n        color: #333;\n\n        i {\n          margin-right: 8px;\n          color: #409eff;\n        }\n      }\n\n      .template-category {\n        background: #409eff;\n        color: white;\n        padding: 4px 8px;\n        border-radius: 4px;\n        font-size: 12px;\n      }\n    }\n\n    .template-details {\n      .template-field {\n        margin-bottom: 8px;\n\n        &:last-child {\n          margin-bottom: 0;\n        }\n\n        label {\n          font-weight: 600;\n          color: #666;\n          margin-right: 8px;\n        }\n\n        span {\n          color: #333;\n        }\n\n        p {\n          margin: 4px 0 0 0;\n          color: #666;\n          line-height: 1.5;\n          font-size: 14px;\n        }\n      }\n    }\n  }\n}\n\n// 步骤切换下拉菜单样式\n.el-dropdown-menu {\n  .el-dropdown-menu__item {\n    padding: 8px 16px;\n    font-size: 13px;\n    line-height: 1.4;\n\n    i {\n      margin-right: 8px;\n      color: #666;\n    }\n\n    &.is-active {\n      background-color: #f0f7ff;\n      color: #409eff;\n      font-weight: 500;\n\n      i {\n        color: #409eff;\n      }\n    }\n\n    &:hover {\n      background-color: #f5f7fa;\n    }\n\n    &.is-active:hover {\n      background-color: #e6f4ff;\n    }\n  }\n}\n</style>\n"]}]}