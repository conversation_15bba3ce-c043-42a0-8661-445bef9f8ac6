{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue?vue&type=template&id=040a21b8&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\src\\views\\opinion-analysis\\index.vue", "mtime": 1753695251423}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753065270161}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753065270161}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1753065271554}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753065273026}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753065270161}, {"path": "C:\\Users\\<USER>\\Desktop\\thin\\thinktankui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753065272328}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}